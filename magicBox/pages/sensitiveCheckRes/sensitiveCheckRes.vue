<template>
	<view class="padding-bottom-60">
		<view class="check-res">
			<view class="flex al-center jc-between padding-lr-20" style="padding-top: 20rpx;">
				标记说明
			</view>
			<view class="flex al-center padding-20 color-white" style="gap: 20rpx;font-size: 28rpx;">
				<view class="green">
					广告违禁词
				</view>
				<view class="blue">
					美妆违禁词
				</view>
				<view class="red">
					直播间违禁词
				</view>
				<view class="yellow">
					常见违禁词
				</view>
			</view>

			<textareaBox height="400" v-model="value" placeholder="违禁词检测" count maxlength="1000"></textareaBox>

			<view class="flex flex-wrap gap-10  padding-20">

				<view class="flex1">
					<u-button type="primary" plain @click="togglePy">一键替换为拼音</u-button>
				</view>
				<view class="flex1">
					<u-button type="primary" @click="copyText">复制</u-button>
				</view>
			</view>

			<view class="flex flex-wrap gap-10  padding-20">
				<view class="flex1">
					<u-button type="primary" plain>文案配音</u-button>
				</view>
				<view class="flex1">
					<u-button type="primary" plain>AI写爆款</u-button>
				</view>
				<view class="flex1">
					<u-button type="primary" plain>文案专业评测</u-button>
				</view>
			</view>


		</view>

		<view class="m-top-40">
			<share></share>
		</view>
	</view>

</template>

<script>
	import {
		copyText
	} from '@/utils/index.js';
	export default {
		data() {
			return {
				value: '',
				createNum: 87.6,
				showPY: false,

			}
		},
		onLoad(p) {
			if (p.id) {
				uni.setNavigationBarTitle({
					title: '文案改写详情'
				})
			} else {
				//识别进来
				console.log('识别进来');
			}

		},
		methods: {
			togglePy() {
				this.showPY = !this.showPY
			},
			goPage() {
				uni.navigateTo({
					url: "/pages/rewriteTextHis/"
				})
			},
			clearContent() {
				this.value = ''
			},
			copyText() {
				copyText(this.value)
			}

		}
	}
</script>

<style lang="scss" scoped>
	.check-res {
		.green {
			background-color: rgb(0, 226, 102);
			padding: 6rpx;
		}

		.blue {
			background-color: rgb(117, 249, 235);
			padding: 6rpx;
		}

		.red {
			background-color: rgb(254, 74, 101);
			padding: 6rpx;
		}

		.yellow {
			background-color: rgb(252, 202, 0);
			padding: 6rpx;
		}

	}
</style>