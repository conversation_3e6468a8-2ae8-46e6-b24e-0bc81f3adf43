<template>
	<view class="container">
		<u-overlay :show="show" @click="show = false">
			<view style="display: flex;justify-content: center;align-items: center;height: 100%;">
				<view class="">
					<image src="/static/c.png" style="width: 80vw;border-radius: 40rpx;" mode="widthFix"></image>
					<view
						style="width: 200rpx;display: flex;margin-left:50%;transform: translateX(-50%);margin-top: 20rpx;">
						<u-button type="primary" shape="circle" @click="showTip=false">我知道了</u-button>
					</view>

				</view>
			</view>
		</u-overlay>

		<!-- 顶部提示 -->
		<view class="top-tip" @click="collect" v-if="showTip">
			<view class="flex al-center">
				<u-icon name="info-circle" color="#FE4A65" bold size="18"></u-icon>
				<text class="tip-text">添加小程序到桌面，方便下次使用！</text>
			</view>
			<view class="ok-button">OK</view>
		</view>
		<view class="" style="height: 10rpx;">

		</view>
		<view class="conntent">
			<!-- 轮播图 -->
			<swiper class="swiper" autoplay interval="3000" duration="500" circular>
				<swiper-item v-for="(item,i) in swiperList" :key="i">
					<image class="swiper-image" :src="item" mode="aspectFill"></image>
				</swiper-item>
			</swiper>

			<!-- 文案/音视频处理 -->
			<view class="menu-box">
				<view class="title">
					素材处理 · 解放生产力
				</view>
				<view class="section exclusive-section shadow">
					<view class="grid">
						<view class="grid-item" v-for="(item, index) in topPart" :key="item.key"
							@click="goPage(item.key)">
							<image class="grid-icon" src="/static/logo.png" mode="aspectFit"></image>
							<text class="grid-text">{{ item.name }}</text>
						</view>
					</view>
				</view>

			</view>


			<!-- 创作爆款 -->
			<view class="menu-box">
				<view class="title">
					创作爆款、账号运营、MCN内部专用 · 独家AI算法
				</view>
				<view class="section exclusive-section shadow">
					<view class="grid">
						<view class="grid-item" v-for="(item, index) in bottomPart" :key="item.key"
							@click="goPage(item.key)">
							<image class="grid-icon" src="/static/logo.png" mode="aspectFit"></image>
							<text class="grid-text">{{ item.name }}</text>
						</view>
					</view>
				</view>

			</view>
		</view>


	</view>
</template>

<script>
	import {
		topPart,
		bottomPart
	} from '@/config/menu';
	export default {
		data() {
			return {
				showTip: true,
				show: false,
				swiperList: [
					'https://tse1-mm.cn.bing.net/th/id/OIP-C.mH9YLFEL5YdVxJM82mjVJQHaEo?w=300&h=187&c=7&r=0&o=7&dpr=1.5&pid=1.7&rm=3',
					'https://tse4-mm.cn.bing.net/th/id/OIP-C.MmmDZ6UOCwqEMuP1LAYo9QAAAA?w=256&h=180&c=7&r=0&o=7&dpr=1.5&pid=1.7&rm=3'
				],
				topPart: topPart,
				bottomPart: bottomPart,
			};
		},
		methods: {
			collect() {
				this.show = true
			},
			goPage(key) {
				uni.navigateTo({
					url: "/pages/module/module" + "?key=" + key
				})
			}
		},
	};
</script>

<style scoped lang="scss">
	.container {
		background-color: $uni-color-primary;
		height: 100vh;

		.conntent {
			height: 100vh;
			overflow-y: auto;
			padding: 20rpx;
			background: #fff;
			border-radius: 40rpx 40rpx 0 0;
		}
	}

	/* 顶部提示 - 使用主题色 #FE4A65 */
	.top-tip {
		display: flex;
		align-items: center;
		justify-content: space-between;
		background: #fff;
		color: $uni-color-primary;
		padding: 10rpx 30rpx;
		border-radius: 12rpx;
		margin: 0 30rpx;
		margin-bottom: 8rpx;
		box-shadow: 0 8rpx 24rpx rgba(254, 74, 101, 0.1);

	}

	.tip-text {
		color: $uni-text-color;
		font-size: 26rpx;
		margin-left: 8rpx;
	}

	.ok-button {
		background-color: $uni-color-primary;
		color: #fff;
		border: none;
		padding: 10rpx 30rpx;
		border-radius: 1000px;
		font-size: 24rpx;
		margin-left: 20rpx;
	}

	/* 轮播图样式 */
	.swiper {
		height: 160rpx;
		border-radius: 20rpx;
		overflow: hidden;
		margin-bottom: 40rpx;
	}

	.swiper-image {
		width: 100%;
		height: 100%;
	}

	/* 通用部分样式 */
	.menu-box {

		.title {
			font-weight: 28rpx;
			color: $uni-text-info;
			margin-bottom: 10rpx;
		}

		.section {
			background-color: white;
			border-radius: 24rpx;
			padding: 30rpx 0;
			margin-bottom: 40rpx;
			// box-shadow: 0pt 4rpx 12rpx 0 rgba(0, 0, 0, 0.4);
		}
	}



	/* 网格布局 - 改为每行4个 */
	.grid {
		display: flex;
		flex-wrap: wrap;
		justify-content: flex-start;
	}

	.grid-item {
		width: 25%;
		text-align: center;
		padding-bottom: 30rpx;
	}

	.grid-item:nth-child(4n) {
		margin-right: 0;
		/* 每行第4个item右边距为0 */
	}

	.grid-icon {
		width: 80rpx;
		height: 80rpx;
		margin: 0 auto 10rpx;
	}

	.grid-text {
		font-size: 24rpx;
		color: $uni-text-color;
		display: block;
		line-height: 1.4;
	}

	/* 独家部分特殊样式 - 使用主题色作为标题颜色 */
	.exclusive-section {
		/* background: linear-gradient(135deg, #f5f7fa 0%, #e4e8eb 100%); */
		/* border: 1px solid #e1e5e9; */
	}

	.exclusive-section .section-title {
		color: #000;
	}

	.exclusive-section .grid-text {
		font-weight: 500;
	}
</style>