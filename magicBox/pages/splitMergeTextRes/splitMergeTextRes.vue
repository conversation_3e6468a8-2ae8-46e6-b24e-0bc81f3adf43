<template>
	<view class="padding-bottom-60">
		<view class="rewrite-res ">
			<view class="flex al-center jc-between padding-lr-20" style="padding-top: 20rpx;">
				<view class="flex al-center ">
					<view class="m-right-20 bold">
						<u-alert showIcon type="success" title="以下是拆分重组后生成的新文案!" fontSize="12"></u-alert>
					</view>
					<!-- <u-switch v-model="showChange" size="20" activeColor="#4cd964"></u-switch> -->
				</view>
				<!-- <view class="font-color-info font-size-24" @click="goPage">
					历史记录>
				</view> -->
			</view>
			<view class="flex al-center  " style="gap: 20rpx;font-size: 28rpx;">
				<u-tabs :list="textList" keyName="title" lineWidth="50" :current="curTabIndex" lineColor="#7A60FF"
					@change="tabChange" :activeStyle="{
            color: '#7A60FF',
        }"></u-tabs>
			</view>

			<textareaBox height="300" v-model="curText" placeholder="文案内容" count maxlength="1000">
			</textareaBox>

			<view class="flex flex-wrap gap-10  padding-20">
				<view class="flex1">
					<u-button type="primary" plain>AI写爆款</u-button>
				</view>
				<view class="flex1">
					<u-button type="primary" plain>重新生成</u-button>
				</view>
				<view class="flex1">
					<u-button type="primary" @click="copyText">复制</u-button>
				</view>
			</view>

			<view class="flex flex-wrap gap-10 padding-20">
				<view class="flex1">
					<u-button type="primary" plain>文案配音</u-button>
				</view>
				<view class="flex1">
					<u-button type="primary" plain>违禁词检测</u-button>
				</view>
				<view class="flex1">
					<u-button type="primary" plain>文案专业评测</u-button>
				</view>
			</view>


		</view>

		<view class="m-top-40">
			<share></share>
		</view>
	</view>

</template>

<script>
	import {
		copyText
	} from '@/utils/index.js';
	export default {
		data() {
			return {
				curText: '',
				curTabIndex: 0,
				textList: [{
					title: '文案一',
					text: '文案一内容',
				}, {
					title: '文案二',
					text: '文案二内容',
				}, {
					title: '文案三',
					text: '文案三内容',
				}],
				value: '',
				showChange: false,

			}
		},
		onLoad(p) {
			if (p.id) {
				uni.setNavigationBarTitle({
					title: '文案重组详情'
				})
			} else {
				//识别进来
				console.log('识别进来');
			}

			this.curText = this.textList[0].text


		},
		methods: {
			tabChange(e) {
				this.curTabIndex = e.index
				this.curText = e.text
			},
			goPage() {
				uni.navigateTo({
					url: "/pages/splitMergeTextHis/splitMergeTextHis"
				})
			},
			clearContent() {
				this.value = ''
			},
			copyText() {
				copyText(this.value)
			}

		}
	}
</script>

<style lang="scss" scoped>
	.rewrite-res {}
</style>