<template>
	<view class="container">
		<view class="font-color-info">
			最长录制时间为10分钟
		</view>
		<view class="timer m-top-20">{{ currentTime }}</view>
		<view v-if="status==1" :class="status==1?'green':'yellow'">{{ status==1?"录制中...":"已暂停" }}</view>

		<template v-if="status==0">
			<view class="icon" @click="actionOp(0)">
				<view class="icon-box">
					<u-icon name="mic" size="40" color="#fff"></u-icon>
				</view>
				<view class="icon-text">
					开始录制
				</view>
			</view>
		</template>

		<template v-if="status==1">
			<view class="icon" @click="actionOp(1)">
				<view class="icon-box" style="background: orangered;">
					<u-icon name="pause" size="40" color="#fff"></u-icon>
				</view>
				<view class="icon-text">
					暂停
				</view>
			</view>
		</template>

		<template v-if="status==2">
			<view class="icon" @click="actionOp(2)">
				<view class="icon-box bg-green">
					<u-icon name="play-right-fill" size="40" color="#fff"></u-icon>
				</view>
				<view class="icon-text">
					继续
				</view>
			</view>
		</template>

		<template v-if="status==3">
			<view class="icon" @click="actionOp(3)">
				<view class="icon-box">
					<u-icon name="mic" size="40" color="#fff"></u-icon>
				</view>
				<view class="icon-text">
					重新录制
				</view>
			</view>
		</template>

		<view class="btns" v-if="status==2 || status==3">
			<view class="btn" @click="playRecord">
				<u-button type="primary">试听录音</u-button>
			</view>
			<view class="btn">
				<u-button type="primary" @click="convertText">转文字</u-button>
			</view>
		</view>



		<!-- 音频播放弹窗 -->
		<u-modal :show="showPlayModal" :show-cancel-button="true" :show-confirm-button="true" width="600rpx"
			confirm-text="关闭试听" cancelText="保存录音" @cancel="saveRecord" title="试听录音" @confirm="closeLis">
			<view class="audio-player">
				<view class="audio-controls">
					<u-icon :name="isPlaying ? 'pause-circle' : 'play-circle'" size="60" color="#2979ff"
						@click="togglePlay"></u-icon>
					<slider :value="currentProgress" :max="duration" block-size="16" @changing="onSliderChanging"
						@change="onSliderChange" activeColor="#2979ff" class="slider" backgroundColor="#eee"></slider>
					<view class="time-display">
						<text>{{ formatTime(currentProgress) }}</text>
						<text>/</text>
						<text>{{ formatTime(duration) }}</text>
					</view>
				</view>
			</view>
			<view class="modal-footer" slot="footer">
				<u-button type="primary" @click="closePlayModal">关闭</u-button>
			</view>
		</u-modal>

		<!-- 转文字结果弹窗 -->
		<u-modal :show="showTextModal" title="语音转文字结果" :show-confirm-button="false" width="80%">
			<view class="text-result">
				<scroll-view scroll-y style="max-height: 300px;">
					<text>{{ convertedText || '转换中...' }}</text>
				</scroll-view>
			</view>
			<view class="modal-footer" slot="footer">
				<u-button type="primary" @click="showTextModal = false">关闭</u-button>
				<u-button type="success" @click="copyText" v-if="convertedText">复制文本</u-button>
			</view>
		</u-modal>
	</view>
</template>

<script>
	export default {
		data() {
			return {
				currentTime: '00:00',
				status: 0, // 0未开始 1录制中 2暂停 3停止
				recorderManager: null,
				timer: null,
				audioPath: '',
				recordedSeconds: 0,

				// 播放相关
				showPlayModal: false,
				isPlaying: false,
				duration: 0,
				currentProgress: 0,
				audioContext: null,
				progressInterval: null,

				// 转文字相关
				showTextModal: false,
				convertedText: ''
			};
		},
		onLoad() {
			this.initRecorder();
		},
		onUnload() {
			// this.closePlayModal();
			// if (this.recorderManager) {
			// 	this.recorderManager.stop();
			// }
		},
		destroyed() {
			this.closePlayModal();
			if (this.recorderManager) {
				this.recorderManager.stop();
			}
		},
		methods: {
			saveRecord() {
				uni.showToast({
					title: '录音已保存',
					icon: "none"
				})
			},
			closeLis() {
				this.showPlayModal = false
				this.audioContext.stop()
			},
			initRecorder() {
				this.recorderManager = uni.getRecorderManager();

				this.recorderManager.onStart(() => {
					console.log('recorder start');
					this.startTimer();
				});

				this.recorderManager.onPause(() => {
					console.log('recorder pause');
				});

				this.recorderManager.onResume(() => {
					console.log('recorder resume');
				});

				this.recorderManager.onStop((res) => {
					console.log('recorder stop', res);
					this.duration = Math.floor(res.duration / 1000)
					this.audioPath = res.tempFilePath;
					clearInterval(this.timer);

					if (this.status === 1) {
						this.status = 3;
					}
				});

				this.recorderManager.onError((res) => {
					console.log('recorder error', res);
					uni.showToast({
						title: '录音失败: ' + res.errMsg,
						icon: 'none'
					});
					this.status = 0;
					clearInterval(this.timer);
				});
			},

			actionOp(status) {
				if (status == 0) {
					this.startRecord()
				} else if (status == 1) {
					this.pauseRecord()
				} else if (status == 2) {
					this.resumeRecord()
				} else if (status == 3) {
					this.stopRecord();
					this.startRecord()
				}
			},

			startRecord() {
				this.status = 1;
				this.recordedSeconds = 0;
				this.currentTime = '00:00';
				this.audioPath = '';
				this.recorderManager.start({
					duration: 600000, // 10分钟
					sampleRate: 44100,
					numberOfChannels: 1,
					encodeBitRate: 192000,
					format: 'mp3',
				});
			},

			pauseRecord() {
				this.status = 2;
				this.recorderManager.pause();
				clearInterval(this.timer);
			},

			resumeRecord() {
				this.status = 1;
				this.recorderManager.resume();
				this.startTimer();
			},

			stopRecord() {
				this.status = 3;
				this.recorderManager.stop();
			},

			playRecord() {

				if (this.status == 3 && this.audioPath) {
					this.showPlayModal = true;
					this.$nextTick(() => {
						this.initAudioPlayer();
					});
				} else {
					uni.showModal({
						content: '试听后无法继续录制，是否结束录制并试听？',
						confirmText: '试听',
						cancelText: '继续录音',
						success: (res) => {
							if (res.confirm) {
								if (this.status === 1 || this.status === 2) {
									this.stopRecord();
								}
								this.showPlayModal = true;
								this.$nextTick(() => {
									this.initAudioPlayer();
								});
							}
						}
					});
				}


			},

			initAudioPlayer() {
				if (this.audioContext) {
					this.audioContext.destroy();
				}

				this.audioContext = uni.createInnerAudioContext();
				console.log(this.audioPath, 'this.audioPath');
				this.audioContext.src = this.audioPath;

				this.audioContext.onPlay(() => {
					this.isPlaying = true;
					this.startProgressTimer();
				});

				this.audioContext.onPause(() => {
					this.isPlaying = false;
					this.clearProgressTimer();
				});

				this.audioContext.onStop(() => {
					this.isPlaying = false;
					this.clearProgressTimer();
				});

				this.audioContext.onEnded(() => {
					this.isPlaying = false;
					this.currentProgress = 0;
					this.clearProgressTimer();
				});

				this.audioContext.onTimeUpdate(() => {
					this.currentProgress = Math.floor(this.audioContext.currentTime);
				});

				this.audioContext.onCanplay(() => {
					console.log('onCanplay', this.audioContext);
					// this.duration = Math.floor(this.audioContext.duration);
				});

				this.audioContext.onError((res) => {
					console.error('播放错误:', res);
					uni.showToast({
						title: '播放失败: ' + res.errMsg,
						icon: 'none'
					});
					this.isPlaying = false;
					this.clearProgressTimer();
				});

				// 自动播放
				this.audioContext.play();
			},

			togglePlay() {
				if (this.isPlaying) {
					this.audioContext.pause();
				} else {
					this.audioContext.play();
				}
			},

			startProgressTimer() {
				this.clearProgressTimer();
				this.progressInterval = setInterval(() => {
					if (this.audioContext) {
						this.currentProgress = Math.floor(this.audioContext.currentTime);
					}
				}, 200);
			},

			clearProgressTimer() {
				if (this.progressInterval) {
					clearInterval(this.progressInterval);
					this.progressInterval = null;
				}
			},

			onSliderChanging(e) {
				this.currentProgress = e.detail.value;
			},

			onSliderChange(e) {
				if (this.audioContext) {
					this.audioContext.seek(e.detail.value);
				}
			},

			closePlayModal() {
				if (this.audioContext) {
					this.audioContext.stop();
					this.audioContext.destroy();
					this.audioContext = null;
				}
				this.clearProgressTimer();
				this.showPlayModal = false;
				this.isPlaying = false;
				this.currentProgress = 0;
			},

			convertText() {
				if (this.status == 3 && this.audioPath) {
					// 
					uni.navigateTo({
						url: '/pages/audioConvertText/audioConvertText'
					})
				} else {
					uni.showModal({
						content: '现在转文字会结束录音，无法继续录制，是否结束并转文字？',
						confirmText: '转文字',
						cancelText: '取消',
						success: (res) => {
							if (res.confirm) {
								this.stopRecord();
								setTimeout(() => {
									uni.navigateTo({
										url: '/pages/audioConvertText/audioConvertText'
									})
								}, 200)


							}
						}
					});
				}
			},

			copyText() {
				if (!this.convertedText) return;

				uni.setClipboardData({
					data: this.convertedText,
					success: () => {
						uni.showToast({
							title: '复制成功',
							icon: 'success'
						});
					}
				});
			},

			startTimer() {
				clearInterval(this.timer);
				this.timer = setInterval(() => {
					this.recordedSeconds++;
					this.currentTime = this.formatTime(this.recordedSeconds);

					if (this.recordedSeconds >= 600) {
						this.stopRecord();
					}
				}, 1000);
			},

			formatTime(seconds) {
				const mins = Math.floor(seconds / 60);
				const secs = Math.floor(seconds % 60);
				return `${mins.toString().padStart(2, '0')}:${secs.toString().padStart(2, '0')}`;
			}
		},

	};
</script>

<style lang="scss" scoped>
	.container {
		display: flex;
		flex-direction: column;
		background-color: #f5f5f5;
		text-align: center;
		height: 100vh;
		padding: 20rpx;
	}

	.timer {
		font-size: 100rpx;
		font-weight: bold;
		color: $uni-color-blue;
	}

	.status {
		font-size: 18px;
		color: #333;
		margin-bottom: 20px;
	}

	.icon {
		position: fixed;
		bottom: 40vh;
		left: 50%;
		transform: translateX(-50%);
		text-align: center;
		display: flex;
		justify-content: center;
		align-items: center;
		flex-direction: column;

		.icon-box {
			background: $uni-color-blue;
			text-align: center;
			padding: 10rpx;
			border-radius: 50%;
			width: 100rpx;
			height: 100rpx;
			display: flex;
			justify-content: center;
			align-items: center;
		}

		.icon-text {
			margin-top: 20rpx;
		}
	}

	.btns {
		display: flex;
		position: fixed;
		bottom: 80rpx;
		width: 96vw;
		left: 50%;
		transform: translateX(-50%);
		gap: 20rpx;

		.btn {
			width: 50%;
		}
	}

	.green {
		color: $uni-color-green
	}

	.bg-green {
		background-color: $uni-color-green !important;
	}

	.yellow {
		color: rgb(252, 136, 0);
	}

	/* 音频播放器样式 */
	.audio-player {
		padding: 20rpx;
		display: flex;
		flex-direction: column;
		align-items: center;
		width: 100%;
	}

	.audio-controls {
		width: 100%;
		display: flex;
		flex-direction: column;
		align-items: center;
		margin-top: 20rpx;
	}

	.slider {
		width: 100%;
		margin: 30rpx 0;
	}

	.time-display {
		width: 100%;
		display: flex;
		justify-content: space-between;
		font-size: 24rpx;
		color: #666;
	}

	.modal-footer {
		display: flex;
		justify-content: center;
		margin-top: 30rpx;
		gap: 20rpx;
	}

	.text-result {
		padding: 20rpx;
		line-height: 1.6;
		white-space: pre-wrap;
	}
</style>