<template>
	<view class="container">
		<view class="padding-20">
			<u-subsection :list="platformList" mode="subsection" :current="curPlatform" activeColor="#7A60FF"
				@change="platFormChange"></u-subsection>
		</view>
		<view class="flex jc-between al-center">
			<view class="info">
				可保存7天内、最近的10条记录。
			</view>
			<view class="" @click="clearAll" style="font-size: 28rpx;">
				<u--text color="#7A60FF" size="13" text="全部清空"></u--text>
			</view>
		</view>

		<template v-if="textList.length>0">
			<textList :textList="textList" @copy="copyText" @detail="goResultPage" @del="del">
			</textList>
		</template>


		<template v-else>
			<view style="margin-top: 200rpx;">
				<u-empty text="暂无记录"></u-empty>
			</view>
		</template>


	</view>
</template>

<script>
	export default {
		data() {
			return {
				curPlatform: 0,
				platformList: ['短视频文案', '小红书文案', '长视频文案'],
				textList: [],
			}
		},
		mounted() {
			this.getTextList();
		},
		methods: {
			platFormChange(e) {
				this.curPlatform = e
			},
			del(id) {
				const idx = this.textList.findIndex(e => e.id == id)
				if (idx != -1) {
					this.textList.splice(idx, 1)
				}
			},
			goResultPage(id) {
				uni.navigateTo({
					url: '/pages/evaluateTextRes/evaluateTextRes' + '?id=' + id
				})
			},

			clearAll() {
				uni.showModal({
					title: '提示',
					content: '确认全部清空吗？',
					success: (res) => {
						if (res.confirm) {
							this.textList = []
						}
					}
				})
			},

			getTextList() {
				const list = [{
						id: 1,
						time: '2022-02-08 20:00:00',
						content: '记录记录记录记录记录记录记录记录记录记录记录记录记录记录记录记录记录',
					},
					{
						id: 2,
						time: '2022-02-06 20:00:00',
						content: '记录记录记录记录记录记录记录记录记录记录记录记录记录记录记录记录记录记录记录记录记录记录记录记录记录记录记录记录记录记录记录记录记录记录',
					},
				]
				this.textList = list
			}
		}
	}
</script>

<style lang="scss" scoped>
	.container {
		padding: 20rpx;

		.info {
			color: #999;
			font-size: 28rpx;
			padding-left: 20rpx;
		}


	}
</style>