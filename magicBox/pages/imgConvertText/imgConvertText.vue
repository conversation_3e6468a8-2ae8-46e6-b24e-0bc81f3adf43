<template>
	<view class="">
		<textareaBox height="400" v-model="value" placeholder="识别图片里的文字"></textareaBox>
		<view class="btns">
			<view class="btn">
				<u-button type="primary" plain>文案改写</u-button>
			</view>
			<view class="btn">
				<u-button type="primary" plain>文案配音</u-button>
			</view>
			<view class="btn">
				<u-button type="primary" plain>删除换行</u-button>
			</view>
			<view class="btn">
				<u-button type="primary" @click="copyText">复制文案</u-button>
			</view>
		</view>
		<share></share>
	</view>
</template>

<script>
	export default {
		data() {
			return {
				value: ''

			}
		},
		onLoad() {
			this.translate()
		},
		methods: {
			copyText() {
				if (!this.value) {
					uni.showToast({
						title: '没有可复制的文案',
						icon: "none"
					})
					return
				}
				uni.setClipboardData({
					data: this.value,
					success: () => {
						uni.showToast({
							title: '文案已复制',
							icon: "none"
						})
					},
					fail: () => {
						uni.showToast({
							title: '文案复制失败',
							icon: "none"
						})
					}
				})
			},
			translate() {
				uni.showLoading({
					title: "识别中...",
				})

				setTimeout(() => {
					uni.hideLoading()
					this.value = '识别结果识别结果识别结果识别结果识别结果识别结果'
				}, 2000)
			},

		}
	}
</script>

<style scoped lang="scss">
	.btns {
		display: flex;
		justify-content: space-between;
		padding: 0 20rpx;
		gap: 10rpx;
	}

	.btn {
		width: 30%;
	}
</style>