<template>
	<view class="padding-bottom-60">
		<view class="rewrite-res">
			<view class="flex al-center jc-between padding-lr-20" style="padding-top: 20rpx;">
				<view class="flex al-center ">
					<view class="m-right-20 bold">
						显示改动点
					</view>
					<u-switch v-model="showChange" size="20" activeColor="#4cd964"></u-switch>
				</view>
				<view class="font-color-info font-size-24" @click="goPage">
					历史记录>
				</view>
			</view>
			<view class="flex al-center m-top-20 padding-20" style="gap: 20rpx;font-size: 28rpx;">
				<view class="bg-blue color-white  radius" style="padding:2rpx 6rpx;">
					原创度：{{createNum}}%
				</view>
				<view class="bg-red " style="text-decoration: line-through;">
					表示删除
				</view>
				<view class="bg-green ">
					表示删除
				</view>
			</view>

			<textareaBox height="300" v-model="value" placeholder="文案改写" count maxlength="1000"></textareaBox>

			<view class="flex flex-wrap gap-10  padding-20">
				<view class="flex1">
					<u-button type="primary" plain>AI写爆款</u-button>
				</view>
				<view class="flex1">
					<u-button type="primary" plain>重新改写</u-button>
				</view>
				<view class="flex1">
					<u-button type="primary" @click="copyText">复制</u-button>
				</view>
			</view>

			<view class="flex flex-wrap gap-10  padding-20">
				<view class="flex1">
					<u-button type="primary" plain>文案配音</u-button>
				</view>
				<view class="flex1">
					<u-button type="primary" plain>违禁词检测</u-button>
				</view>
				<view class="flex1">
					<u-button type="primary" plain>文案专业评测</u-button>
				</view>
			</view>


		</view>

		<view class="m-top-40">
			<share></share>
		</view>
	</view>

</template>

<script>
	import {
		copyText
	} from '@/utils/index.js';
	export default {
		data() {
			return {
				value: '',
				createNum: 87.6,
				showChange: false,

			}
		},
		onLoad(p) {
			if (p.id) {
				uni.setNavigationBarTitle({
					title: '文案改写详情'
				})
			} else {
				//识别进来
				console.log('识别进来');
			}

		},
		methods: {
			goPage() {
				uni.navigateTo({
					url: "/pages/rewriteTextHis/rewriteTextHis"
				})
			},
			clearContent() {
				this.value = ''
			},
			copyText() {
				copyText(this.value)
			}

		}
	}
</script>

<style lang="scss" scoped>
	.rewrite-res {}
</style>