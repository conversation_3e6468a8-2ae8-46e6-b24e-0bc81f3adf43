<template>
	<view>
		<u-sticky bgColor="#fff">
			<u-tabs :list="tabList" lineWidth="100" :current="curTabIndex" lineColor="#7A60FF" @change="tabChange"
				:activeStyle="{
            color: '#7A60FF',
        }"></u-tabs>
		</u-sticky>
		<view class="mdl-content">
			<!-- 提取视频文案 -->
			<getVideoText v-if="curTab=='getVideoText'"></getVideoText>
			<!-- 提取笔记文案 -->
			<getNoteText v-if="curTab=='getNoteText'"></getNoteText>
			<!-- 声音转文字 -->
			<voiceToText v-if="curTab=='voiceToText'"></voiceToText>
			<!-- 图片转文字 -->
			<imageToText v-if="curTab=='imageToText'"></imageToText>
			<!-- 文案改写去重 -->
			<rewriteText v-if="curTab=='rewriteText'"></rewriteText>
			<!-- 文案拆分重组 -->
			<splitMergeText v-if="curTab=='splitMergeText'"></splitMergeText>
			<!-- 文案专业评测 -->
			<evaluateText v-if="curTab=='evaluateText'"></evaluateText>
			<!-- 违禁词检测 -->
			<sensitiveCheck v-if="curTab=='sensitiveCheck'"></sensitiveCheck>
			<!-- 提取无水印视频 -->
			<dlNoWatermarkVideo v-if="curTab=='dlNoWatermarkVideo'"></dlNoWatermarkVideo>
		</view>
	</view>
</template>

<script>
	import {
		topPart,
		bottomPart
	} from '@/config/menu';
	import getVideoText from './comp/getVideoText.vue';
	import getNoteText from './comp/getNoteText.vue';
	import voiceToText from './comp/voiceToText.vue';
	import imageToText from './comp/imageToText.vue';
	import rewriteText from './comp/rewriteText.vue';
	import splitMergeText from './comp/splitMergeText.vue';
	import evaluateText from './comp/evaluateText.vue';
	import sensitiveCheck from './comp/sensitiveCheck.vue';
	import dlNoWatermarkVideo from './comp/dlNoWatermarkVideo.vue';
	export default {
		components: {
			getVideoText,
			getNoteText,
			voiceToText,
			imageToText,
			rewriteText,
			splitMergeText,
			evaluateText,
			sensitiveCheck,
			dlNoWatermarkVideo,
		},
		data() {
			return {
				curTabIndex: 0,
				curTab: '',
				tabList: [...topPart, ...bottomPart]
			}
		},
		watch: {
			curTab: {
				handler(n) {
					if (n) {
						console.log(n);
						const title = this.tabList.find((e, i) => {
							if (e.key == n) {
								this.curTabIndex = i
								return true
							}

						}).name

						uni.setNavigationBarTitle({
							title: title
						})
					}

				},
				immediate: true

			}
		},
		onLoad(p) {
			this.curTab = p.key

		},
		methods: {
			tabChange(v) {
				this.curTab = v.key
			}
		}
	}
</script>

<style scoped>
	.mdl-content {
		margin-top: 20rpx;
	}
</style>