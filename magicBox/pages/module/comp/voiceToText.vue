<template>
	<view class="container">
		<view class="flex nav-img color-white">
			<view class="record radius" @click="goRecording">

				<view class="bg-white radius icon">
					<u-icon name="mic" size="30" color="rgb(86, 87, 240)"></u-icon>
				</view>
				<view style="height: 100rpx;">

				</view>
				<view class="">
					录音转文字
				</view>
				<view class="font-size-24 m-top-20">
					普通话录音，自动转文字
				</view>
			</view>
			<view class="upload radius" @click="upLoadFile">

				<view class="bg-white radius icon">
					<u-icon name="file-text-fill" size="30" color="rgb(7, 193, 96)"></u-icon>
				</view>
				<view style="height: 100rpx;">

				</view>

				<view class="">
					上传本地文件
				</view>
				<view class="font-size-24 m-top-20">
					选取本地文件
				</view>
			</view>
		</view>
		<u-tabs :list="tabs" :current="activeTab" :activeStyle="{
            color: '#7A60FF',
        }" lineColor="#7A60FF" lineWidth="60" @change="tabChange"></u-tabs>
		<view class="flex jc-between al-center">
			<view class="info">
				可保存7天内、最近的10条记录。
			</view>
			<view class="" @click="clearAll">
				<u--text color="#7A60FF" size="13" text="全部清空"></u--text>
			</view>
		</view>

		<view v-if="activeTab === 0">
			<template v-if="recordingList.length>0">
				<view class="recording-item shadow m-top-40 radius padding-20 flex al-center jc-between"
					v-for="item in recordingList" :key="item.id">
					<view class="">
						<view class="flex al-center">
							<view class="" @click="togglePlay(item)">
								<u-icon v-if="item.isPlay" color="#7A60FF" bold size="40" name="pause-circle"></u-icon>
								<u-icon v-else size="40" color="#7A60FF" bold name="play-circle"></u-icon>
							</view>
							<view class="m-left-20">
								<text v-if="currentPlayingId == item.id">{{formatTime(item.currentTime || 0)}} /
								</text>
								{{item.duration}}
							</view>
						</view>
						<view class="font-color-info font-size-24 m-top-20">
							{{item.time}}
						</view>
					</view>
					<view class="flex">
						<view class="m-right-20">
							<u-button type="primary" size="small" plain @click="delRecording(item.id)">删除</u-button>
						</view>
						<u-button type="primary" size="small" @click="goResultPage">转文字</u-button>
					</view>
				</view>
			</template>
			<template v-else>
				<view style="margin-top: 200rpx;">
					<u-empty text="暂无记录"></u-empty>
				</view>
			</template>


		</view>

		<view v-if="activeTab === 1">
			<template v-if="textList.length>0">
				<textList :textList="textList" @copy="copyText" @detail="goResultPage" @del="del"></textList>
			</template>


			<template v-else>
				<view style="margin-top: 200rpx;">
					<u-empty text="暂无记录"></u-empty>
				</view>
			</template>

		</view>
	</view>
</template>

<script>
	import {
		formatTime
	} from '@/utils/index.js'
	export default {
		data() {
			return {
				activeTab: 0,
				recordingList: [],
				textList: [],
				tabs: [{
						name: '录音记录'
					},
					{
						name: '转文字记录'
					}
				],
				innerAudioContext: null, // 音频上下文
				currentPlayingId: null, // 当前播放的音频ID
				formatTime: formatTime,

			}
		},
		mounted() {
			// 创建音频实例
			this.innerAudioContext = uni.createInnerAudioContext();
			this.initAudioEvents();
			this.getRecordingList();
			this.getTextList();
		},
		onUnload() {
			this.stopPlay();
			this.innerAudioContext.destroy();
		},
		beforeDestroy() {
			// 组件销毁时停止播放并释放资源
			this.stopPlay();
			this.innerAudioContext.destroy();
		},
		methods: {
			del(id) {

				const idx = this.textList.findIndex(e => e.id == id)
				if (idx != -1) {
					this.textList.splice(idx, 1)
				}


			},

			goResultPage() {
				uni.navigateTo({
					url: "/pages/audioConvertText/audioConvertText"
				})
			},
			upLoadFile() {
				wx.chooseVideo({
					sourceType: ['album', 'camera'], // 从相册或相机选择
					maxDuration: 60, // 视频最大时长（秒），默认60
					camera: 'back', // 默认后置摄像头
					compressed: true, // 是否压缩视频
					success: (res) => {
						console.log('视频临时路径:', res.tempFilePath);
						// 调用上传方法
						this.uploadAudio(res.tempFilePath);
					},
					fail: (err) => {
						console.error('选择视频失败:', err);
					}
				});
			},
			uploadAudio(fileUrl) {
				uni.showLoading({
					title: '模拟上传...'
				})

				setTimeout(() => {
					uni.hideLoading()
					uni.navigateTo({
						url: "/pages/audioConvertText/audioConvertText"
					})
				}, 1000)

				// uni.uploadFile({
				// 	url: 'https://example.com/upload', // 替换为你的上传接口地址
				// 	filePath: fileUrl,
				// 	name: 'file', // 服务器接收的字段名
				// 	formData: {
				// 		// 如果需要，可以在这里添加其他表单数据
				// 		'user': 'test'
				// 	},
				// 	success: (res) => {
				// 		console.log('上传成功', res);
				// 		uni.showToast({
				// 			title: '上传成功',
				// 			icon: 'success',
				// 		});
				// 	},
				// 	fail: (err) => {
				// 		console.error('上传失败', err);
				// 		uni.showToast({
				// 			title: '上传失败',
				// 			icon: 'none',
				// 		});
				// 	},
				// });
			},
			goRecording() {
				uni.navigateTo({
					url: "/pages/recording/recording"
				})
			},
			// 初始化音频事件监听
			initAudioEvents() {
				this.innerAudioContext.onPlay(() => {
					console.log('开始播放');
				});

				this.innerAudioContext.onPause(() => {
					console.log('暂停播放');
				});

				this.innerAudioContext.onStop(() => {
					console.log('停止播放');
					this.resetCurrentPlaying();
				});

				this.innerAudioContext.onEnded(() => {
					console.log('播放完成');
					this.resetCurrentPlaying();
				});

				this.innerAudioContext.onError((res) => {
					console.log('播放错误', res);
					uni.showToast({
						title: '播放失败',
						icon: 'none'
					});
					this.resetCurrentPlaying();
				});

				// 更新当前播放时间
				this.innerAudioContext.onTimeUpdate(() => {
					if (this.currentPlayingId) {
						const currentItem = this.recordingList.find(item => item.id === this.currentPlayingId);
						if (currentItem) {
							currentItem.currentTime = this.innerAudioContext.currentTime;
							// 手动触发视图更新
							this.$forceUpdate();
						}
					}
				});
			},

			// 切换播放/暂停状态
			togglePlay(item) {
				if (this.currentPlayingId === item.id) {
					// 当前点击的是正在播放的音频
					if (item.isPlay) {
						this.pausePlay();
					} else {
						this.resumePlay();
					}
					console.log('原来的', item);
				} else {
					console.log('播放新的', item);
					// 点击的是其他音频，停止当前播放并开始新的播放
					this.stopPlay()
					setTimeout(() => {
						this.startPlay(item);
					}, 200)
				}
			},

			// 开始播放
			startPlay(item) {

				// 重置音频上下文
				this.innerAudioContext.src = item.url;
				// setTimeout(() => {
				// 	this.innerAudioContext?.seek(0);
				// }, 200)

				// 更新状态
				this.currentPlayingId = item.id;
				console.log('设置播放id', this.currentPlayingId);
				item.isPlay = true;
				item.currentTime = 0;

				// 开始播放
				this.innerAudioContext.play();
				console.log('开始播放', item);
			},

			// 暂停播放
			pausePlay() {
				if (this.currentPlayingId) {
					const currentItem = this.recordingList.find(item => item.id === this.currentPlayingId);
					if (currentItem) {
						currentItem.isPlay = false;
						console.log('暂停播放', currentItem);

					}
					this.innerAudioContext.pause();

				}
			},

			// 继续播放
			resumePlay() {
				if (this.currentPlayingId) {
					const currentItem = this.recordingList.find(item => item.id === this.currentPlayingId);
					if (currentItem) {
						currentItem.isPlay = true;
						console.log('继续播放', currentItem);
					}
					this.innerAudioContext.play();
				}
			},

			// 停止播放
			stopPlay() {
				if (this.currentPlayingId) {
					const currentItem = this.recordingList.find(item => item.id === this.currentPlayingId);
					if (currentItem) {
						currentItem.isPlay = false;
						currentItem.currentTime = 0;

					}
					this.innerAudioContext.stop();
					this.currentPlayingId = null;
					console.log('清除播放id', this.currentPlayingId);
				}
			},

			// 重置当前播放状态
			resetCurrentPlaying() {
				if (this.currentPlayingId) {
					const currentItem = this.recordingList.find(item => item.id === this.currentPlayingId);
					if (currentItem) {
						currentItem.isPlay = false;
						currentItem.currentTime = 0;
					}
					this.currentPlayingId = null;
					// this.$forceUpdate();
				}
			},



			// 删除录音
			delRecording(id) {
				uni.showModal({
					title: '提示',
					content: '确认删除这条录音吗？',
					success: (res) => {
						if (res.confirm) {
							// 如果正在播放的是要删除的音频，先停止
							if (this.currentPlayingId === id) {
								this.stopPlay();
							}
							// 从列表中删除
							this.recordingList = this.recordingList.filter(item => item.id !== id);
							uni.showToast({
								title: '删除成功',
								icon: 'success'
							});
						}
					}
				});
			},
			clearAll() {
				uni.showModal({
					title: '提示',
					content: '确认全部清空吗？',
					success: (res) => {
						if (res.confirm) {
							if (this.activeTab == 0) {
								this.recordingList = []
								if (this.currentPlayingId) {
									this.stopPlay();
								}
							} else {
								this.textList = []
							}
						}
					}

				})
			},

			tabChange(v) {
				this.activeTab = v.index
				this.stopPlay(); // 切换tab时停止播放
			},
			getRecordingList() {
				const list = [{
						id: 1,
						time: '2022-02-08 20:00:00',
						duration: '02:08',
						isPlay: false,
						url: 'https://preview.tosound.com:3321/preview?file=ccmixter%2F0%2F6%2F660328.mp3&token=Y2NtaXh0ZXIlMkYwJTJGNiUyRjY2MDMyOC5tcDM=&sound=audio.mp3'
					},
					{
						id: 2,
						time: '2022-02-06 20:00:00',
						duration: '02:08',
						isPlay: false,
						url: 'https://cdn.tosound.com:3321/preview?file=looperman%2F0%2F105%2F618616.mp3&token=bG9vcGVybWFuJTJGMCUyRjEwNSUyRjYxODYxNi5tcDM=&sound=audio.mp3',
					},
					{
						id: 3,
						time: '2022-02-01 20:00:00',
						duration: '02:08',
						isPlay: false,
						url: 'https://cdn.tosound.com:3321/preview?file=looperman%2F0%2F103%2F617069.mp3&token=bG9vcGVybWFuJTJGMCUyRjEwMyUyRjYxNzA2OS5tcDM=&sound=audio.mp3'
					}
				]
				this.recordingList = list
			},
			getTextList() {
				const list = [{
						id: 1,
						time: '2022-02-08 20:00:00',
						content: '转文字的结果转文字的结果转文字的结果转文字的结果转文字的结果转文字的结果转文字的结果转文字的结果转文字的结果转文字的结果转文字的结果转文字的结果转文字的结果转文字的结果转文字的结果转文字的结果转文字的结果转文字的结果',
					},
					{
						id: 2,
						time: '2022-02-06 20:00:00',
						content: '转文字的结果转文字的结果转文字的结果转文字的结果转文字的结果转文字的结果转文字的结果转文字的结果转文字的结果',
					},

				]
				this.textList = list
			}
		}
	}
</script>

<style lang="scss" scoped>
	.container {
		padding: 20rpx;

		.info {
			color: #999;
			font-size: 24rpx;
			padding-left: 20rpx;
		}

		.nav-img {
			gap: 20rpx;
			text-align: center;

			.record {
				position: relative;
				width: 50%;
				background: rgb(86, 87, 240);
				padding: 30rpx;
				text-align: center;


			}

			.icon {
				position: absolute;
				padding: 10rpx;
				left: 50%;
				transform: translateX(-50%);
			}

			.upload {
				position: relative;
				width: 50%;
				background: rgb(7, 193, 96);
				padding: 30rpx;
			}

			image {
				height: 240rpx;
			}
		}
	}
</style>