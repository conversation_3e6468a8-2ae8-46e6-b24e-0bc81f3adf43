<template>
	<view class="sensitive-check">
		<view class="margin-lr-20">
			<u-alert showIcon title="默认检测以下词库，点击词库名可取消对该词库的检测" fontSize="12"></u-alert>
		</view>
		<view class="">
			<u-checkbox-group v-model="type" placement="row" activeColor="#7A60FF" size="14" label-size="11">
				<view class="flex gap-10 flex-wrap ">
					<view style="width: 40%;" class="m-right-20 m-top-20 flex jc-center al-center"
						v-for="item in typeList" :key="item.name">
						<u-checkbox :label="item.label" :name="item.name"></u-checkbox>
					</view>
				</view>

			</u-checkbox-group>
		</view>
		<textareaBox height="400" v-model="value" placeholder="请粘贴或者输入您要检测的文案" count maxlength="1000">
		</textareaBox>
		<view class="padding-20 flex gap-10">
			<view class="flex1">
				<u-button type="success" plain @click="pasteLink">粘贴文案</u-button>
			</view>
			<view class="flex1">
				<u-button type="primary" @click="submit">开始检测</u-button>
			</view>
		</view>
		<pageLoading ref="loadingRef" :progress="progress" loadingText='违禁词检测中'></pageLoading>
	</view>
</template>

<script>
	import {
		copyText
	} from '@/utils/index.js'
	export default {
		data() {
			return {
				progress: 0,
				typeList: [{
					label: '广告法违禁词',
					name: 1
				}, {
					label: '美妆违禁词',
					name: 2
				}, {
					label: '直播间违禁词',
					name: 3
				}, {
					label: '常见违禁词',
					name: 4
				}],
				type: [1, 2, 3, 4],
				value: ''
			}
		},
		methods: {
			submit() {
				if (!this.value) {
					uni.showToast({
						icon: "none",
						title: "请粘贴或者输入您要检测的文案"
					})
					return
				}
				this.$refs.loadingRef.open()
				setTimeout(() => {
					this.progress = 100
					setTimeout(() => {
						this.$refs.loadingRef.close()
						uni.navigateTo({
							url: "/pages/sensitiveCheckRes/sensitiveCheckRes"
						})
					}, 500)
				}, 1000)
			},
			pasteLink() {
				uni.getClipboardData({
					success: (res) => {
						if (res.data) {
							this.value = res.data
						} else {
							uni.showToast({
								icon: 'none',
								title: '未检测到粘贴内容，请重新复制，再试一试粘贴哦'
							})
						}

					},
					fail: () => {
						uni.showToast({
							title: '粘贴失败'
						})
					}
				})
			},
		}
	}
</script>

<style lang="scss" scoped>
	.sensitive-check {}
</style>