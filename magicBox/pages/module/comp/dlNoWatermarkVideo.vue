<template>
	<view class="get-video-text-page">
		<textareaBox height="160" v-model="value" placeholder="长按粘贴视频链接，支持斗音(口令或链接)、视品号、筷手、西呱、小红叔、头涤、wei视、bi站、皮皮侠视频链接"
			count maxlength="500"></textareaBox>
		<view class="btns">
			<view class="btn">
				<u-button type="success" plain @click="pasteLink">粘贴链接</u-button>
			</view>
			<view class="btn">
				<u-button type="primary">提取文案</u-button>

			</view>
			<view class="btn">
				<u-button type="primary" @click="getVideo">提取视频</u-button>
			</view>

		</view>
		<view class="result m-top-20 padding-20" v-if="showRes">
			<view class="flex jc-between">
				<view class="flex">
					<u-icon name="checkmark-circle-fill" v-if="progress==100" size="18" color="#7a60ff"></u-icon>
					<u-icon name="clock" v-else size="18" color="#7a60ff"></u-icon>
					<text class="bold m-right-20">{{progress==100?'已去除水印':'正在去水印'}}</text>
					<text class="bold color-green">{{progress}}%</text>
				</view>
				<view class="flex al-center">
					<text class="font-size-24 font-color-info m-right-20">{{progress==100?'耗时':'约等待'}}</text>
					<text>{{formatTime(waitTime)}}</text>
				</view>
			</view>

			<view class="res-content" v-if="resContent">
				<video style="width: 100%;" :src="resContent" controls></video>
			</view>
			<view class="res-content" style="color: #999;" v-else>
				正在去水印...
			</view>
			<!-- <view class="font-size-24 flex m-top-20 al-center" v-if="resContent">
				文案提取不全？点击 <view style="width: 140rpx;margin-left: 10rpx;">
					<u-button type="primary" size="mini" plain="">重新提取</u-button>
				</view>
			</view> -->


			<view class="btns2 m-top-40" v-if="resContent">
				<view class="btn2">
					<u-button type="primary" plain>视频去字幕</u-button>
				</view>
				<view class="btn2">
					<u-button type="primary">保存相册</u-button>

				</view>
				<view class="btn2">
					<u-button type="primary" plain>视频提取人声</u-button>
				</view>
				<view class="btn2">
					<u-button type="primary" @click="goPage">提取视频文案</u-button>
				</view>

			</view>


		</view>
		<view class="m-top-80" v-if="resContent">
			<share></share>
		</view>

		<u-popup mode="center" :show="showTip" @close="close" @open="open" :closeOnClickOverlay="false" round="6">
			<view class="padding-20" style="margin: 20rpx;width: 70vw;">
				<view class="bold" style="text-align: center;">
					说明
				</view>
				<view style="max-height: 70vh;overflow-y: scroll;font-size: 28rpx;" class="m-top-40">
					<view class="">
						去水映功能由本平台完全免费提供，不收取任何费用，不进行任何商业性目的用。
					</view>
					<view class="m-top-20">
						本平台作为提供技术服务的中立工具，提供的去水映功能仅帮助用户提取用于学习观看，降低浏览障碍，提升观赏体验。
					</view>
					<view class="m-top-20">
						请用户务必合法使用，若用户滥用该功能实施侵权行为，用户应自行承担由此产生的法律责任。本平台倡导与用户共同营造风清气正的网络环境!
					</view>
				</view>

			</view>
			<view class="flex">
				<view class="flex1">
					<u-button type="info" @click="giveup">放弃去水印</u-button>
				</view>
				<view class="flex1">
					<u-button type="primary" @click="iknown">我知道了</u-button>
				</view>
			</view>

		</u-popup>
	</view>


</template>

<script>
	import {
		formatTime,
		copyText
	} from '@/utils/index.js'
	export default {
		data() {
			return {
				showTip: false,
				showRes: false,
				resContent: '',
				waitTime: 6,
				progress: 0,
				value: '',
				formatTime: formatTime,
			}
		},
		mounted() {
			if (this.isShowTip()) {
				this.open()
			}
		},
		methods: {
			isShowTip() {
				const res = uni.getStorageSync('noShowVideoTip')
				return res ? false : true
			},
			iknown() {
				this.close()
				uni.setStorageSync('noShowVideoTip', true)

			},
			giveup() {
				this.close()
				uni.navigateBack()
			},
			close() {
				this.showTip = false
			},
			open() {
				this.showTip = true
			},
			goPage() {
				// uni.navigateTo({
				// 	url: "/pages/module/module" + '?key=rewriteText'
				// })
			},
			copyText() {
				console.log(this.resContent, 'this.resContent');
				copyText(this.resContent)
			},
			getVideo() {
				if (!this.value) {
					uni.showToast({
						icon: "none",
						title: "请先粘贴视频链接哦"
					})
					return
				}
				this.showRes = true
				setTimeout(() => {
					this.progress = 100
					this.resContent = 'https://qiniu-web-assets.dcloud.net.cn/unidoc/zh/2minute-demo.mp4'
				}, 1000)
				//接口请求
			},
			pasteLink() {
				uni.getClipboardData({
					success: (res) => {
						if (res.data) {
							this.value = res.data
						} else {
							uni.showToast({
								icon: 'none',
								title: '未检测到粘贴内容，请重新复制，再试一试粘贴哦'
							})
						}

					},
					fail: () => {
						uni.showToast({
							title: '粘贴失败'
						})
					}
				})
			},
			clearContent() {
				this.value = ''
			}
		}
	}
</script>

<style scoped>
	.get-video-text-page {
		padding-bottom: 60rpx;
	}

	.res-content {
		margin-top: 20rpx;
		min-height: 200rpx;
		padding: 20rpx;
		/* border: 1px solid #bbbbbb; */
	}

	.btns,
	.btns2 {
		display: flex;
		justify-content: space-between;
		padding: 0 20rpx;
		flex-wrap: wrap;
		gap: 20rpx;
	}

	.btn {
		width: 30%;
	}

	.btns2 {
		padding: 0;
	}

	.btn2 {
		width: 340rpx;
	}
</style>