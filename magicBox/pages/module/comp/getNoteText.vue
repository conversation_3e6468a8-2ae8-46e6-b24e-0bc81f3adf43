<template>
	<view class="get-note-text-page">
		<textareaBox height="160" v-model="value" placeholder="支持小红叔、斗音图文、知呼、微脖、头涤等主流文章链接，一键提取内容" count maxlength="500">
		</textareaBox>
		<view class="btns">
			<view class="btn">
				<u-button type="success" plain @click="pasteLink">粘贴链接</u-button>
			</view>
			<view class="btn">
				<u-button type="primary" @click="getText">提取文案</u-button>

			</view>
			<view class="btn" style="width: 300rpx;">
				<u-button type="primary">爆款文案生成</u-button>
			</view>

		</view>
		<view class="result m-top-20 padding-20" v-if="showRes">
			<view class="flex jc-between">
				<view class="flex">
					<u-icon name="checkmark-circle-fill" v-if="progress==100" size="18" color="#7a60ff"></u-icon>
					<u-icon name="clock" v-else size="18" color="#7a60ff"></u-icon>
					<text class="bold m-right-20">已提取</text>
					<text class="bold color-green">{{progress}}%</text>
				</view>
				<view class="flex al-center">
					<text class="font-size-24 font-color-info m-right-20">{{progress==100?'耗时':'约等待'}}</text>
					<text>{{formatTime(waitTime)}}</text>
				</view>
			</view>

			<view class="res-content" v-if="resContent">
				{{resContent}}
			</view>
			<view class="res-content" style="color: #999;" v-else>
				您可以浏览其他界面，会为您保留提取结果
			</view>
			<view class="font-size-24 flex m-top-20 al-center" v-if="resContent">
				文案提取不全？点击 <view style="width: 140rpx;margin-left: 10rpx;">
					<u-button type="primary" size="mini" plain="">重新提取</u-button>
				</view>
			</view>


			<view class="btns2 m-top-40" v-if="resContent">
				<view class="btn2">
					<u-button type="primary" plain>违禁词检测</u-button>
				</view>
				<view class="btn2">
					<u-button type="primary" @click="copyText">复制文案</u-button>

				</view>
				<view class="btn2">
					<u-button type="primary" plain>文案专业评测</u-button>
				</view>
				<view class="btn2">
					<u-button type="primary" @click="goPage">文案改写风格</u-button>
				</view>

			</view>


		</view>
		<view class="m-top-80" v-if="resContent">
			<share></share>
		</view>

	</view>


</template>

<script>
	import {
		formatTime,
		copyText
	} from '@/utils/index.js'
	export default {
		data() {
			return {
				showRes: false,
				resContent: '',
				waitTime: 6,
				progress: 0,
				value: '',
				formatTime: formatTime,
			}
		},
		methods: {
			goPage() {
				uni.navigateTo({
					url: "/pages/module/module" + '?key=rewriteText'
				})
			},
			copyText() {
				console.log(this.resContent, 'this.resContent');
				copyText(this.resContent)
			},
			getText() {
				if (!this.value) {
					uni.showToast({
						icon: "none",
						title: "请先粘文章贴链接哦"
					})
					return
				}
				this.showRes = true
				setTimeout(() => {
					this.progress = 100
					this.resContent = '获取的结果，获取的结果，获取的结果，获取的结果'
				}, 1000)
				//接口请求 
			},
			pasteLink() {
				uni.getClipboardData({
					success: (res) => {
						if (res.data) {
							this.value = res.data
						} else {
							uni.showToast({
								icon: 'none',
								title: '未检测到粘贴内容，请重新复制，再试一试粘贴哦'
							})
						}
					},
					fail: () => {
						uni.showToast({
							title: '粘贴失败'
						})
					}
				})
			},
			clearContent() {
				this.value = ''
			}
		}
	}
</script>

<style scoped>
	.get-note-text-page {
		padding-bottom: 60rpx;
	}

	.res-content {
		margin-top: 20rpx;
		min-height: 200rpx;
		padding: 20rpx;
		border: 1px solid #bbbbbb;
	}

	.btns,
	.btns2 {
		display: flex;
		justify-content: space-between;
		padding: 0 20rpx;
		flex-wrap: wrap;
		gap: 20rpx;
	}

	.btn {
		flex: 1;
	}

	.btns2 {
		padding: 0;
	}

	.btn2 {
		width: 340rpx;
	}
</style>