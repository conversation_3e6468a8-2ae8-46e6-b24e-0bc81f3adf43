<template>
	<view class="container">
		<view class="flex nav-img color-white">
			<view class="record radius" @click="chooseImage">
				<view class="">
					<view class="title">
						相册/拍照
					</view>
					<view class="font-size-24 m-top-20">
						拍图扫描 文本识别
					</view>
				</view>
				<view class="bg-white radius icon">
					<u-icon name="photo" size="60" color="rgb(239, 133, 47)"></u-icon>
				</view>
			</view>
			<view class="record radius msg" @click="chooseImageFromChat">
				<view class="">
					<view class="title">
						聊天记录中的图片
					</view>
					<view class="font-size-24 m-top-20">
						从聊天记录中选择图片
					</view>
				</view>
				<view class="bg-white radius icon">
					<u-icon name="weixin-fill" size="60" color="rgb(115, 116, 255)"></u-icon>
				</view>
			</view>
		</view>

	</view>
</template>

<script>
	export default {
		data() {
			return {

			}
		},
		methods: {
			// 从相册选择或拍照
			chooseImage() {
				uni.chooseImage({
					count: 1, // 最多可以选择9张图片
					sizeType: ['original', 'compressed'], // 可以指定是原图还是压缩图
					sourceType: ['album', 'camera'], // 可以指定来源是相册还是相机
					success: (res) => {
						console.log('从相册获取图片', res);
						uni.showLoading({
							title: '模拟上传'
						})
						setTimeout(() => {
							uni.hideLoading()
							uni.navigateTo({
								url: '/pages/imgConvertText/imgConvertText'
							})
						}, 1500)

					},
					fail: (err) => {
						// console.error('选择图片失败:', err);
						// uni.showToast({
						// 	title: '选择图片失败',
						// 	icon: 'none'
						// });
					}
				});
			},

			// 从聊天记录选择图片
			chooseImageFromChat() {
				// 微信小程序中可以使用chooseMessageFile从聊天记录选择文件
				// 其他平台可能需要使用其他方式实现
				if (uni.chooseMessageFile) {
					uni.chooseMessageFile({
						count: 1,
						type: 'image',
						success: (res) => {
							console.log('从聊天记录获取图片', res);
							uni.showLoading({
								title: '模拟上传'
							})
							setTimeout(() => {
								uni.hideLoading()
								uni.navigateTo({
									url: '/pages/imgConvertText/imgConvertText'
								})
							}, 1500)
						},
						fail: (err) => {
							console.error('从聊天记录选择图片失败:', err);
							uni.showToast({
								title: '未选择图片',
								icon: 'none'
							});
						}
					});
				} else {
					uni.showToast({
						title: '当前平台不支持从聊天记录选择',
						icon: 'none'
					});
				}
			},



		}
	}
</script>

<style lang="scss" scoped>
	.container {
		padding: 20rpx;

		.info {
			color: #999;
			font-size: 24rpx;
			padding-left: 20rpx;
		}

		.nav-img {
			gap: 20rpx;
			text-align: left;
			flex-direction: column;

			.title {
				font-size: 40rpx;
			}

			.record {
				position: relative;
				width: 100%;
				background: rgb(239, 133, 47);
				padding: 60rpx;
				text-align: left;
				display: flex;
				justify-content: space-between;
			}

			.icon {
				position: absolute;
				top: 50%;
				transform: translateY(-50%);
				padding: 10rpx;
				right: 60rpx;
				border-radius: 50%;
			}

			.msg {
				background: rgb(115, 116, 255);
			}


		}

	}
</style>