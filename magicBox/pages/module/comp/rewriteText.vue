<template>
	<view style="padding-bottom: 60rpx;">
		<view class="title flex al-center jc-between">
			<view class="">
				粘贴或输入文案
			</view>
			<view class="font-color-info his" @click="goPage">
				历史记录>
			</view>
		</view>

		<textareaBox height="400" v-model="value" placeholder="请粘贴或者输入文案" count maxlength="1000"></textareaBox>
		<view class="flex al-center padding-20">
			<view class="m-right-20">
				同时改写文案风格
			</view>
			<u-switch v-model="rewrite" size="20" activeColor="#4cd964"></u-switch>
		</view>
		<view class="padding-20" v-if="rewrite">
			<u-subsection :list="styleList" mode="subsection" :current="curStyle" activeColor="#4cd964"
				@change="styleChange"></u-subsection>
			<view v-if="curStyle==0">
				<view class="m-top-20 stitle">
					选一种热门风格
				</view>
				<view class="tag-list">
					<view class="tag-list-item" v-for="(item, index) in hotTagList" :key="index">
						<u-tag :text="item.label" :plain="selectedHotTag === item.value?false:true"
							:type="selectedHotTag === item.value ? 'success' : 'info'"
							@click="handleHotTagClick(item.value)" />
					</view>
				</view>
			</view>

			<view v-if="curStyle==1">
				<view class="m-top-20 stitle">
					选择风格标签（可多选）
				</view>
				<view class="tag-list">
					<view class="tag-list-item" v-for="(item, index) in customTagList" :key="index">
						<u-tag :text="item.label" :plain="selectedCustomTags.includes(item.value)?false:true"
							:type="selectedCustomTags.includes(item.value) ? 'success' : 'info'"
							@click="handleCustomTagClick(item.value)" />
					</view>
				</view>
			</view>

			<view class="padding-20">
				<view class="">
					<view class="flex al-center">
						<view class="stitle">
							文案长度
						</view>
						<view class="" style="flex:1;">
							<u-slider v-model="textLen" min="100" max="1000" activeColor="#4cd964"></u-slider>
						</view>
					</view>
					<view class="color-green">
						{{textLen}}字
					</view>
				</view>

			</view>



		</view>
		<view class="btns m-top-40">
			<view class="btn">
				<u-button type="primary" plain @click="pasteLink">粘贴</u-button>
			</view>
			<view class="btn">
				<u-button type="primary" @click="getText">自动改写</u-button>
			</view>
		</view>

		<pageLoading ref="loadingRef" :progress="progress" loadingText='文案改写中'></pageLoading>
	</view>
</template>

<script>
	export default {
		data() {
			return {
				progress: 0,
				textLen: 100,
				hotTagList: [{
						"label": "金枪大叔",
						"value": "金枪大叔"
					},
					{
						"label": "房琪",
						"value": "房琪"
					},
					{
						"label": "王家卫",
						"value": "王家卫"
					},
					{
						"label": "多余和毛毛姐",
						"value": "多余和毛毛姐"
					},
					{
						"label": "樊登读书",
						"value": "樊登读书"
					},
					{
						"label": "无穷小亮",
						"value": "无穷小亮"
					},
					{
						"label": "李永乐老师",
						"value": "李永乐老师"
					},
					{
						"label": "雷军",
						"value": "雷军"
					},
					{
						"label": "Rapi酱",
						"value": "Rapi酱"
					},
					{
						"label": "罗翔系",
						"value": "罗翔系"
					},
					{
						"label": "崔磊系",
						"value": "崔磊系"
					}
				],
				customTagList: [{
						"label": "犀利毒舌",
						"value": "犀利毒舌"
					},
					{
						"label": "通俗易懂",
						"value": "通俗易懂"
					},
					{
						"label": "轻松幽默",
						"value": "轻松幽默"
					},
					{
						"label": "爹味说教",
						"value": "爹味说教"
					},
					{
						"label": "温柔委婉",
						"value": "温柔委婉"
					},
					{
						"label": "专业名词",
						"value": "专业名词"
					},
					{
						"label": "严肃谨慎",
						"value": "严肃谨慎"
					},
					{
						"label": "女友系",
						"value": "女友系"
					},
					{
						"label": "讲故事",
						"value": "讲故事"
					},
					{
						"label": "夸张",
						"value": "夸张"
					},
					{
						"label": "恐吓",
						"value": "恐吓"
					},
					{
						"label": "逻辑清晰",
						"value": "逻辑清晰"
					},
					{
						"label": "多列数据",
						"value": "多列数据"
					},
					{
						"label": "多举例子",
						"value": "多举例子"
					},
					{
						"label": "多做类比",
						"value": "多做类比"
					},
					{
						"label": "天马行空",
						"value": "天马行空"
					}
				],
				selectedHotTag: '', // 单选存储的值
				selectedCustomTags: [], // 多选存储的数组
				curStyle: 0,
				styleList: ["用热门风格", "自定义风格"],
				rewrite: false,
				value: ''
			}
		},
		methods: {
			goPage() {
				uni.navigateTo({
					url: "/pages/rewriteTextHis/rewriteTextHis"
				})
			},
			styleChange(e) {
				this.curStyle = e
			},
			getText() {
				if (!this.value) {
					uni.showToast({
						icon: "none",
						title: "没有文案内容"
					})
					return
				}

				// 检查是否选择了标签
				if (this.rewrite) {
					if (this.curStyle === 0 && !this.selectedHotTag) {
						uni.showToast({
							icon: "none",
							title: "请选择一个热门风格"
						})
						return
					}

					if (this.curStyle === 1 && this.selectedCustomTags.length === 0) {
						uni.showToast({
							icon: "none",
							title: "请至少选择一个自定义风格"
						})
						return
					}
				}

				// 准备请求参数
				const params = {
					text: this.value,
					rewrite: this.rewrite,
					styleType: this.curStyle,
					styleValue: this.curStyle === 0 ? this.selectedHotTag : this.selectedCustomTags.join(',')
				}

				console.log('请求参数:', params)

				this.$refs.loadingRef.open()
				setTimeout(() => {
					this.progress = 100
					setTimeout(() => {
						this.$refs.loadingRef.close()
						uni.navigateTo({
							url: "/pages/rewriteTextRes/rewriteTextRes"
						})
					}, 500)
				}, 1000)


				// 这里调用接口请求
				// this.$api.rewriteText(params).then(res => {
				//   // 处理结果
				// })
			},
			pasteLink() {
				uni.getClipboardData({
					success: (res) => {
						if (res.data) {
							this.value = res.data
						} else {
							uni.showToast({
								icon: 'none',
								title: '未检测到粘贴内容，请重新复制，再试一试粘贴哦'
							})
						}
					},
					fail: () => {
						uni.showToast({
							title: '粘贴失败'
						})
					}
				})
			},
			clearContent() {
				this.value = ''
			},
			// 处理热门标签点击（单选）
			handleHotTagClick(value) {
				if (this.selectedHotTag === value) {
					this.selectedHotTag = '' // 取消选择
				} else {
					this.selectedHotTag = value // 选择新标签
				}
			},
			// 处理自定义标签点击（多选）
			handleCustomTagClick(value) {
				const index = this.selectedCustomTags.indexOf(value)
				if (index > -1) {
					// 已存在则移除
					this.selectedCustomTags.splice(index, 1)
				} else {
					// 不存在则添加
					this.selectedCustomTags.push(value)
				}
			}
		}
	}
</script>

<style scoped lang="scss">
	.stitle {
		font-size: 28rpx;
	}

	.title {
		padding: 0 20rpx;

		.his {
			font-size: 24rpx;
		}
	}

	.btns {
		display: flex;
		justify-content: space-between;
		padding: 0 20rpx;
		gap: 20rpx;
	}

	.btn {
		flex: 1;
	}



	.tag-list {
		display: flex;
		flex-wrap: wrap;
		margin-top: 20rpx;
	}

	.tag-list-item {
		margin: 10rpx;
	}
</style>