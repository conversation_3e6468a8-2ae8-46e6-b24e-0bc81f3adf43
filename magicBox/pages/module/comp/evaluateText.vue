<template>
	<view style="padding-bottom: 60rpx;">
		<view class="title m-top-20 flex al-center jc-between">
			<view class="">
				{{step==1?'您想测评的文案':'您想发布的平台类型'}}
			</view>
			<view class="font-color-info his" @click="goPage">
				历史记录>
			</view>
		</view>

		<template v-if="step==1">
			<view class="flex al-center" style="margin-top: -10rpx;">
				<textareaBox class="flex1" height="400" v-model="formData.text" placeholder="请粘贴或者输入您想要测评的文案" count
					maxlength="1000">
				</textareaBox>
			</view>



		</template>

		<template v-else>
			<view class="padding-20">
				<u-subsection :list="platformList" mode="subsection" :current="formData.curPlatform"
					activeColor="#4cd964" @change="platFormChange"></u-subsection>
			</view>

			<view class="padding-20">
				<view class="">
					您的粉丝画像
				</view>
				<view class="flex jc-center al-center m-top-40 padding-lr-20">
					<view class="color-blue font-size-28" style="width: 140rpx;">
						粉丝性别：
					</view>
					<view class="flex1">
						<u-radio-group activeColor="#7A60FF" v-model="formData.sex" size="14" label-size="11"
							placement="row">
							<view class="m-right-20">
								<u-radio label="男性居多" name="1"></u-radio>
							</view>
							<view class="m-right-20">
								<u-radio label="女性居多" name="2"></u-radio>
							</view>
							<u-radio label="男女通吃" name="3"></u-radio>
						</u-radio-group>
					</view>
				</view>
				<view class="flex jc-center al-center m-top-40 padding-lr-20">
					<view class="color-blue font-size-28" style="width: 140rpx;">
						粉丝年龄：
					</view>
					<view class=" flex1">
						<u-checkbox-group v-model="formData.age" placement="row" activeColor="#7A60FF" size="14"
							label-size="11">
							<view class="flex gap-10 flex-wrap ">
								<view class="m-right-20 m-top-20" v-for="item in ageList" :key="item.name">
									<u-checkbox :label="item.label" :name="item.name"></u-checkbox>
								</view>
							</view>

						</u-checkbox-group>

					</view>
				</view>
			</view>


			<view class="padding-lr-20 m-top-40 margin-bottom-20">
				本次文案目标
			</view>
			<view class="padding-20 border padding-20 margin-lr-20">
				<view>
					<view class="m-top-20 stitle color-blue">
						本次文案的目标是
					</view>
					<view class="tag-list">
						<view class="tag-list-item" v-for="(item, index) in targetList" :key="index">
							<u-tag :text="item.label" :plain="selectedTargetTags.includes(item.value)?false:true"
								:type="selectedTargetTags.includes(item.value) ? 'success' : 'info'"
								@click="handleTargetTagClick(item.value)" />
						</view>
					</view>
				</view>

				<view>
					<view class="m-top-20 stitle color-blue">
						想要引导的用户行为是
					</view>
					<view class="tag-list">
						<view class="tag-list-item" v-for="(item, index) in customTagList" :key="index">
							<u-tag :text="item.label" :plain="selectedCustomTags.includes(item.value)?false:true"
								:type="selectedCustomTags.includes(item.value) ? 'success' : 'info'"
								@click="handleCustomTagClick(item.value)" />
						</view>
					</view>
				</view>

			</view>
			<view class="padding-lr-20 m-top-40 margin-bottom-20">
				您平时的文案风格
			</view>
			<textareaBox height="100" v-model="formData.textStyle" placeholder="请输入您平时的文案风格，以便精准的评测该文案是否契合您平时的文案风格"
				count maxlength="100">
			</textareaBox>

		</template>


		<view class="btns m-top-80">
			<view class="btn" v-if="step==1">
				<u-button type="primary" @click="nextStep">下一步</u-button>
			</view>
			<view class="btn" v-if="step==2">
				<u-button type="primary" plain @click="step=1">上一步</u-button>
			</view>
			<view class="btn" v-if="step==2">
				<u-button type="primary" @click="getText">立即生成</u-button>
			</view>
		</view>

		<pageLoading ref="loadingRef" :progress="progress" loadingText='文案评测中'></pageLoading>
	</view>
</template>

<script>
	export default {
		data() {
			return {
				formData: {
					text: '',
					curPlatform: 0,
					sex: '',
					age: [],
					textStyle: ''
				},
				ageList: [{
						label: '18以下',
						name: '1',
					},
					{
						label: '18-23',
						name: '2',
					},
					{
						label: '24-30',
						name: '3',
					},
					{
						label: '31-40',
						name: '4',
					},
					{
						label: '41-50',
						name: '5',
					},
					{
						label: '50以上',
						name: '6',
					},
				],

				platformList: ['短视频文案', '小红书文案', '长视频文案'],
				step: 2,
				progress: 0,
				targetList: [{
						"label": "种草",
						"value": "种草"
					},
					{
						"label": "涨粉",
						"value": "涨粉"
					},
					{
						"label": "软营销、塑造价值",
						"value": "软营销、塑造价值"
					},
					{
						"label": "吸引垂直受众",
						"value": "吸引垂直受众"
					},
					{
						"label": "吸引泛流量扩大流量池",
						"value": "吸引泛流量扩大流量池"
					},
					{
						"label": "拉近与粉丝距离",
						"value": "拉近与粉丝距离"
					},
					{
						"label": "塑造人设背书和权威性",
						"value": "塑造人设背书和权威性"
					},
				],
				customTagList: [{
						"label": "点赞",
						"value": "点赞"
					},
					{
						"label": "收藏",
						"value": "收藏"
					},
					{
						"label": "关注",
						"value": "关注"
					},
					{
						"label": "转发",
						"value": "转发"
					},
					{
						"label": "私信",
						"value": "私信"
					},
				],
				selectedCustomTags: [], // 多选存储的数组
				selectedTargetTags: [], // 多选存储的数组
				curStyle: 0,
				styleList: ["用热门风格", "自定义风格"],
				rewrite: false,
				value: ''
			}
		},
		methods: {
			nextStep() {
				if (!this.formData.text) {
					uni.showToast({
						title: '请粘贴或输入想要测评的文案',
						icon: 'none'
					})
					return
				}
				this.step = 2
			},
			goPage() {
				uni.navigateTo({
					url: "/pages/evaluateTextHis/evaluateTextHis"
				})
			},
			styleChange(e) {
				this.curStyle = e
			},
			platFormChange(e) {
				this.formData.curPlatform = e
			},
			getText() {
				//表单验证

				if (!this.formData.text) {
					uni.showToast({
						title: '请输入要测评的文案',
						icon: 'none'
					});
					return;
				}

				if (!this.formData.sex) {
					uni.showToast({
						title: '请选择粉丝性别',
						icon: 'none'
					});
					return;
				}

				if (this.formData.age.length === 0) {
					uni.showToast({
						title: '请选择粉丝年龄段',
						icon: 'none'
					});
					return;
				}

				if (this.selectedTargetTags.length === 0) {
					uni.showToast({
						title: '请选择文案目标',
						icon: 'none'
					});
					return;
				}

				if (this.selectedCustomTags.length === 0) {
					uni.showToast({
						title: '请选择想要引导的用户行为',
						icon: 'none'
					});
					return;
				}


				// if (!this.formData.textStyle) {
				// 	uni.showToast({
				// 		title: '请输入您平时的文案风格',
				// 		icon: 'none'
				// 	});
				// 	return;
				// }



				// 验证通过，准备请求数据
				const params = {
					text: this.formData.text,
					platform: this.platformList[this.formData.curPlatform],
					sex: this.formData.sex,
					age: this.formData.age,
					targets: this.selectedTargetTags,
					behaviors: this.selectedCustomTags,
					textStyle: this.formData.textStyle
				};

				console.log('请求参数:', params);

				this.$refs.loadingRef.open()
				setTimeout(() => {
					this.progress = 100
					setTimeout(() => {
						this.$refs.loadingRef.close()
						uni.navigateTo({
							url: "/pages/evaluateTextRes/evaluateTextRes"
						})
					}, 500)
				}, 1000)


				// 这里调用接口请求
				// this.$api.rewriteText(params).then(res => {
				//   // 处理结果
				// })
			},
			pasteLink() {
				uni.getClipboardData({
					success: (res) => {
						if (res.data) {
							this.value = res.data
						} else {
							uni.showToast({
								icon: 'none',
								title: '未检测到粘贴内容，请重新复制，再试一试粘贴哦'
							})
						}
					},
					fail: () => {
						uni.showToast({
							title: '粘贴失败'
						})
					}
				})
			},
			clearContent() {
				this.value = ''
			},
			// 处理热门标签点击
			handleTargetTagClick(value) {
				const index = this.selectedTargetTags.indexOf(value)
				if (index > -1) {
					// 已存在则移除
					this.selectedTargetTags.splice(index, 1)
				} else {
					// 不存在则添加
					this.selectedTargetTags.push(value)
				}
			},
			// 处理自定义标签点击（多选）
			handleCustomTagClick(value) {
				const index = this.selectedCustomTags.indexOf(value)
				if (index > -1) {
					// 已存在则移除
					this.selectedCustomTags.splice(index, 1)
				} else {
					// 不存在则添加
					this.selectedCustomTags.push(value)
				}
			}
		}
	}
</script>

<style scoped lang="scss">
	.num {
		border: 1px solid $uni-color-blue;
		border-radius: 50%;
		width: 30rpx;
		height: 30rpx;
		line-height: 30rpx;
		text-align: center;
	}

	.stitle {
		font-size: 28rpx;
	}

	.title {
		padding: 0 20rpx;

		.his {
			font-size: 24rpx;
		}
	}

	.btns {
		display: flex;
		justify-content: space-between;
		padding: 0 20rpx;
		gap: 20rpx;
	}

	.btn {
		flex: 1;
	}



	.tag-list {
		display: flex;
		flex-wrap: wrap;
		margin-top: 20rpx;
	}

	.tag-list-item {
		margin: 10rpx;
	}
</style>