<template>
	<view class="padding-bottom-60">
		<view class="rewrite-res ">
			<view class="flex al-center jc-between padding-lr-20" style="padding-top: 20rpx;">
				<view class="">
					文案综合评分：<text class="color-red"> 96 分</text>
				</view>
				<view class="">
					等级：<text class="color-red"> 96 分</text>
				</view>
			</view>

			<view class="padding-20">
				<TreeTable :data="tableData" />
			</view>
			<view class="flex flex-wrap gap-10  padding-20">
				<view class="flex1">
					<u-button type="primary" plain>违禁词检测</u-button>
				</view>
				<view class="flex1">
					<u-button type="primary">文案改写</u-button>
				</view>
			</view>

			<view class="flex flex-wrap gap-10 padding-20">
				<view class="flex1">
					<u-button type="primary" plain>文案配音</u-button>
				</view>
				<view class="flex1">
					<u-button type="primary" plain>爆款文案生成</u-button>
				</view>
			</view>


		</view>

		<view class="m-top-40">
			<share></share>
		</view>
	</view>

</template>

<script>
	import {
		copyText
	} from '@/utils/index.js';
	import TreeTable from '@/components/xlh-table/xlh-table.vue';
	export default {
		components: {
			TreeTable
		},
		data() {
			return {
				columns: [{
						title: '部门名称',
						key: 'name'
					},
					{
						title: '负责人',
						key: 'leader'
					},
					{
						title: '人数',
						key: 'count'
					}
				],
				tableData: [{
						type: 'header',
						content: '创作目标匹配度（满分20分）',
						rowspan: 8
					},
					{
						content: '文案整体内容、话术是否与本次创作的目标一致，如种草、引流、吸引垂直受众。'
					},
					{
						score: 20
					},
					{
						type: 'header',
						content: '行为引导匹配度（满分5分）'
					},
					{
						content: '文案内容中是否有用户行为引导的相关文案提示或钩子。'
					},
					{
						score: 5
					},
					// 其他数据...
					{
						type: 'score',
						content: '总分',
						colspan: 2
					},
					{
						score: 96
					}
				],
				curTabIndex: 0,
				value: '',

			}
		},
		onLoad(p) {
			if (p.id) {
				uni.setNavigationBarTitle({
					title: '文案评测详情'
				})
			} else {
				//识别进来
				console.log('识别进来');
			}



		},
		methods: {
			tabChange(e) {
				this.curTabIndex = e.index
			},
			// goPage() {
			// 	uni.navigateTo({
			// 		url: "/pages/"
			// 	})
			// },
			clearContent() {
				this.value = ''
			},
			copyText() {
				copyText(this.value)
			}

		}
	}
</script>

<style lang="scss" scoped>
	.rewrite-res {}
</style>