<template>
	<view class="textarea-box">
		<u--textarea :height="height" :value="innerValue" @input="input" :placeholder="placeholder"
			:maxlength="maxlength" :count="count"></u--textarea>
		<view class="clear" @click.stop="clearContent">清空</view>
	</view>
</template>

<script>
	export default {
		name: "textarea-box",
		props: {
			value: {
				type: String,
				default: ''
			},
			height: {
				type: [Number, String],
				default: 160
			},
			maxlength: {
				type: [Number, String],
				default: 200
			},
			placeholder: {
				type: String,
				default: '请输入内容'
			},
			count: {
				type: Boolean,
				default: true
			}
		},
		data() {
			return {
				innerValue: ''

			};
		},
		watch: {
			value: {
				immediate: true,
				handler(n) {
					// 仅当外部值变化且与内部值不同时更新
					if (n !== this.innerValue) {
						this.innerValue = n;
					}
				}
			}
		},
		methods: {
			input(v) {
				this.innerValue = v;
				this.$emit('input', v);
			},
			clearContent() {
				this.innerValue = '';
				this.$emit('input', '');
			}
		}
	}
</script>

<style lang="scss" scoped>
	.textarea-box {
		margin: 20rpx;
		border: 1px solid #bbbbbb;
		position: relative;

		.clear {
			color: red;
			position: absolute;
			bottom: 0rpx;
			right: 160rpx;
			font-size: 24rpx;
			z-index: 100;
			padding: 6rpx;
		}
	}
</style>