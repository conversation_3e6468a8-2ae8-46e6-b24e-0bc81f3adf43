<template>
	<view class="">
		<view class="bg-white radius shadow padding-20 m-top-20 font-size-28" v-for="item in textList" :key="item.id">
			<view class="title color-black bold margin-bottom-20" v-if="showTitle">
				重组生成 <text class="num">{{item.num || 0}}</text> 条文案
			</view>
			<view class="of-2">
				{{item.content}}
			</view>
			<view class="flex jc-between al-center m-top-20">
				<view class="font-color-info font-size-24">
					{{item.time}}
				</view>
				<view class="flex al-center ">
					<view class="m-right-20">
						<u-button type="primary" size="small" plain @click="del(item.id)">删除</u-button>
					</view>
					<view class="m-right-20">
						<u-button type="primary" size="small" :plain="showCopy?true:false"
							@click="detail(item.id,item)">详情</u-button>
					</view>
					<u-button v-if="showCopy" type="primary" size="small"
						@click="copyText(item.content)">复制全部</u-button>
				</view>
			</view>
		</view>
	</view>

</template>

<script>
	import {
		copyText
	} from '@/utils/index.js';
	export default {
		name: "textList",
		props: {
			showCopy: {
				type: Boolean,
				default: true
			},
			showTitle: {
				type: Boolean,
				default: false,
			},
			textList: {
				type: Array,
				default: () => []
			}
		},
		data() {
			return {

			};
		},
		methods: {
			detail(id) {
				this.$emit('detail', id)
			},
			del(id) {
				uni.showModal({
					title: '提示',
					content: '确认删除此条记录吗？',
					success: (res) => {
						if (res.confirm) {
							this.$emit('del', id)
						}

					}
				})
			},
			copyText: copyText,
		}
	}
</script>

<style lang="scss" scoped>
	.num {
		color: $uni-color-green;
		padding: 0 10rpx;
	}
</style>