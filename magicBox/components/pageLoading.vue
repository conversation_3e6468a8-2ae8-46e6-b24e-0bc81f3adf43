<template>
	<view v-if="show" class="loading-container">
		<view class="loading-text ">{{loadingText}}...</view>
		<view class="loading-progress">{{progress}}%</view>
		<view class="loading-description">
			独家AI算法，量身定制<br>
			符合您账号定位的爆款
		</view>
		<share></share>
	</view>
</template>

<script>
	export default {
		props: {
			progress: {
				type: Number,
				default: 0
			},
			loadingText: {
				type: String,
				default: '加载中'
			}
		},
		data() {
			return {
				show: false, // 控制loading显示与隐藏
			};
		},
		methods: {
			open() {
				this.show = true;
			},
			close() {
				this.show = false;
			},
		},
	};
</script>

<style scoped lang="scss">
	.loading-container {
		position: fixed;
		z-index: 9999;
		top: 0;
		left: 0;
		width: 100vw;
		height: 100vh;
		background-color: rgba(255, 255, 255, 1);
		padding-top: 40rpx;
		/* display: flex;
		flex-direction: column;
		justify-content: center; */
		/* align-items: center; */
		text-align: center;
	}

	.loading-text {
		font-size: 24px;
		color: $uni-color-blue;
	}

	.loading-progress {
		font-size: 36px;
		color: $uni-color-blue;
		margin-bottom: 20px;
		margin-top: 60rpx;
	}

	.loading-description {
		font-size: 16px;
		color: #333;
		margin-bottom: 100rpx;
	}
</style>