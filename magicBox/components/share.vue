<template>
	<view class="share-page">
		<!-- 遮罩层 -->
		<view class="mask" v-if="showInfo" @click="showInfo = false"></view>

		<!-- 提示信息 -->
		<view class="info" :class="{active: showInfo}">
			<view class="triangle"></view>
			点击右上角更多按钮，分享至朋友圈邀请更多朋友体验~
		</view>

		<view class="tip-text">
			分享小程序可获得奖励哦~
		</view>

		<view class="flex al-center jc-between boxs m-top-40">
			<view class="box" @click="goPage">
				<view class="icon">
					<image src="/static/gzh.png" mode=""></image>
				</view>
				<view class="font-color-info">
					关注公众号
				</view>
				<view class="num">
					+10次
				</view>
			</view>
			<view class="box" @click="shareToFriends">
				<view class="icon">
					<image src="/static/pyq.png" mode=""></image>
				</view>
				<view class="font-color-info">
					分享到朋友圈
				</view>
				<view class="num">
					+10次
				</view>
			</view>
			<view class="box" @click="shareToWx" style="position: relative;">
				<button open-type="share" bindsuccess="onShareSuccess" bindfail="onShareFail"
					style="opacity: 0;background: red;width: 180rpx;position: absolute;height: 160rpx;">分享给好友</button>
				<view class="icon">
					<image src="/static/wx.png" mode=""></image>
				</view>
				<view class="font-color-info">
					分享给好友/群
				</view>
				<view class="num">
					+10次
				</view>
			</view>
		</view>
	</view>
</template>

<script>
	export default {
		name: "share",
		data() {
			return {
				showInfo: false
			};
		},
		methods: {
			// 分享成功回调
			onShareSuccess(res) {
				console.log('分享成功', res)
				wx.showToast({
					title: '分享成功',
					icon: 'success'
				})
			},

			// 分享失败回调
			onShareFail(err) {
				console.error('分享失败', err)
				wx.showToast({
					title: '分享失败',
					icon: 'none'
				})
			},

			shareToWx() {
				// 原有逻辑
			},

			shareToFriends() {
				this.showInfo = true;
			},

			goPage() {
				uni.navigateTo({
					url: '/pages/publicNum/publicNum'
				})
			}
		}
	}
</script>

<style lang="scss" scoped>
	.share-page {
		text-align: center;
		border: 1px solid #999;
		border-radius: 10rpx;
		padding: 20rpx;
		margin: 20rpx;
		position: relative;

		.tip-text {
			color: #666;
		}

		/* 遮罩样式 */
		.mask {
			position: fixed;
			top: 0;
			left: 0;
			right: 0;
			bottom: 0;
			background: rgba(0, 0, 0, 0.5);
			z-index: 98;
		}

		/* 提示信息样式 */
		.info {
			position: fixed;
			top: -100%;
			/* 初始位置在屏幕外 */
			background: rgb(80, 97, 255);
			color: #fff;
			border-radius: 10rpx;
			width: 96vw;
			left: 50%;
			transform: translateX(-50%);
			padding: 30rpx;
			z-index: 99;
			transition: all 0.3s ease;
			box-shadow: 0 4rpx 12rpx rgba(0, 0, 0, 0.15);

			/* 三角箭头 */
			.triangle {
				position: absolute;
				top: -16rpx;
				right: 100rpx;
				width: 0;
				height: 0;
				border-left: 16rpx solid transparent;
				border-right: 16rpx solid transparent;
				border-bottom: 16rpx solid rgb(80, 97, 255);
			}

			&.active {
				top: 20rpx;
				/* 显示时位置 */
			}
		}

		.boxs {
			gap: 10rpx;
			font-size: 24rpx;
			margin-top: 40rpx;

			.box {
				width: 33%;
				padding: 20rpx 0;
				border-radius: 12rpx;
				transition: all 0.2s;



				.icon {
					image {
						width: 60rpx;
						height: 60rpx;
					}
				}

				.num {
					color: $uni-color-blue;
					margin-top: 10rpx;
					font-weight: bold;
				}
			}
		}
	}
</style>