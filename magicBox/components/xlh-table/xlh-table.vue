<template>
	<view class="mpopup">
		<view class="mpopup-body">
			<!-- 表头 -->
			<view class="mpopup-body-thead">
				<view class="mpopup-body-thead-name">评测维度</view>
				<view class="mpopup-body-thead-other">
					<view class="mpopup-body-thead-other-unit">评分标准</view>
					<view class="mpopup-body-thead-other-notAudit">得分</view>
				</view>
			</view>
			<!-- 表体 -->
			<view class="mpopup-body-tbody" v-if="divisionSettingList.length>0">
				<view v-for="(item,index) in divisionSettingList" class="mpopup-body-tbody-tr">
					<view class="mpopup-body-tbody-tr-name">
						<view class="">
							{{item.name}}
						</view>
						<view class="" v-if="item.fullScore">
							（{{item.fullScore}}分）
						</view>
					</view>
					<view class="mpopup-body-tbody-tr-other">
						<view class="mpopup-body-tbody-tr-other-box" v-for="(items,indexs) in item.infoList">
							<view class="mpopup-body-tbody-tr-other-box-unit">{{items.standard}}</view>
							<view class="mpopup-body-tbody-tr-other-box-notAudit">
								{{items.score}}
							</view>
						</view>
					</view>
				</view>
			</view>
		</view>
	</view>
</template>

<script>
	export default {
		name: "xlh-table",
		data() {
			return {
				divisionSettingList: [{
						"name": "创作目标匹配度",
						"fullScore": 20,
						"infoList": [{
							"standard": "文案整体内容、话术是否与本次创作的目标一致，如种草、引流、吸引垂直受众。",
							"score": 13,

						}]
					},
					{
						"name": "传播潜力",
						"fullScore": 30,
						"infoList": [{
								"standard": "核心内容(观点)的逻辑是否清晰明了语言是否准确、流畅、通俗易懂，符合大众习惯。",
								"score": 3,

							},
							{
								"standard": "是否能激发大众的情绪，如带来感动、共鸣、认同、开心等。",
								"score": 3,

							},
						]
					},
					{
						"name": "总分",
						"infoList": [{
							"standard": "90-100为优秀，80-89为良好，60-79为般，小于60待改进。",
							"score": 19,
						}]
					},
				]

			}
		}
	}
</script>

<style lang="less" scoped>
	.mpopup {
		// width: 100%;
		// height: 100vh;

		display: flex;
		justify-content: center;
		align-items: center;

		&-body {
			width: 100%;
			height: 100%;
			box-sizing: border-box;
			background: #fff;
			overflow: auto;
			// border-radius: 2%;

			&-close {
				width: 100%;
				text-align: right;
				padding-bottom: 20rpx;
			}

			&-thead {
				width: 100%;
				display: flex;
				justify-content: space-between;
				line-height: 60rpx;
				border-bottom: 1rpx solid #d6d6d6;
				color: #7A60FF;
				font-size: 30rpx;
				font-weight: bold;
				background: #e2e2e2;

				&-name {
					width: 25%;
					text-align: center;
					border-top: 1rpx solid #d6d6d6;
					border-left: 1rpx solid #d6d6d6;
					padding: 20rpx;
				}

				&-other {
					width: 75%;
					display: flex;
					justify-content: space-between;

					&-unit {
						width: 70%;
						text-align: center;
						border-left: 1rpx solid #d6d6d6;
						border-top: 1rpx solid #d6d6d6;
						padding: 20rpx;
					}

					&-notAudit {
						width: 30%;
						text-align: center;
						border-left: 1rpx solid #d6d6d6;
						border-top: 1rpx solid #d6d6d6;
						border-right: 1rpx solid #d6d6d6;
						padding: 20rpx;
					}

					// &-timeOut {
					// 	width: 20%;
					// 	text-align: center;
					// 	border-left: 1rpx solid #d6d6d6;
					// 	border-top: 1rpx solid #d6d6d6;
					// }

					// &-accumulative {
					// 	width: 20%;
					// 	text-align: center;
					// 	border-left: 1rpx solid #d6d6d6;
					// 	border-right: 1rpx solid #d6d6d6;
					// 	border-top: 1rpx solid #d6d6d6;
					// }
				}
			}

			&-tbody {
				width: 100%;
				font-size: 28rpx;

				&-tr {
					width: 100%;
					display: flex;
					justify-content: space-between;

					&-count {
						width: 51.6%;
						text-align: center;
						line-height: 60rpx;
						border-left: 1rpx solid #d6d6d6;
						border-right: 1rpx solid #d6d6d6;
						border-bottom: 1rpx solid #d6d6d6;
					}

					&-others {
						width: 48%;
						display: flex;
						flex-direction: row;
						line-height: 60rpx;

						&-notAudit {
							width: 33.3%;
							text-align: center;
							border-right: 1rpx solid #d6d6d6;
							border-bottom: 1rpx solid #d6d6d6;
						}

						&-timeOut {
							width: 33.3%;
							text-align: center;
							border-right: 1rpx solid #d6d6d6;
							border-bottom: 1rpx solid #d6d6d6;
						}

						&-accumulative {
							width: 33.3%;
							text-align: center;
							border-right: 1rpx solid #d6d6d6;
							border-bottom: 1rpx solid #d6d6d6;
						}
					}

					&-name {
						width: 25%;
						word-break: break-word;
						display: flex;
						justify-content: center;
						align-items: center;
						flex-direction: column;
						border: 1rpx solid #d6d6d6;
						border-top: none;
						padding: 10rpx;

					}

					&-other {
						width: 75%;
						display: flex;
						flex-direction: column;
						border-right: 1rpx solid #d6d6d6;

						&-box {
							width: 100%;
							line-height: 40rpx;
							display: flex;
							justify-content: space-between;
							border-bottom: 1rpx solid #d6d6d6;

							&-unit {
								width: 70%;
								text-align: left;
								border-right: 1rpx solid #d6d6d6;
								padding: 10rpx;
							}

							&-notAudit {
								width: 30%;
								text-align: center;
								padding: 10rpx;
								align-self: center;
							}

							// &-timeOut {
							// 	width: 20%;
							// 	text-align: center;
							// 	border-right: 1rpx solid #d6d6d6;
							// }

							// &-accumulative {
							// 	width: 20%;
							// 	text-align: center;
							// 	border-right: 1rpx solid #d6d6d6;
							// }
						}
					}
				}
			}
		}

	}
</style>