export function uploadFile() {

}

// 格式化时间显示
export function formatTime(seconds) {
	const min = Math.floor(seconds / 60);
	const sec = Math.floor(seconds % 60);
	return `${min.toString().padStart(2, '0')}:${sec.toString().padStart(2, '0')}`;
}

export function copyText(text) {
	if (!text) {
		uni.showToast({
			title: '没有可复制的文案',
			icon: "none"
		})
		return
	}
	uni.setClipboardData({
		data: text,
		success: () => {
			uni.showToast({
				title: '文案已复制',
				icon: "none"
			})
		},
		fail: () => {
			uni.showToast({
				title: '文案复制失败',
				icon: "none"
			})
		}
	})
}