import App from './App'

// #ifndef VUE3
import Vue from 'vue'
import uView from './uni_modules/uview-ui';
import share from './mixin/share';
Vue.mixin(share)
Vue.use(uView);
import './uni.promisify.adaptor'
Vue.config.productionTip = false
App.mpType = 'app'
const app = new Vue({
	...App
})
app.$mount()
// #endif

// #ifdef VUE3
import {
	createSSRApp
} from 'vue'
export function createApp() {
	const app = createSSRApp(App)
	return {
		app
	}
}
// #endif