<script>
	export default {
		onLaunch: function() {
			console.log('App Launch')
		},
		onShow: function() {
			console.log('App Show')
		},
		onHide: function() {
			console.log('App Hide')
		},
	}
</script>

<style lang="scss">
	/*每个页面公共css */

	view {
		padding: 0;
		margin: 0;
		box-sizing: border-box;
	}

	/* #ifdef MP-WEIXIN */
	scroll-view ::v-deep ::-webkit-scrollbar {
		display: none;
		width: 0 !important;
		height: 0 !important;
		-webkit-appearance: none;
		background: transparent;
	}

	/* #endif */

	.relative {
		position: relative;
	}

	.absolute {
		position: absolute;
	}

	.border {
		border: 1px solid #bbbbbb;
	}

	.flex {
		display: flex;
	}

	.al-center {
		align-items: center;
	}

	.jc-center {
		justify-content: center;
	}

	.jc-between {
		justify-content: space-between;
	}

	.flex1 {
		flex: 1;
	}

	.flex-wrap {
		flex-wrap: wrap;
	}

	.gap-10 {
		gap: 10rpx;
	}

	.bg-white {
		background: #fff;
	}

	.shadow {
		box-shadow: 1px 1px 10px 2px #ededed;
	}

	.padding-bottom-60 {
		padding-bottom: 60rpx;
	}

	.padding-bottom-20 {
		padding-bottom: 20rpx;
	}

	.padding-lr-20 {
		padding-left: 20rpx;
		padding-right: 20rpx;
	}

	.margin-bottom-20 {
		margin-bottom: 20rpx;
	}

	.margin-lr-20 {
		margin-left: 20rpx;
		margin-right: 20rpx;
	}

	.m-top-20 {
		margin-top: 20rpx;
	}

	.m-top-40 {
		margin-top: 40rpx;
	}

	.m-top-80 {
		margin-top: 80rpx;
	}

	.m-right-20 {
		margin-right: 20rpx;
	}

	.m-left-20 {
		margin-left: 20rpx;
	}

	.radius {
		border-radius: 10rpx;
	}

	.padding-20 {
		padding: 20rpx;
	}

	.font-size-24 {
		font-size: 24rpx;
	}

	.font-size-20 {
		font-size: 20rpx;
	}

	.font-size-28 {
		font-size: 28rpx;
	}

	.font-color-info {
		color: $uni-text-color-grey
	}

	.color-white {
		color: #fff
	}

	.color-black {
		color: black;
	}

	.color-blue {
		color: $uni-color-blue;
	}

	.bg-blue {
		background: $uni-color-blue ;
	}


	.color-green {
		color: $uni-color-green;
	}

	.color-red {
		color: rgb(254, 74, 101);

	}

	.bg-green {
		background: $uni-color-green;
	}

	.bg-red {
		background: rgb(254, 74, 101);
	}

	.padding-10 {
		padding: 10rpx;
	}

	.bold {
		font-weight: bold;
	}

	.of-2 {
		overflow: hidden;
		/* 隐藏超出部分 */
		text-overflow: ellipsis;
		/* 显示省略号 */
		display: -webkit-box;
		/* 使用Webkit的box布局 */
		-webkit-line-clamp: 2;
		/* 限制显示两行 */
		-webkit-box-orient: vertical;
		/* 垂直排列内容 */
	}
</style>