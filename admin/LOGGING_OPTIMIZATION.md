# 日志优化说明

## 优化背景

从您提供的日志中发现了几个问题：
1. **日志重复冗余**：同一个异常被多次打印完整堆栈
2. **信息过于详细**：DEBUG级别的信息在INFO级别输出
3. **Apple Silicon Mac兼容性**：JAVE2缺少ARM64架构支持
4. **异常处理不够精细**：没有区分不同类型的转换失败

## 优化内容

### 1. 日志级别调整

#### 调整前
```java
log.info("音频信息检测完成: {}", info); // 输出完整对象信息
log.error("JAVE2转换失败", e); // 输出完整异常堆栈
```

#### 调整后
```java
log.info("音频格式检测: 格式={}, 采样率={}Hz, 声道={}, 时长={}ms, 是否支持={}", 
        sourceInfo.getFormat(), sourceInfo.getSampleRate(), sourceInfo.getChannels(), 
        sourceInfo.getDuration(), sourceInfo.isSupported());
log.debug("音频信息检测完成: {}", info); // 详细信息降级为DEBUG
log.error("JAVE2转换失败: {}", e.getMessage()); // 只输出错误消息，不输出堆栈
```

### 2. 异常处理优化

#### 针对JAVE2不可用的特殊处理
```java
if (e.getMessage() != null && e.getMessage().contains("ffmpeg")) {
    log.warn("JAVE2不可用，FFmpeg二进制文件缺失: {}", e.getMessage());
    throw new AudioConversionException("JAVE2转换器不可用，请尝试安装系统FFmpeg", e);
}
```

#### 减少重复异常日志
- 在最外层捕获异常时只记录简要信息
- 避免在异常传播过程中重复记录相同信息

### 3. Apple Silicon Mac支持

#### 添加依赖
```xml
<!-- Apple Silicon Mac支持 -->
<dependency>
    <groupId>ws.schild</groupId>
    <artifactId>jave-nativebin-osxm1</artifactId>
    <version>3.5.0</version>
</dependency>
```

#### 系统检测和提示
```java
String osArch = System.getProperty("os.arch");
String osName = System.getProperty("os.name");

if ("aarch64".equals(osArch) && osName.toLowerCase().contains("mac")) {
    log.warn("检测到Apple Silicon Mac，JAVE2可能不支持。建议安装Homebrew FFmpeg: brew install ffmpeg");
}
```

### 4. 转换器状态日志优化

#### 启动时的状态检查
```java
log.info("系统信息: OS={}, 架构={}", osName, osArch);
log.info("音频转换器可用性 - JAVE2: {}, FFmpeg: {}", javeAvailable, ffmpegAvailable);

if (!javeAvailable && ffmpegAvailable) {
    log.info("JAVE2不可用，将使用系统FFmpeg进行音频转换");
} else if (javeAvailable && !ffmpegAvailable) {
    log.info("FFmpeg不可用，将使用JAVE2进行音频转换");
} else {
    log.info("JAVE2和FFmpeg都可用，优先使用JAVE2");
}
```

#### 转换过程日志
```java
log.info("音频需要转换: {}", sourceInfo.getReason());
log.info("使用JAVE2进行音频转换");
log.info("JAVE2转换成功，耗时: {}ms", javeResult.getConversionTimeMs());
```

## 优化效果

### 日志输出对比

#### 优化前（冗余日志）
```
2025-07-22 21:21:53.648 [http-nio-8000-exec-1] INFO  org.yixz.common.converter.AudioFormatDetector - 音频信息检测完成: AudioInfo{format='MPEG-1 LAYER 3', sampleRate=48000, channels=Stereo, bitRate=77, duration=313000, isSupported=false, reason='不支持的采样率: 48000Hz; 不支持的声道数: Stereo(需要单声道); '}
2025-07-22 21:21:53.684 [http-nio-8000-exec-1] ERROR org.yixz.common.converter.AudioConverter - 音频编码失败
ws.schild.jave.EncoderException: java.io.IOException: Cannot run program...
[完整异常堆栈 - 60多行]
2025-07-22 21:21:53.688 [http-nio-8000-exec-1] ERROR org.yixz.service.AudioConversionService - JAVE2转换失败
org.yixz.common.exception.AudioConversionException: 音频编码失败...
[重复的异常堆栈 - 60多行]
```

#### 优化后（简洁日志）
```
2025-07-22 21:21:53.648 [http-nio-8000-exec-1] INFO  org.yixz.service.AudioConversionService - 音频格式检测: 格式=MPEG-1 LAYER 3, 采样率=48000Hz, 声道=Stereo, 时长=313000ms, 是否支持=false
2025-07-22 21:21:53.649 [http-nio-8000-exec-1] INFO  org.yixz.service.AudioConversionService - 音频需要转换: 不支持的采样率: 48000Hz; 不支持的声道数: Stereo(需要单声道);
2025-07-22 21:21:53.650 [http-nio-8000-exec-1] INFO  org.yixz.service.AudioConversionService - 使用JAVE2进行音频转换
2025-07-22 21:21:53.684 [http-nio-8000-exec-1] WARN  org.yixz.service.AudioConversionService - JAVE2不可用，FFmpeg二进制文件缺失: Cannot run program "/var/folders/.../ffmpeg-aarch64-3.5.0-osx"
2025-07-22 21:21:53.685 [http-nio-8000-exec-1] ERROR org.yixz.service.impl.AliyunServiceImpl - 音频转换失败: JAVE2转换器不可用，请尝试安装系统FFmpeg
```

### 优化收益

1. **日志量减少**: 从100+行减少到5-10行
2. **信息更清晰**: 关键信息一目了然
3. **问题定位更快**: 直接指出解决方案
4. **存储空间节省**: 减少日志文件大小
5. **性能提升**: 减少日志I/O开销

## 日志配置建议

### application.yml配置
```yaml
logging:
  level:
    # 音频转换相关日志
    org.yixz.service.AudioConversionService: INFO
    org.yixz.common.converter: INFO
    
    # 开发时可以开启DEBUG查看详细信息
    # org.yixz.common.converter.AudioFormatDetector: DEBUG
    # org.yixz.common.converter.AudioConverter: DEBUG
    
    # 第三方库日志控制
    ws.schild.jave: WARN
    
  pattern:
    console: "%d{yyyy-MM-dd HH:mm:ss.SSS} [%thread] %-5level %logger{36} - %msg%n"
    file: "%d{yyyy-MM-dd HH:mm:ss.SSS} [%thread] %-5level %logger{36} - %msg%n"
```

### 生产环境建议
```yaml
logging:
  level:
    root: WARN
    org.yixz: INFO
    # 只在出现问题时临时开启DEBUG
    # org.yixz.common.converter: DEBUG
```

## Apple Silicon Mac解决方案

### 1. 安装系统FFmpeg（推荐）
```bash
# 使用Homebrew安装
brew install ffmpeg

# 验证安装
ffmpeg -version
```

### 2. 使用Rosetta运行（临时方案）
```bash
# 在Rosetta模式下运行应用
arch -x86_64 java -jar your-app.jar
```

### 3. 等待JAVE2更新
JAVE2项目正在开发Apple Silicon支持，可以关注项目更新。

## 监控建议

### 关键指标
1. **转换成功率**: 监控音频转换的成功率
2. **转换器使用情况**: 统计JAVE2 vs FFmpeg的使用比例
3. **转换耗时**: 监控转换性能
4. **错误类型分布**: 统计不同错误的比例

### 告警设置
- 转换成功率低于90%时告警
- 连续转换失败超过10次时告警
- 平均转换时间超过阈值时告警

这些优化确保了日志的简洁性和可读性，同时提供了足够的信息用于问题诊断和系统监控。
