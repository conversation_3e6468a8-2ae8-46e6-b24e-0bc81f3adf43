# 音频转换功能说明

## 概述

为了支持阿里云ASR接口的音频格式要求，我们实现了一套完整的音频格式检测和转换系统。该系统能够自动检测音频格式，并在需要时将其转换为阿里云ASR兼容的格式。

## 阿里云ASR格式要求

### 支持的音频格式
- **PCM**: 原始音频数据
- **WAV**: PCM编码的WAV文件
- **OGG**: OGG封装的OPUS、SPEEX
- **AMR**: 自适应多速率编码
- **MP3**: MPEG音频层3
- **AAC**: 高级音频编码

### 技术规格
- **声道**: 单声道（mono）
- **采样位数**: 16 bit
- **采样率**: 8000 Hz 或 16000 Hz

## 解决方案架构

### 1. 音频格式检测 (`AudioFormatDetector`)
- 自动检测音频文件的格式、采样率、声道数等信息
- 验证是否符合阿里云ASR要求
- 提供转换建议

### 2. 音频转换器
#### JAVE2转换器 (`AudioConverter`)
- 基于FFmpeg的Java包装库
- 跨平台支持（Windows、Linux、macOS）
- 内置FFmpeg二进制文件，无需额外安装

#### FFmpeg命令行转换器 (`FFmpegAudioConverter`)
- 直接调用系统FFmpeg命令
- 作为JAVE2的备选方案
- 需要系统预装FFmpeg

### 3. 统一转换服务 (`AudioConversionService`)
- 自动选择最佳转换器
- 统一的API接口
- 自动资源管理和清理

## 依赖配置

### Maven依赖
```xml
<!-- JAVE2 - Java Audio Video Encoder (基于FFmpeg) -->
<dependency>
    <groupId>ws.schild</groupId>
    <artifactId>jave-core</artifactId>
    <version>3.5.0</version>
</dependency>

<!-- JAVE2 平台特定的本地库 -->
<dependency>
    <groupId>ws.schild</groupId>
    <artifactId>jave-nativebin-win64</artifactId>
    <version>3.5.0</version>
</dependency>
<dependency>
    <groupId>ws.schild</groupId>
    <artifactId>jave-nativebin-linux64</artifactId>
    <version>3.5.0</version>
</dependency>
<dependency>
    <groupId>ws.schild</groupId>
    <artifactId>jave-nativebin-osx64</artifactId>
    <version>3.5.0</version>
</dependency>

<!-- 音频文件信息读取 -->
<dependency>
    <groupId>org.jaudiotagger</groupId>
    <artifactId>jaudiotagger</artifactId>
    <version>3.0.1</version>
</dependency>
```

## 使用方式

### 1. 基本使用
```java
@Autowired
private AudioConversionService audioConversionService;

// 转换音频文件
AudioConversionService.ConversionResult result = 
    audioConversionService.convertForAliyunASR(audioFile);

if (result.isSuccess()) {
    // 使用转换后的音频流
    InputStream audioStream = result.getAudioStream();
    // ... 进行ASR识别
    
    // 清理临时文件
    result.cleanup();
} else {
    log.error("音频转换失败: {}", result.getMessage());
}
```

### 2. 检查是否需要转换
```java
boolean needsConversion = audioConversionService.needsConversion(audioFile);
if (!needsConversion) {
    // 直接使用原文件
    InputStream audioStream = audioFile.getInputStream();
}
```

### 3. 获取音频信息
```java
AudioFormatDetector.AudioInfo info = audioConversionService.getAudioInfo(audioFile);
System.out.println("音频格式: " + info.getFormat());
System.out.println("采样率: " + info.getSampleRate());
System.out.println("声道数: " + info.getChannels());
System.out.println("是否支持: " + info.isSupported());
```

### 4. 检查服务状态
```java
AudioConversionService.ServiceStatus status = audioConversionService.getServiceStatus();
System.out.println("JAVE2可用: " + status.isJaveAvailable());
System.out.println("FFmpeg可用: " + status.isFfmpegAvailable());
System.out.println("首选转换器: " + status.getPreferredConverter());
```

## 集成到ASR服务

音频转换功能已集成到 `AliyunServiceImpl` 中：

```java
@Override
public AsrResponseVo speechToText(AsrRequestDto request) {
    // 自动进行音频格式转换（如果需要）
    AudioConversionService.ConversionResult conversionResult = 
        audioConversionService.convertForAliyunASR(request.getAudioFile());
    
    if (!conversionResult.isSuccess()) {
        throw new RuntimeException("音频格式转换失败: " + conversionResult.getMessage());
    }
    
    // 使用转换后的音频进行识别
    String result = callAliyunAsrApiWithStream(conversionResult.getAudioStream());
    
    // 构建响应，包含转换信息
    AsrResponseVo response = new AsrResponseVo();
    response.setText(result);
    response.setOriginalFormat(conversionResult.getSourceInfo().getFormat());
    response.setConvertedFormat(conversionResult.getTargetInfo().getFormat());
    response.setConverterUsed(conversionResult.getConverterUsed());
    response.setConversionTime(conversionResult.getConversionTimeMs());
    
    // 清理临时文件
    conversionResult.cleanup();
    
    return response;
}
```

## 转换策略

### 自动选择转换器
1. **优先使用JAVE2**: 内置FFmpeg，无需额外安装
2. **备选FFmpeg**: 如果JAVE2不可用，使用系统FFmpeg
3. **错误处理**: 如果都不可用，返回明确的错误信息

### 转换参数
- **目标格式**: WAV (PCM编码)
- **目标采样率**: 16kHz (如果源文件是8kHz则保持8kHz)
- **目标声道**: 单声道
- **目标比特率**: 256kbps

### 性能优化
- **格式检查**: 如果已符合要求，跳过转换
- **临时文件**: 自动管理和清理临时文件
- **内存优化**: 使用流式处理，避免大文件内存占用

## 错误处理

### 常见错误及解决方案

#### 1. "没有可用的音频转换器"
**原因**: JAVE2和FFmpeg都不可用
**解决方案**: 
- 检查JAVE2依赖是否正确添加
- 安装FFmpeg到系统PATH中

#### 2. "音频格式检测失败"
**原因**: 文件损坏或格式不支持
**解决方案**: 
- 检查文件完整性
- 使用支持的音频格式

#### 3. "转换超时"
**原因**: 文件过大或系统资源不足
**解决方案**: 
- 增加超时时间配置
- 优化系统资源

### 日志监控
```java
# 启用详细日志
logging.level.org.yixz.service.AudioConversionService=DEBUG
logging.level.org.yixz.common.converter.AudioConverter=DEBUG
logging.level.org.yixz.common.converter.FFmpegAudioConverter=DEBUG
```

## 测试

### 运行测试
```bash
mvn test -Dtest=AudioConversionServiceTest
```

### 测试覆盖
- 服务状态检查
- 音频格式检测
- 转换需求判断
- 模拟数据转换
- 错误处理

## 部署注意事项

### 开发环境
- JAVE2会自动下载对应平台的FFmpeg二进制文件
- 无需额外配置

### 生产环境
- 建议同时安装系统FFmpeg作为备选
- 监控临时文件清理情况
- 设置合适的超时时间

### Docker部署
```dockerfile
# 安装FFmpeg作为备选
RUN apt-get update && apt-get install -y ffmpeg
```

## 性能指标

### 转换性能
- **小文件** (<1MB): 通常 <1秒
- **中等文件** (1-10MB): 通常 1-5秒
- **大文件** (>10MB): 根据文件大小线性增长

### 资源消耗
- **内存**: 主要用于临时缓冲，通常 <50MB
- **CPU**: 转换期间CPU使用率较高
- **磁盘**: 临时文件占用，转换后自动清理

## 扩展功能

### 未来改进方向
1. **批量转换**: 支持多文件并行转换
2. **缓存机制**: 相同文件避免重复转换
3. **异步转换**: 大文件异步处理
4. **格式预检**: 上传时预检查格式
5. **转换队列**: 高并发时的队列管理

### 自定义转换器
可以通过实现转换接口添加新的转换器：
```java
public interface AudioConverter {
    ConversionResult convert(MultipartFile source, ConversionParams params);
    boolean isAvailable();
}
```
