# ASR方法适配说明

## 适配背景

在实现音频转换功能后，发现原有的ASR识别方法还在直接使用 `request.getAudioFile().getInputStream()`，这会绕过音频格式转换流程，导致：

1. **格式不兼容**：直接使用原始音频文件，可能不符合阿里云ASR要求
2. **转换功能失效**：音频转换功能被绕过，无法发挥作用
3. **配置不匹配**：使用默认配置而非实际音频格式配置

## 适配方案

### 1. 新增带音频信息的方法

#### `callAliyunRecognizerAsrApiWithStream`
```java
private String callAliyunRecognizerAsrApiWithStream(InputStream audioStream, AudioFormatDetector.AudioInfo audioInfo)
```

**特性**：
- 接收转换后的音频流
- 根据实际音频信息配置识别器
- 使用正确的格式和采样率参数

#### `callAliyunTranscriberAsrApiWithStream`
```java
private String callAliyunTranscriberAsrApiWithStream(InputStream audioStream, AudioFormatDetector.AudioInfo audioInfo)
```

**特性**：
- 支持实时语音识别
- 动态配置转录器参数
- 优化的错误处理和日志

### 2. 旧方法适配

#### 标记为废弃
```java
@Deprecated
private String callAliyunRecognizerAsrApi(AsrRequestDto request)

@Deprecated
private String callAliyunTranscriberAsrApi(AsrRequestDto request)
```

#### 内部重定向
旧方法现在会：
1. 先调用音频转换服务
2. 使用转换后的音频流调用新方法
3. 确保向后兼容性

### 3. 智能配置

#### 动态格式配置
```java
if (audioInfo != null) {
    // 使用实际的音频格式信息
    String basicFormat = AudioFormatDetector.extractBasicFormat(audioInfo.getFormat());
    recognizer.setFormat(getInputFormat(basicFormat));
    recognizer.setSampleRate(getSampleRate(audioInfo.getSampleRate()));
} else {
    // 使用默认配置
    recognizer.setFormat(getInputFormat(asrConfig.getDefaultFormat()));
    recognizer.setSampleRate(getSampleRate(asrConfig.getDefaultSampleRate()));
}
```

#### 采样率适配
```java
// 使用实际的采样率发送音频数据
int actualSampleRate = audioInfo != null ? audioInfo.getSampleRate() : asrConfig.getDefaultSampleRate();
sendAudioData(recognizer, audioStream, actualSampleRate);
```

## 适配效果

### 1. 完整的音频转换流程

#### 调用链路
```
speechToText() 
  → audioConversionService.convertForAliyunASR() 
  → callAliyunAsrApiWithStream() 
  → callAliyunRecognizerAsrApiWithStream()
```

#### 数据流
```
MultipartFile → 格式检测 → 音频转换 → InputStream + AudioInfo → ASR识别
```

### 2. 智能参数配置

#### 转换前后对比
```java
// 转换前：固定配置
recognizer.setFormat(InputFormatEnum.PCM);
recognizer.setSampleRate(SampleRateEnum.SAMPLE_RATE_16K);

// 转换后：动态配置
String basicFormat = AudioFormatDetector.extractBasicFormat(audioInfo.getFormat()); // "WAV"
recognizer.setFormat(getInputFormat(basicFormat)); // InputFormatEnum.PCM
recognizer.setSampleRate(getSampleRate(audioInfo.getSampleRate())); // 实际采样率
```

### 3. 向后兼容性

#### 旧代码仍可工作
```java
// 这些调用仍然有效，但会通过音频转换流程
String result1 = callAliyunRecognizerAsrApi(request);
String result2 = callAliyunTranscriberAsrApi(request);
```

#### 警告日志
```
WARN - 使用了已废弃的方法 callAliyunRecognizerAsrApi，建议使用音频转换流程
```

## 使用建议

### 1. 新代码使用新方法

#### 推荐方式
```java
// 通过统一的speechToText入口，自动处理音频转换
AsrResponseVo response = aliyunService.speechToText(request);
```

#### 直接调用（如果需要）
```java
AudioConversionService.ConversionResult conversionResult = 
    audioConversionService.convertForAliyunASR(audioFile);

String result = callAliyunRecognizerAsrApiWithStream(
    conversionResult.getAudioStream(), 
    conversionResult.getTargetInfo()
);
```

### 2. 旧代码迁移

#### 逐步迁移
1. 现有代码继续工作（通过废弃方法的重定向）
2. 观察日志中的废弃警告
3. 逐步替换为新的调用方式

#### 性能优化
- 新方法避免了重复的音频转换
- 更精确的参数配置提高识别准确率

### 3. 配置优化

#### 根据音频特性调整
```yaml
ai:
  aliyun:
    asr:
      # 这些是默认值，实际会根据音频信息动态调整
      default-sample-rate: 16000
      default-format: PCM
```

## 测试验证

### 1. 功能测试

#### 测试用例
- MP3文件 → 转换为WAV → ASR识别
- 48kHz音频 → 转换为16kHz → ASR识别
- 立体声音频 → 转换为单声道 → ASR识别

#### 验证点
- 音频转换是否成功
- ASR配置是否正确
- 识别结果是否准确

### 2. 兼容性测试

#### 旧方法调用
```java
@Test
public void testBackwardCompatibility() {
    // 确保旧方法仍然工作
    String result = aliyunService.callAliyunRecognizerAsrApi(request);
    assertNotNull(result);
}
```

#### 新方法调用
```java
@Test
public void testNewMethod() {
    ConversionResult conversion = audioConversionService.convertForAliyunASR(audioFile);
    String result = aliyunService.callAliyunRecognizerAsrApiWithStream(
        conversion.getAudioStream(), conversion.getTargetInfo());
    assertNotNull(result);
}
```

## 监控指标

### 1. 转换成功率
- 音频格式转换成功率
- ASR识别成功率
- 端到端成功率

### 2. 性能指标
- 音频转换耗时
- ASR识别耗时
- 总体处理时间

### 3. 配置准确性
- 格式检测准确率
- 参数配置匹配度
- 识别质量提升

## 总结

通过这次适配，我们实现了：

1. **完整的音频转换流程**：所有ASR调用都经过音频格式检测和转换
2. **智能参数配置**：根据实际音频信息动态配置识别器
3. **向后兼容性**：旧代码无需修改即可享受新功能
4. **性能优化**：避免重复转换，提高处理效率
5. **更好的用户体验**：支持更多音频格式，提高识别准确率

现在整个ASR系统具备了完整的音频格式适配能力，可以处理各种格式的音频文件并自动转换为阿里云ASR兼容的格式。
