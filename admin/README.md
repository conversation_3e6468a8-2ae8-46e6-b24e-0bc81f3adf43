# 数据库

数据库名：xcx

账号：xcx_root

密码：xcx@2025

ecs实例连接数据库测试

```
mysql -hrm-bp1gt0za43skgf820.mysql.rds.aliyuncs.com -P3306 -uxcx_root -pxcx@2025
```

测试表

```
CREATE TABLE `sys_menu` (
  `id` int NOT NULL AUTO_INCREMENT COMMENT '主键',
  `parent_id` int DEFAULT '0' COMMENT '父菜单',
  `name` varchar(50) NOT NULL COMMENT '菜单名称',
  `type` varchar(10) NOT NULL COMMENT '菜单类型，btn-按钮，menu-目录，func-功能',
  `route` varchar(50) NOT NULL COMMENT '菜单路由',
  `url` varchar(100) DEFAULT NULL COMMENT '菜单地址',
  `icon` varchar(100) DEFAULT NULL COMMENT '图标',
  `permission` varchar(100) DEFAULT NULL COMMENT '授权标识',
  `sort_no` int NOT NULL DEFAULT '0' COMMENT '排序',
  `created_by` varchar(100) NOT NULL DEFAULT '' COMMENT '提交人员',
  `updated_by` varchar(100) NOT NULL DEFAULT '' COMMENT '修改人员',
  `created_date` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `updated_date` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  PRIMARY KEY (`id`),
  UNIQUE KEY `uk_route` (`route`) USING BTREE
) ENGINE=InnoDB AUTO_INCREMENT=1 DEFAULT CHARSET=utf8mb4 COMMENT='菜单表';
```

### 项目部署到ECS

项目打包

将项目打成jar包，将jar包上传到云服务器的/usr/local/xcx目录下

启动服务

```
/usr/local/xcx/start.sh xcx_admin.jar stop
/usr/local/xcx/start.sh xcx_admin.jar start
```

查看启动状态

```
/usr/local/xcx/start.sh xcx_admin.jar status
```

访问swagger服务
```
120.55.126.59:8000/doc.html#/home
```

