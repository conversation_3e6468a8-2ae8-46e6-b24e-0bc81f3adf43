# 综合媒体处理功能指南

## 📋 目录

- [项目概述](#项目概述)
- [音频转换功能](#音频转换功能)
- [音频分离功能](#音频分离功能)
- [字幕提取功能](#字幕提取功能)
- [AI服务集成](#ai服务集成)
- [系统架构](#系统架构)
- [配置说明](#配置说明)
- [API接口](#api接口)
- [使用示例](#使用示例)
- [部署指南](#部署指南)
- [故障排除](#故障排除)

## 🎯 项目概述

本项目是一个综合性的媒体处理平台，集成了音频转换、音频分离、字幕提取和AI识别等功能。支持多种媒体格式，提供统一的API接口，具备高可用性和扩展性。

### 核心功能
- **音频转换**: 支持多种音频格式转换，自动适配阿里云ASR要求
- **音频分离**: 从视频文件中提取音频轨道，支持多种输出格式
- **字幕提取**: 从视频文件中提取字幕，支持多种字幕格式
- **AI服务**: 集成语音转文字(ASR)和图片转文字(OCR)功能
- **多厂商支持**: 支持阿里云、百度云等多个服务提供商

### 技术特点
- **智能选择**: 自动选择最佳的处理器（FFmpeg优先，JAVE2备选）
- **格式检测**: 自动检测媒体文件格式和参数
- **资源管理**: 自动管理临时文件和系统资源
- **错误处理**: 完善的异常处理和重试机制
- **性能优化**: 缓存机制和异步处理

## 🎵 音频转换功能

### 功能概述
为了支持阿里云ASR接口的音频格式要求，实现了完整的音频格式检测和转换系统。

### 支持的格式
**输入格式**: MP3, WAV, AAC, OGG, FLAC, M4A, AMR
**输出格式**: WAV (PCM编码), MP3, AAC, OGG

### 技术规格
- **声道**: 自动转换为单声道（mono）
- **采样位数**: 16 bit
- **采样率**: 8000 Hz 或 16000 Hz（自动适配）

### 核心组件

#### 1. AudioFormatDetector
```java
// 检测音频格式
AudioFormatDetector.AudioInfo info = AudioFormatDetector.detectAudioInfo(audioFile);
System.out.println("格式: " + info.getFormat());
System.out.println("采样率: " + info.getSampleRate());
System.out.println("声道数: " + info.getChannels());
```

#### 2. AudioConverter (JAVE2)
```java
// 使用JAVE2进行转换
AudioConverter.ConversionResult result = AudioConverter.convertAudio(audioFile, targetFormat);
```

#### 3. FFmpegAudioConverter
```java
// 使用FFmpeg进行转换
FFmpegAudioConverter.ConversionResult result = FFmpegAudioConverter.convertAudio(audioFile, targetFormat);
```

#### 4. AudioConversionService
```java
@Autowired
private AudioConversionService audioConversionService;

// 统一转换接口
AudioConversionService.ConversionResult result = 
    audioConversionService.convertForAliyunASR(audioFile);
```

### 转换策略
1. **优先使用FFmpeg**: 功能更强大，性能更好
2. **备选JAVE2**: 内置FFmpeg，无需额外安装
3. **智能检测**: 如果已符合要求，跳过转换

## 🎬 音频分离功能

### 功能概述
从视频文件中提取音频轨道，支持多种视频格式和音频输出格式。

### 支持的格式
**输入格式**: MP4, AVI, MOV, MKV, FLV, WMV
**输出格式**: WAV, MP3, AAC, OGG

### 核心组件

#### 1. MediaSeparationResult
```java
public class MediaSeparationResult {
    private boolean success;
    private String message;
    private File audioFile;
    private InputStream audioStream;
    private VideoInfo sourceVideoInfo;
    private AudioInfo audioInfo;
    private long processingTimeMs;
    private String converterUsed;
}
```

#### 2. 音频分离方法
```java
// 基础音频分离
MediaSeparationResult result = AudioConverter.separateAudioFromVideo(videoFile);

// 指定输出格式
MediaSeparationResult result = AudioConverter.separateAudioFromVideo(videoFile, "mp3");

// 指定音频轨道
MediaSeparationResult result = AudioConverter.separateAudioFromVideo(videoFile, "wav", 0);
```

#### 3. FFmpeg音频分离
```java
// 使用FFmpeg进行音频分离（推荐）
MediaSeparationResult result = FFmpegAudioConverter.separateAudioFromVideo(videoFile, "wav");
```

### 服务层接口
```java
@Autowired
private AudioConversionService audioConversionService;

// 音频分离服务
MediaSeparationResult result = audioConversionService.separateAudioFromVideo(videoFile);

// 指定格式的音频分离
MediaSeparationResult result = audioConversionService.separateAudioFromVideo(videoFile, "mp3");

// 提取指定音频轨道
MediaSeparationResult result = audioConversionService.extractAudioTrack(videoFile, "wav", 0);
```

### 处理流程
1. **视频信息检测**: 自动检测视频格式、时长、分辨率等
2. **音频轨道分析**: 识别可用的音频轨道数量
3. **格式转换**: 根据需求转换为指定格式
4. **结果封装**: 返回音频文件和详细信息

## 📝 字幕提取功能

### 功能概述
从视频文件中提取字幕轨道，支持多种字幕格式，优先使用FFmpeg。

### 支持的格式
**输入格式**: MP4, MKV, AVI, MOV（包含字幕轨道）
**输出格式**: SRT, VTT, ASS, SSA, SUB

### 核心组件

#### 1. SubtitleInfo
```java
public class SubtitleInfo {
    private int trackIndex;        // 字幕轨道索引
    private String language;       // 字幕语言
    private String format;         // 字幕格式
    private String encoding;       // 字幕编码
    private boolean isDefault;     // 是否为默认字幕
    private boolean isForced;      // 是否为强制字幕
    private int entryCount;        // 字幕条目数量
}
```

#### 2. SubtitleExtractionResult
```java
public class SubtitleExtractionResult {
    private boolean success;
    private String message;
    private List<SubtitleFile> subtitleFiles;
    private VideoInfo sourceVideoInfo;
    private long processingTimeMs;
    private String extractorUsed;  // "FFmpeg" 或 "JAVE2"
}
```

#### 3. 字幕提取方法
```java
// 提取所有字幕轨道
SubtitleExtractionResult result = SubtitleExtractor.extractSubtitles(videoFile);

// 提取指定字幕轨道
SubtitleExtractionResult result = SubtitleExtractor.extractSubtitleTrack(videoFile, 0);
```

### 服务层接口
```java
@Autowired
private SubtitleExtractionService subtitleExtractionService;

// 提取所有字幕
SubtitleExtractionResult result = subtitleExtractionService.extractSubtitles(videoFile);

// 提取指定轨道
SubtitleExtractionResult result = subtitleExtractionService.extractSubtitleTrack(videoFile, 0);

// 检查是否包含字幕
boolean hasSubtitles = subtitleExtractionService.hasSubtitles(videoFile);
```

### 智能提取器选择
1. **优先FFmpeg**: 功能完整，支持多种格式
2. **备选JAVE2**: 功能有限，但提供基础支持
3. **自动检测**: 系统启动时自动检测可用性

### FFmpeg字幕提取特性
- **轨道检测**: 使用ffprobe自动检测字幕轨道
- **格式转换**: 自动转换为SRT格式
- **语言识别**: 尝试识别字幕语言
- **批量提取**: 支持提取多个字幕轨道

## 🤖 AI服务集成

### 架构设计
采用策略模式和工厂模式，支持多厂商灵活切换。

### 支持的服务商
- **阿里云**: ASR + OCR
- **百度云**: ASR + OCR
- **可扩展**: 支持添加新的服务商

### API接口

#### 1. 语音转文字 (ASR)
```http
POST /ai/asr
Content-Type: multipart/form-data

audioFile: [音频文件]
format: wav
sampleRate: 16000
language: zh
enablePunctuation: true
provider: aliyun
```

#### 2. 图片转文字 (OCR)
```http
POST /ai/ocr
Content-Type: multipart/form-data

imageFile: [图片文件]
ocrType: general
detectOrientation: true
language: zh
provider: aliyun
```

#### 3. 服务状态查询
```http
GET /ai/status
```

### 集成音频转换
ASR服务已完全集成音频转换功能：
```java
@Override
public AsrResponseVo speechToText(AsrRequestDto request) {
    // 自动进行音频格式转换
    AudioConversionService.ConversionResult conversionResult = 
        audioConversionService.convertForAliyunASR(request.getAudioFile());
    
    // 使用转换后的音频进行识别
    String result = callAliyunAsrApiWithStream(conversionResult.getAudioStream());
    
    // 返回包含转换信息的响应
    AsrResponseVo response = new AsrResponseVo();
    response.setText(result);
    response.setConverterUsed(conversionResult.getConverterUsed());
    response.setConversionTime(conversionResult.getConversionTimeMs());
    
    return response;
}
```

## 🏗️ 系统架构

### 核心工具类
#### MediaConverterUtil
统一管理FFmpeg和JAVE2的可用性检测：
```java
// 检查可用性
boolean ffmpegAvailable = MediaConverterUtil.isFFmpegAvailable();
boolean javeAvailable = MediaConverterUtil.isJaveAvailable();

// 获取首选转换器
String preferredConverter = MediaConverterUtil.getPreferredConverter();
String preferredSubtitleExtractor = MediaConverterUtil.getPreferredSubtitleExtractor();

// 获取系统信息
MediaConverterUtil.SystemInfo systemInfo = MediaConverterUtil.getSystemInfo();
```

### 优先级策略
1. **音频转换**: FFmpeg > JAVE2
2. **音频分离**: FFmpeg > JAVE2
3. **字幕提取**: FFmpeg > JAVE2

### 服务层架构
```
Controller Layer
    ↓
Service Layer (AudioConversionService, SubtitleExtractionService)
    ↓
Converter Layer (AudioConverter, FFmpegAudioConverter, SubtitleExtractor)
    ↓
Utility Layer (MediaConverterUtil, AudioFormatDetector)
```

## ⚙️ 配置说明

### Maven依赖
```xml
<!-- JAVE2 - Java Audio Video Encoder -->
<dependency>
    <groupId>ws.schild</groupId>
    <artifactId>jave-core</artifactId>
    <version>3.5.0</version>
</dependency>

<!-- JAVE2 平台特定的本地库 -->
<dependency>
    <groupId>ws.schild</groupId>
    <artifactId>jave-nativebin-win64</artifactId>
    <version>3.5.0</version>
</dependency>
<dependency>
    <groupId>ws.schild</groupId>
    <artifactId>jave-nativebin-linux64</artifactId>
    <version>3.5.0</version>
</dependency>
<dependency>
    <groupId>ws.schild</groupId>
    <artifactId>jave-nativebin-osx64</artifactId>
    <version>3.5.0</version>
</dependency>

<!-- 音频文件信息读取 -->
<dependency>
    <groupId>org.jaudiotagger</groupId>
    <artifactId>jaudiotagger</artifactId>
    <version>3.0.1</version>
</dependency>
```

### 应用配置
```properties
# AI服务配置
ai.provider=aliyun

# 阿里云配置
ai.aliyun.access-key-id=your_access_key_id
ai.aliyun.access-key-secret=your_access_key_secret
ai.aliyun.asr.endpoint=https://nls-meta.cn-shanghai.aliyuncs.com
ai.aliyun.ocr.endpoint=https://ocr-api.cn-hangzhou.aliyuncs.com

# 百度云配置
ai.baidu.api-key=your_api_key
ai.baidu.secret-key=your_secret_key
ai.baidu.asr.endpoint=https://vop.baidu.com/server_api
ai.baidu.ocr.endpoint=https://aip.baidubce.com/rest/2.0/ocr

# 音频转换配置
audio.conversion.timeout=60000
audio.conversion.temp-dir=/tmp/audio_conversion
audio.conversion.cleanup-on-shutdown=true

# 字幕提取配置
subtitle.extraction.timeout=30000
subtitle.extraction.default-format=srt
subtitle.extraction.temp-dir=/tmp/subtitle_extraction
```

### 日志配置
```properties
# 启用详细日志
logging.level.org.yixz.service.AudioConversionService=DEBUG
logging.level.org.yixz.service.SubtitleExtractionService=DEBUG
logging.level.org.yixz.common.converter=DEBUG
logging.level.org.yixz.common.util.MediaConverterUtil=DEBUG
```

## 📚 API接口

### 音频转换接口
```http
POST /api/audio/convert
Content-Type: multipart/form-data

audioFile: [音频文件]
targetFormat: wav
targetSampleRate: 16000
targetChannels: 1
```

### 音频分离接口
```http
POST /api/audio/separate
Content-Type: multipart/form-data

videoFile: [视频文件]
outputFormat: wav
trackIndex: 0
```

### 字幕提取接口
```http
POST /api/subtitle/extract
Content-Type: multipart/form-data

videoFile: [视频文件]
trackIndex: 0
outputFormat: srt
```

### 系统状态接口
```http
GET /api/system/status
```

响应示例：
```json
{
  "code": "SUCCESS",
  "data": {
    "ffmpegAvailable": true,
    "javeAvailable": true,
    "preferredConverter": "FFmpeg",
    "preferredSubtitleExtractor": "FFmpeg",
    "systemInfo": {
      "osName": "Mac OS X",
      "osArch": "aarch64",
      "javaVersion": "17.0.1"
    }
  }
}
```

## 💡 使用示例

### 1. 音频转换示例
```java
@RestController
@RequestMapping("/api/audio")
public class AudioController {

    @Autowired
    private AudioConversionService audioConversionService;

    @PostMapping("/convert")
    public ResponseEntity<?> convertAudio(@RequestParam("audioFile") MultipartFile audioFile) {
        try {
            // 检查是否需要转换
            if (!audioConversionService.needsConversion(audioFile)) {
                return ResponseEntity.ok("音频格式已符合要求，无需转换");
            }

            // 执行转换
            AudioConversionService.ConversionResult result =
                audioConversionService.convertForAliyunASR(audioFile);

            if (result.isSuccess()) {
                // 返回转换后的音频流
                return ResponseEntity.ok()
                    .header("Content-Type", "audio/wav")
                    .body(new InputStreamResource(result.getAudioStream()));
            } else {
                return ResponseEntity.badRequest().body(result.getMessage());
            }
        } catch (Exception e) {
            return ResponseEntity.status(500).body("转换失败: " + e.getMessage());
        }
    }
}
```

### 2. 音频分离示例
```java
@RestController
@RequestMapping("/api/media")
public class MediaController {

    @Autowired
    private AudioConversionService audioConversionService;

    @PostMapping("/separate-audio")
    public ResponseEntity<?> separateAudio(
            @RequestParam("videoFile") MultipartFile videoFile,
            @RequestParam(value = "format", defaultValue = "wav") String format) {
        try {
            MediaSeparationResult result =
                audioConversionService.separateAudioFromVideo(videoFile, format);

            if (result.isSuccess()) {
                Map<String, Object> response = new HashMap<>();
                response.put("success", true);
                response.put("message", result.getMessage());
                response.put("processingTime", result.getProcessingTimeMs());
                response.put("converterUsed", result.getConverterUsed());
                response.put("audioInfo", result.getAudioInfo());
                response.put("videoInfo", result.getSourceVideoInfo());

                return ResponseEntity.ok(response);
            } else {
                return ResponseEntity.badRequest().body(result.getMessage());
            }
        } catch (Exception e) {
            return ResponseEntity.status(500).body("音频分离失败: " + e.getMessage());
        }
    }
}
```

### 3. 字幕提取示例
```java
@RestController
@RequestMapping("/api/subtitle")
public class SubtitleController {

    @Autowired
    private SubtitleExtractionService subtitleExtractionService;

    @PostMapping("/extract")
    public ResponseEntity<?> extractSubtitles(
            @RequestParam("videoFile") MultipartFile videoFile,
            @RequestParam(value = "trackIndex", defaultValue = "-1") int trackIndex) {
        try {
            SubtitleExtractionResult result;

            if (trackIndex >= 0) {
                // 提取指定轨道
                result = subtitleExtractionService.extractSubtitleTrack(videoFile, trackIndex);
            } else {
                // 提取所有轨道
                result = subtitleExtractionService.extractSubtitles(videoFile);
            }

            if (result.isSuccess()) {
                Map<String, Object> response = new HashMap<>();
                response.put("success", true);
                response.put("message", result.getMessage());
                response.put("processingTime", result.getProcessingTimeMs());
                response.put("extractorUsed", result.getExtractorUsed());
                response.put("subtitleCount", result.getSubtitleTrackCount());

                // 返回字幕文件信息
                List<Map<String, Object>> subtitles = new ArrayList<>();
                for (SubtitleExtractionResult.SubtitleFile subtitleFile : result.getSubtitleFiles()) {
                    Map<String, Object> subtitleInfo = new HashMap<>();
                    subtitleInfo.put("trackIndex", subtitleFile.getSubtitleInfo().getTrackIndex());
                    subtitleInfo.put("language", subtitleFile.getSubtitleInfo().getLanguage());
                    subtitleInfo.put("format", subtitleFile.getSubtitleInfo().getFormat());
                    subtitleInfo.put("isDefault", subtitleFile.getSubtitleInfo().isDefault());
                    subtitleInfo.put("content", subtitleFile.getContent());
                    subtitles.add(subtitleInfo);
                }
                response.put("subtitles", subtitles);

                return ResponseEntity.ok(response);
            } else {
                return ResponseEntity.badRequest().body(result.getMessage());
            }
        } catch (Exception e) {
            return ResponseEntity.status(500).body("字幕提取失败: " + e.getMessage());
        }
    }
}
```

## 🚀 部署指南

### 开发环境部署

#### 1. 环境要求
- **Java**: JDK 17+
- **Maven**: 3.6+
- **FFmpeg**: 4.0+（可选，推荐安装）

#### 2. 快速启动
```bash
# 克隆项目
git clone [项目地址]
cd xiaochengxu/admin

# 编译项目
mvn clean compile

# 运行项目
mvn spring-boot:run
```

#### 3. FFmpeg安装（推荐）
```bash
# macOS
brew install ffmpeg

# Ubuntu/Debian
sudo apt update
sudo apt install ffmpeg

# CentOS/RHEL
sudo yum install epel-release
sudo yum install ffmpeg

# Windows
# 下载FFmpeg并添加到PATH环境变量
```

### 生产环境部署

#### 1. Docker部署
```dockerfile
FROM openjdk:17-jdk-slim

# 安装FFmpeg
RUN apt-get update && \
    apt-get install -y ffmpeg && \
    rm -rf /var/lib/apt/lists/*

# 复制应用
COPY target/xcx_admin.jar app.jar

# 创建临时目录
RUN mkdir -p /tmp/audio_conversion /tmp/subtitle_extraction

# 启动应用
ENTRYPOINT ["java", "-jar", "/app.jar"]
```

#### 2. 构建和运行
```bash
# 构建镜像
docker build -t xcx-admin .

# 运行容器
docker run -d \
  --name xcx-admin \
  -p 8000:8000 \
  -v /data/logs:/app/logs \
  -v /data/temp:/tmp \
  -e SPRING_PROFILES_ACTIVE=prod \
  xcx-admin
```

#### 3. 云服务器部署
```bash
# 上传jar包到服务器
scp target/xcx_admin.jar user@server:/root/user/app/

# 启动服务
/root/user/app/start.sh xcx_admin.jar start

# 查看状态
/root/user/app/start.sh xcx_admin.jar status

# 停止服务
/root/user/app/start.sh xcx_admin.jar stop
```

### 配置优化

#### 1. JVM参数优化
```bash
java -Xms2g -Xmx4g \
     -XX:+UseG1GC \
     -XX:MaxGCPauseMillis=200 \
     -XX:+HeapDumpOnOutOfMemoryError \
     -XX:HeapDumpPath=/data/logs/heapdump.hprof \
     -jar xcx_admin.jar
```

#### 2. 系统参数优化
```bash
# 增加文件描述符限制
ulimit -n 65536

# 增加进程数限制
ulimit -u 32768

# 设置临时目录
export TMPDIR=/data/temp
```

#### 3. 监控配置
```properties
# 启用监控端点
management.endpoints.web.exposure.include=health,info,metrics,prometheus
management.endpoint.health.show-details=always

# 自定义健康检查
management.health.custom.enabled=true
```

## 🔧 故障排除

### 常见问题及解决方案

#### 1. "没有可用的音频转换器"
**症状**: 启动时提示转换器不可用
**原因**: JAVE2和FFmpeg都不可用
**解决方案**:
```bash
# 检查JAVE2依赖
mvn dependency:tree | grep jave

# 安装FFmpeg
# macOS: brew install ffmpeg
# Ubuntu: sudo apt install ffmpeg
# CentOS: sudo yum install ffmpeg

# 验证FFmpeg安装
ffmpeg -version
```

#### 2. "音频格式检测失败"
**症状**: 无法检测音频文件格式
**原因**: 文件损坏或格式不支持
**解决方案**:
```bash
# 检查文件完整性
file audio.mp3

# 使用FFmpeg检查
ffprobe audio.mp3

# 转换为支持的格式
ffmpeg -i input.xxx -acodec pcm_s16le -ar 16000 -ac 1 output.wav
```

#### 3. "转换超时"
**症状**: 大文件转换时超时
**原因**: 文件过大或系统资源不足
**解决方案**:
```properties
# 增加超时时间
audio.conversion.timeout=120000
subtitle.extraction.timeout=60000

# 优化JVM内存
-Xms2g -Xmx4g
```

#### 4. "字幕提取失败"
**症状**: 无法提取视频字幕
**原因**: 视频不包含字幕或格式不支持
**解决方案**:
```bash
# 检查视频是否包含字幕
ffprobe -v quiet -print_format json -show_streams video.mp4 | grep subtitle

# 手动提取字幕
ffmpeg -i video.mp4 -map 0:s:0 subtitle.srt
```

#### 5. "Apple Silicon Mac兼容性问题"
**症状**: 在Apple Silicon Mac上JAVE2不工作
**原因**: JAVE2对Apple Silicon支持有限
**解决方案**:
```bash
# 安装Homebrew FFmpeg
brew install ffmpeg

# 验证系统架构
uname -m  # 应该显示 arm64

# 检查Java架构
java -XshowSettings:properties -version 2>&1 | grep os.arch
```

### 性能优化建议

#### 1. 内存优化
```properties
# 设置合适的堆内存
-Xms1g -Xmx2g

# 使用G1垃圾收集器
-XX:+UseG1GC
-XX:MaxGCPauseMillis=200

# 启用压缩指针
-XX:+UseCompressedOops
```

#### 2. 磁盘I/O优化
```properties
# 使用SSD存储临时文件
audio.conversion.temp-dir=/ssd/temp/audio
subtitle.extraction.temp-dir=/ssd/temp/subtitle

# 定期清理临时文件
audio.conversion.cleanup-interval=3600000
```

#### 3. 并发优化
```properties
# 设置线程池大小
server.tomcat.threads.max=200
server.tomcat.threads.min-spare=10

# 异步处理配置
spring.task.execution.pool.core-size=8
spring.task.execution.pool.max-size=16
```

### 监控和日志

#### 1. 关键指标监控
- **转换成功率**: 音频/字幕转换成功的比例
- **处理时间**: 平均处理时间和P99延迟
- **资源使用**: CPU、内存、磁盘使用率
- **错误率**: 各类错误的发生频率

#### 2. 日志配置
```xml
<!-- logback-spring.xml -->
<configuration>
    <appender name="MEDIA_PROCESSING" class="ch.qos.logback.core.rolling.RollingFileAppender">
        <file>logs/media-processing.log</file>
        <rollingPolicy class="ch.qos.logback.core.rolling.TimeBasedRollingPolicy">
            <fileNamePattern>logs/media-processing.%d{yyyy-MM-dd}.%i.gz</fileNamePattern>
            <maxFileSize>100MB</maxFileSize>
            <maxHistory>30</maxHistory>
        </rollingPolicy>
        <encoder>
            <pattern>%d{yyyy-MM-dd HH:mm:ss.SSS} [%thread] %-5level %logger{36} - %msg%n</pattern>
        </encoder>
    </appender>

    <logger name="org.yixz.service" level="INFO" additivity="false">
        <appender-ref ref="MEDIA_PROCESSING"/>
    </logger>
</configuration>
```

#### 3. 健康检查
```java
@Component
public class MediaProcessingHealthIndicator implements HealthIndicator {

    @Override
    public Health health() {
        boolean ffmpegAvailable = MediaConverterUtil.isFFmpegAvailable();
        boolean javeAvailable = MediaConverterUtil.isJaveAvailable();

        if (ffmpegAvailable || javeAvailable) {
            return Health.up()
                .withDetail("ffmpeg", ffmpegAvailable)
                .withDetail("jave2", javeAvailable)
                .withDetail("preferred", MediaConverterUtil.getPreferredConverter())
                .build();
        } else {
            return Health.down()
                .withDetail("reason", "No media converter available")
                .build();
        }
    }
}
```

## 📈 性能指标

### 处理性能
- **小文件** (<1MB): 通常 <1秒
- **中等文件** (1-10MB): 通常 1-5秒
- **大文件** (>10MB): 根据文件大小线性增长

### 资源消耗
- **内存**: 主要用于临时缓冲，通常 <100MB
- **CPU**: 转换期间CPU使用率较高（50-80%）
- **磁盘**: 临时文件占用，处理后自动清理

### 并发能力
- **音频转换**: 支持10-20个并发请求
- **字幕提取**: 支持5-10个并发请求
- **系统总体**: 建议不超过50个并发媒体处理请求

## 🔮 未来规划

### 短期目标
1. **批量处理**: 支持多文件并行处理
2. **缓存机制**: 相同文件避免重复转换
3. **异步处理**: 大文件异步处理队列
4. **格式扩展**: 支持更多音频和视频格式

### 长期目标
1. **AI增强**: 集成更多AI服务（语音合成、图像识别等）
2. **云原生**: 支持Kubernetes部署和自动扩缩容
3. **微服务**: 拆分为独立的微服务架构
4. **实时处理**: 支持实时音频流处理

---

## 📞 技术支持

如有问题或建议，请通过以下方式联系：

- **项目地址**: [GitHub/Gitee链接]
- **文档地址**: [在线文档链接]
- **问题反馈**: [Issue链接]

---

*最后更新时间: 2025-01-22*
*文档版本: v1.0.0*
```
