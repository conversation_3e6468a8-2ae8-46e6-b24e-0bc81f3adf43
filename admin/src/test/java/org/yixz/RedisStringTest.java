package org.yixz;

import org.junit.jupiter.api.Test;
import org.springframework.boot.test.context.SpringBootTest;
import org.yixz.common.util.RedisUtil;

/**
 * Redis字符串序列化测试
 */
@SpringBootTest
class RedisStringTest {

    @Test
    public void testStringStorage() {
        String testKey = "test:string:key";
        String testValue = "Hello Redis 测试字符串";
        
        // 保存字符串
        RedisUtil.setCacheObject(testKey, testValue);
        
        // 读取字符串
        String retrievedValue = RedisUtil.getCacheObject(testKey);
        
        System.out.println("原始值: " + testValue);
        System.out.println("读取值: " + retrievedValue);
        System.out.println("是否相等: " + testValue.equals(retrievedValue));
        
        // 清理测试数据
        RedisUtil.deleteObject(testKey);
    }
    
    @Test
    public void testStringWithDuration() {
        String testKey = "test:string:duration";
        String testValue = "测试带过期时间的字符串";
        
        // 保存带过期时间的字符串
        RedisUtil.setCacheObject(testKey, testValue, java.time.Duration.ofMinutes(5));
        
        // 读取字符串
        String retrievedValue = RedisUtil.getCacheObject(testKey);
        
        System.out.println("原始值: " + testValue);
        System.out.println("读取值: " + retrievedValue);
        System.out.println("是否相等: " + testValue.equals(retrievedValue));
        
        // 清理测试数据
        RedisUtil.deleteObject(testKey);
    }
}
