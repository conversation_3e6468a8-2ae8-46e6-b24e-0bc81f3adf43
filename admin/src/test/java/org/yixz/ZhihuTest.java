package org.yixz;

import lombok.extern.slf4j.Slf4j;
import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;
import org.yixz.entity.vo.LinkNoteVo;
import org.yixz.service.LinkNoteHandler;
import org.yixz.service.ZhihuService;


@Slf4j
@SpringBootTest
public class ZhihuTest {
    @Autowired
    private LinkNoteHandler linkNoteHandler;

    @Test
    public void test() {
        LinkNoteVo linkNoteVo = linkNoteHandler.getNote("https://zhuanlan.zhihu.com/p/28723907104");
        log.info("知乎文章---------{}", linkNoteVo);
    }
}
