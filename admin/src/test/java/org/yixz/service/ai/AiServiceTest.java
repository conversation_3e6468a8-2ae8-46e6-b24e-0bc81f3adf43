package org.yixz.service.ai;

import org.junit.jupiter.api.Test;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.ActiveProfiles;

/**
 * AI服务测试类
 *
 * <AUTHOR>
 * @date 2025-07-20
 */
@SpringBootTest
@ActiveProfiles("dev")
public class AiServiceTest {
    
    @Test
    public void testServiceConfiguration() {
        // 这里可以添加具体的测试逻辑
        System.out.println("AI服务配置测试");
    }
}
