server:
  port: 8000
  # Tom<PERSON>服务器配置
  tomcat:
    # 连接超时时间（毫秒）- 客户端连接到服务器的超时时间
    connection-timeout: 60000  # 60秒
    # Keep-Alive超时时间（毫秒）- 保持连接的时间
    keep-alive-timeout: 60000  # 60秒
    # 最大连接数
    max-connections: 8192
    # 最大线程数
    threads:
      max: 200
      min-spare: 10
  # Servlet配置
  servlet:
    # 会话超时时间
    session:
      timeout: 30m  # 30分钟
    # 文件上传配置
    multipart:
      max-file-size: 100MB      # 单个文件最大100MB
      max-request-size: 100MB   # 请求最大100MB
      file-size-threshold: 10MB # 文件大小阈值

# 数据库连接池配置信息
spring:
  # Web相关配置
  web:
    # 资源处理超时
    resources:
      cache:
        period: 3600  # 1小时
  # 任务执行配置
  task:
    execution:
      pool:
        core-size: 8
        max-size: 16
        queue-capacity: 100
        keep-alive: 60s
      thread-name-prefix: "async-task-"
    scheduling:
      pool:
        size: 4
      thread-name-prefix: "scheduled-task-"
  datasource:
    type: com.zaxxer.hikari.HikariDataSource
    driver-class-name: com.mysql.cj.jdbc.Driver
    url: *****************************************************************************************************
    username: root
    password: 1234@wu
    hikari:
      # 连接池中允许的最小连接数。缺省值：10
      minimum-idle: 10
      # 连接池中允许的最大连接数。缺省值：10
      maximum-pool-size: 100
      # 自动提交
      auto-commit: true
      # 一个连接idle状态的最大时长（毫秒），超时则被释放（retired），缺省:10分钟
      idle-timeout: 30000
      # 连接池名字
      pool-name: MyHikariCP
      # 一个连接的生命时长（毫秒），超时而且没被使用则被释放（retired），缺省:30分钟，建议设置比数据库超时时长少30秒
      max-lifetime: 1800000
      # 等待连接池分配连接的最大时长（毫秒），超过这个时长还没可用的连接则发生SQLException， 缺省:30秒
      connection-timeout: 30000
      # 数据库连接测试语句
      connection-test-query: SELECT 1

  # redis cluster配置
  # redis:
  #   cluster:
  #     nodes: 127.0.0.1:7000,127.0.0.1:7001,127.0.0.1:7002,127.0.0.1:7003,127.0.0.1:7004,127.0.0.1:7005
  #     # 设置命令的执行时间，如果超过这个时间，则报错
  #     command-timeout: 2000

  # redis 配置
  data:
    redis:
      host: 127.0.0.1
      port: 6379
      # 超时时间
      connect-timeout: 2000
      # Redis数据库索引（默认为0）
      database: 0
      # Redis服务器连接密码（默认为空）
      password: 123456
      # 连接超时时间（毫秒）
      timeout: 5000
      lettuce:
        pool:
          # 连接池最大连接数（使用负值表示没有限制）默认 8
          max-active: 4
          # 连接池最大阻塞等待时间（使用负值表示没有限制） 默认 -1
          max-wait: -1
          # 连接池中的最大空闲连接 默认 8
          max-idle: 4
          # 连接池中的最小空闲连接 默认 0
          min-idle: 0

# 配置要扫描的实体类映射文件
mybatis-plus:
  mapper-locations: classpath:/mapper/*.xml

# spring-doc-openapi项目配置
springdoc:
  swagger-ui:
    path: /swagger-ui.html
    tags-sorter: alpha
    operations-sorter: alpha
  api-docs:
    path: /v3/api-docs
  group-configs:
    - group: 默认分组
      paths-to-match: /**
      # 生成文档所需的扫包路径，一般为启动类目录
      packages-to-scan: org.yixz

# knife4j配置
knife4j:
  # 是否启用增强设置
  enable: false
  # 是否启用登录认证
  basic:
    enable: true
    username: pabst
    password: pabst@2020
  # 是否为生产模式，为true开启生产环境屏蔽
  production: false

# AI服务配置
ai:
  provider: aliyun
  # 阿里云配置
  aliyun:
    access-key-id: LTAI5tAMgvPyAvvYfAmrBPwk
    access-key-secret: ******************************
    asr:
      endpoint: wss://nls-gateway-cn-beijing.aliyuncs.com/ws/v1
      app-key: sUuuzVWQXJVIxngw
      default-sample-rate: 16000
      default-format: PCM
      enable-intermediate-result: true
      enable-voice-detection: true
      enable-punctuation: true
      enable-itn: false
      connect-timeout-ms: 15000   # 连接超时15秒
      read-timeout-ms: 60000    # 读取超时60秒
      max-wait-time-ms: 10000   # ASR等待结果10秒
      max-retry-attempts: 3
      retry-delay-ms: 1000
    ocr:
      endpoint: ocr-api.cn-hangzhou.aliyuncs.com
      connect-timeout-ms: 15000   # OCR连接超时15秒
      read-timeout-ms: 45000    # OCR读取超时45秒
      max-retry-attempts: 3
      retry-delay-ms: 1000
  # 百度云配置
  baidu:
    api-key: N8PwVuuZ9f2BdPO75L82Yk6Y
    secret-key: bqnz5UtweH3xJUumOXyF8XP946caFp1L
    app-id: 119614993
    asr:
      # 短语音识别标准版
      endpoint: http://vop.baidu.com/server_api
      # 实时语音识别
      realtime-asr: wss://vop.baidu.com/realtime_asr
    ocr:
      # 通用文字识别（标准版）
      endpoint: https://aip.baidubce.com/rest/2.0/ocr/v1/general_basic
      # 通用文字识别（高精度版）
      # endpoint: https://aip.baidubce.com/rest/2.0/ocr/v1/accurate_basic

# 视频下载配置
video:
  download:
    # 默认下载器
    default-downloader: yt-dlp
    # 是否启用平台特定下载器
    enable-platform-specific: true
    # 是否启用降级机制（平台特定失败时使用yt-dlp）
    enable-fallback: true
    # 下载超时时间（毫秒）
    download-timeout-ms: 300000  # 5分钟
    # 连接超时时间（毫秒）
    connect-timeout-ms: 30000    # 30秒
    # 最大重试次数
    max-retry-attempts: 3
    # 重试延迟时间（毫秒）
    retry-delay-ms: 2000
    # 临时文件目录
    temp-dir: /Users/<USER>/Desktop/downloads/tmp
    # 是否保留临时文件（调试用）
    keep-temp-files: false
    # 保存路径
    save-dir: /Users/<USER>/Desktop/downloads

    # yt-dlp配置
    yt-dlp:
      executable-path: yt-dlp
      default-format: best[height<=720]
      extract-audio: false
      audio-format: mp3
      write-subtitles: false
      write-auto-subtitles: false
      output-template: "%(title)s.%(ext)s"

    # 抖音配置
    douyin:
      enabled: true
      user-agent: "Mozilla/5.0 (iPhone; CPU iPhone OS 14_0 like Mac OS X)"
      max-retries: 3

    # 快手配置
    kuaishou:
      enabled: true
      user-agent: "Mozilla/5.0 (iPhone; CPU iPhone OS 14_0 like Mac OS X)"
      max-retries: 3

    # B站配置
    bilibili:
      enabled: true
      user-agent: "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36"
      max-retries: 3
      require-login: false
      sessdata: ""
      bili-jct: ""


# redisson 配置
redisson:
  # redis key前缀
  keyPrefix:
  # 线程池数量
  threads: 4
  # Netty线程池数量
  nettyThreads: 8
  # 单节点配置
  singleServerConfig:
    # 客户端名称
    clientName: ${spring.application.name}
    # 最小空闲连接数
    connectionMinimumIdleSize: 8
    # 连接池大小
    connectionPoolSize: 32
    # 连接空闲超时，单位：毫秒
    idleConnectionTimeout: 10000
    # 命令等待超时，单位：毫秒
    timeout: 3000
    # 发布和订阅连接池大小
    subscriptionConnectionPoolSize: 50

chrome:
  path: D:\Program Files\chrome-test\chrome.exe
  driver:
    path: D:\Program Files\chrome-test\chromeDriver\chromedriver-win64\chromedriver.exe
