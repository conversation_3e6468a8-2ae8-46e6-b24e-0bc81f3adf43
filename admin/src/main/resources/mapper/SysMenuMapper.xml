<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="org.yixz.mapper.SysMenuMapper">

    <select id="getAuthMenu" resultType="org.yixz.entity.vo.SysMenuVo">
        select d.id, d.name, d.menu_type, d.parent_id, d.icon, d.url, d.perm, d.sort_no
        from sys_user a inner join sys_user_role b on a.id=b.user_id
                        inner join sys_role_menu c on b.role_id =c.role_id
                        inner join sys_menu d on c.menu_id = d.id
        where a.id=#{userId}
    </select>
</mapper>
