server:
  port: 8000
  # 生产环境Tomcat服务器配置
  tomcat:
    # 连接超时时间（毫秒）- 生产环境适当延长
    connection-timeout: 120000  # 120秒（2分钟）
    # Keep-Alive超时时间（毫秒）
    keep-alive-timeout: 120000  # 120秒
    # 最大连接数 - 生产环境增加
    max-connections: 10000
    # 最大线程数 - 生产环境增加
    threads:
      max: 500
      min-spare: 50
    # 接受队列长度
    accept-count: 1000
  # Servlet配置
  servlet:
    # 会话超时时间 - 生产环境延长
    session:
      timeout: 60m  # 60分钟
    # 文件上传配置 - 生产环境限制
    multipart:
      max-file-size: 200MB      # 单个文件最大200MB
      max-request-size: 200MB   # 请求最大200MB
      file-size-threshold: 20MB # 文件大小阈值

# 数据库连接池配置信息
spring:
  # Web相关配置
  web:
    # 资源处理超时
    resources:
      cache:
        period: 7200  # 2小时
  # 任务执行配置 - 生产环境增强
  task:
    execution:
      pool:
        core-size: 16
        max-size: 64
        queue-capacity: 500
        keep-alive: 300s  # 5分钟
      thread-name-prefix: "async-task-"
    scheduling:
      pool:
        size: 8
      thread-name-prefix: "scheduled-task-"
  datasource:
    type: com.zaxxer.hikari.HikariDataSource
    driver-class-name: com.mysql.cj.jdbc.Driver
    url: **************************************************************************************************************************************
    username: xcx_root
    password: xcx@2025
    hikari:
      # 连接池中允许的最小连接数。缺省值：10
      minimum-idle: 10
      # 连接池中允许的最大连接数。缺省值：10
      maximum-pool-size: 100
      # 自动提交
      auto-commit: true
      # 一个连接idle状态的最大时长（毫秒），超时则被释放（retired），缺省:10分钟
      idle-timeout: 30000
      # 连接池名字
      pool-name: MyHikariCP
      # 一个连接的生命时长（毫秒），超时而且没被使用则被释放（retired），缺省:30分钟，建议设置比数据库超时时长少30秒
      max-lifetime: 1800000
      # 等待连接池分配连接的最大时长（毫秒），超过这个时长还没可用的连接则发生SQLException， 缺省:30秒
      connection-timeout: 30000
      # 数据库连接测试语句
      connection-test-query: SELECT 1

  # redis cluster配置
  # redis:
  #   cluster:
  #     nodes: 127.0.0.1:7000,127.0.0.1:7001,127.0.0.1:7002,127.0.0.1:7003,127.0.0.1:7004,127.0.0.1:7005
  #     # 设置命令的执行时间，如果超过这个时间，则报错
  #     command-timeout: 2000

  # redis 配置
  data:
    redis:
      host: 127.0.0.1
      port: 6379
      # 超时时间
      connect-timeout: 2000
      # Redis数据库索引（默认为0）
      database: 0
      # Redis服务器连接密码（默认为空）
      password: 123456
      # 连接超时时间（毫秒）
      timeout: 5000
      lettuce:
        pool:
          # 连接池最大连接数（使用负值表示没有限制）默认 8
          max-active: 4
          # 连接池最大阻塞等待时间（使用负值表示没有限制） 默认 -1
          max-wait: -1
          # 连接池中的最大空闲连接 默认 8
          max-idle: 4
          # 连接池中的最小空闲连接 默认 0
          min-idle: 0

# 配置要扫描的实体类映射文件
mybatis-plus:
  mapper-locations: classpath:/mapper/*.xml

# spring-doc-openapi项目配置
springdoc:
  swagger-ui:
    path: /swagger-ui.html
    tags-sorter: alpha
    operations-sorter: alpha
  api-docs:
    path: /v3/api-docs
  group-configs:
    - group: 默认分组
      paths-to-match: /**
      # 生成文档所需的扫包路径，一般为启动类目录
      packages-to-scan: org.yixz

# knife4j配置
knife4j:
  # 是否启用增强设置
  enable: true
  # 是否启用登录认证
  basic:
    enable: true
    username: pabst
    password: pabst@2025
  # 是否为生产模式，为true开启生产环境屏蔽
  production: false

# AI服务配置（生产环境）
ai:
  provider: aliyun
  # 阿里云配置
  aliyun:
    access-key-id: ${ALIYUN_ACCESS_KEY_ID:your_access_key_id}
    access-key-secret: ${ALIYUN_ACCESS_KEY_SECRET:your_access_key_secret}
    asr:
      endpoint: wss://nls-gateway-cn-beijing.aliyuncs.com/ws/v1
      app-key: ${ALIYUN_ASR_APP_KEY:your_app_key}
      default-sample-rate: 16000
      default-format: PCM
      enable-intermediate-result: true
      enable-voice-detection: true
      enable-punctuation: true
      enable-itn: false
      connect-timeout-ms: 30000   # 生产环境连接超时30秒
      read-timeout-ms: 120000   # 生产环境读取超时120秒（2分钟）
      max-wait-time-ms: 20000   # ASR等待结果20秒
      max-retry-attempts: 5
      retry-delay-ms: 2000
    ocr:
      endpoint: ocr-api.cn-hangzhou.aliyuncs.com
      connect-timeout-ms: 30000   # 生产环境OCR连接超时30秒
      read-timeout-ms: 90000    # 生产环境OCR读取超时90秒
      max-retry-attempts: 3
      retry-delay-ms: 1000
  # 百度云配置
  baidu:
    api-key: ${BAIDU_API_KEY:your_api_key}
    secret-key: ${BAIDU_SECRET_KEY:your_secret_key}
    asr:
      endpoint: https://vop.baidu.com/server_api
    ocr:
      endpoint: https://aip.baidubce.com/rest/2.0/ocr


# redisson 配置
redisson:
  # redis key前缀
  keyPrefix:
  # 线程池数量
  threads: 4
  # Netty线程池数量
  nettyThreads: 8
  # 单节点配置
  singleServerConfig:
    # 客户端名称
    clientName: ${spring.application.name}
    # 最小空闲连接数
    connectionMinimumIdleSize: 8
    # 连接池大小
    connectionPoolSize: 32
    # 连接空闲超时，单位：毫秒
    idleConnectionTimeout: 10000
    # 命令等待超时，单位：毫秒
    timeout: 3000
    # 发布和订阅连接池大小
    subscriptionConnectionPoolSize: 50

chrome:
  path: /usr/local/chrome-test/chrome
  driver:
    path: /usr/local/chrome-test/chromeDriver/chromedriver-linux64/chromedriver