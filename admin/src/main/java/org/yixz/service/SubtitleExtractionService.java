package org.yixz.service;

import jakarta.annotation.PostConstruct;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.web.multipart.MultipartFile;
import org.yixz.common.exception.AudioConversionException;
import org.yixz.common.converter.SubtitleExtractor;
import org.yixz.common.converter.SubtitleExtractionResult;
import org.yixz.common.util.MediaConverterUtil;

/**
 * 字幕提取服务
 * 统一管理字幕提取功能
 *
 * <AUTHOR> Assistant
 * @since 2025-01-22
 */
@Slf4j
@Service
public class SubtitleExtractionService {

    private boolean subtitleExtractionAvailable = false;

    @PostConstruct
    public void init() {
        // 检查字幕提取功能可用性
        subtitleExtractionAvailable = SubtitleExtractor.isSubtitleExtractionSupported();

        // 检查具体的提取器可用性
        boolean ffmpegAvailable = MediaConverterUtil.isFFmpegAvailable();
        boolean javeAvailable = MediaConverterUtil.isJaveAvailable();

        log.info("字幕提取功能可用性: {}", subtitleExtractionAvailable);
        log.info("FFmpeg可用性: {}", ffmpegAvailable);
        log.info("JAVE2可用性: {}", javeAvailable);

        if (!subtitleExtractionAvailable) {
            log.warn("字幕提取功能不可用！请安装FFmpeg或确保JAVE2依赖正确");
        } else {
            if (ffmpegAvailable) {
                log.info("字幕提取功能已就绪，优先使用FFmpeg，支持格式: {}", String.join(", ", SubtitleExtractor.getSupportedSubtitleFormats()));
            } else {
                log.info("字幕提取功能已就绪，使用JAVE2（功能有限），支持格式: {}", String.join(", ", SubtitleExtractor.getSupportedSubtitleFormats()));
            }
        }
    }

    /**
     * 从视频文件中提取所有字幕
     * 
     * @param videoFile 视频文件
     * @return 字幕提取结果
     */
    public SubtitleExtractionResult extractSubtitles(MultipartFile videoFile) {
        try {
            log.info("开始提取字幕，文件: {}", videoFile.getOriginalFilename());
            
            // 检查功能可用性
            if (!subtitleExtractionAvailable) {
                throw new AudioConversionException("字幕提取功能不可用，请确保JAVE2依赖正确或安装FFmpeg");
            }
            
            return SubtitleExtractor.extractSubtitles(videoFile);
            
        } catch (AudioConversionException e) {
            // 字幕提取异常直接抛出，不包装
            throw e;
        } catch (Exception e) {
            log.error("字幕提取服务异常", e);
            throw new AudioConversionException("字幕提取服务异常: " + e.getMessage(), e);
        }
    }

    /**
     * 从视频文件中提取指定字幕轨道
     * 
     * @param videoFile 视频文件
     * @param trackIndex 字幕轨道索引
     * @return 字幕提取结果
     */
    public SubtitleExtractionResult extractSubtitleTrack(MultipartFile videoFile, int trackIndex) {
        try {
            log.info("开始提取字幕轨道，文件: {}, 轨道索引: {}", videoFile.getOriginalFilename(), trackIndex);
            
            // 检查功能可用性
            if (!subtitleExtractionAvailable) {
                throw new AudioConversionException("字幕提取功能不可用，请确保JAVE2依赖正确或安装FFmpeg");
            }
            
            return SubtitleExtractor.extractSubtitleTrack(videoFile, trackIndex);
            
        } catch (AudioConversionException e) {
            throw e;
        } catch (Exception e) {
            log.error("字幕轨道提取服务异常", e);
            throw new AudioConversionException("字幕轨道提取服务异常: " + e.getMessage(), e);
        }
    }

    /**
     * 检查视频文件是否包含字幕
     * 
     * @param videoFile 视频文件
     * @return 是否包含字幕
     */
    public boolean hasSubtitles(MultipartFile videoFile) {
        try {
            if (!subtitleExtractionAvailable) {
                return false;
            }
            
            // 简单检测：基于文件扩展名
            String filename = videoFile.getOriginalFilename();
            if (filename == null) {
                return false;
            }
            
            String lowerFilename = filename.toLowerCase();
            return lowerFilename.endsWith(".mkv") || 
                   lowerFilename.endsWith(".mp4") || 
                   lowerFilename.endsWith(".avi") ||
                   lowerFilename.endsWith(".mov");
                   
        } catch (Exception e) {
            log.error("检查字幕失败", e);
            return false;
        }
    }

    /**
     * 获取支持的字幕格式
     * 
     * @return 支持的字幕格式数组
     */
    public String[] getSupportedSubtitleFormats() {
        return SubtitleExtractor.getSupportedSubtitleFormats();
    }

    /**
     * 清理字幕提取结果中的临时文件
     * 
     * @param result 字幕提取结果
     */
    public void cleanupExtractionResult(SubtitleExtractionResult result) {
        SubtitleExtractor.cleanupExtractionResult(result);
    }

    /**
     * 获取服务状态
     *
     * @return 服务状态
     */
    public ServiceStatus getServiceStatus() {
        ServiceStatus status = new ServiceStatus();
        status.setSubtitleExtractionAvailable(subtitleExtractionAvailable);
        status.setSupportedFormats(SubtitleExtractor.getSupportedSubtitleFormats());

        boolean ffmpegAvailable = MediaConverterUtil.isFFmpegAvailable();
        boolean javeAvailable = MediaConverterUtil.isJaveAvailable();

        if (subtitleExtractionAvailable) {
            if (ffmpegAvailable) {
                status.setExtractorType("FFmpeg");
                status.setMessage("字幕提取功能已就绪，优先使用FFmpeg");
            } else if (javeAvailable) {
                status.setExtractorType("JAVE2");
                status.setMessage("字幕提取功能已就绪，使用JAVE2（功能有限）");
            }
        } else {
            status.setExtractorType("无");
            status.setMessage("字幕提取功能不可用，请安装FFmpeg或确保JAVE2依赖正确");
        }

        return status;
    }

    /**
     * 服务状态类
     */
    public static class ServiceStatus {
        private boolean subtitleExtractionAvailable;
        private String extractorType;
        private String message;
        private String[] supportedFormats;

        // Getters and Setters
        public boolean isSubtitleExtractionAvailable() {
            return subtitleExtractionAvailable;
        }

        public void setSubtitleExtractionAvailable(boolean subtitleExtractionAvailable) {
            this.subtitleExtractionAvailable = subtitleExtractionAvailable;
        }

        public String getExtractorType() {
            return extractorType;
        }

        public void setExtractorType(String extractorType) {
            this.extractorType = extractorType;
        }

        public String getMessage() {
            return message;
        }

        public void setMessage(String message) {
            this.message = message;
        }

        public String[] getSupportedFormats() {
            return supportedFormats;
        }

        public void setSupportedFormats(String[] supportedFormats) {
            this.supportedFormats = supportedFormats;
        }

        @Override
        public String toString() {
            return String.format("ServiceStatus{available=%s, extractor='%s', message='%s', formats=%s}",
                    subtitleExtractionAvailable, extractorType, message, 
                    supportedFormats != null ? String.join(",", supportedFormats) : "none");
        }
    }
}
