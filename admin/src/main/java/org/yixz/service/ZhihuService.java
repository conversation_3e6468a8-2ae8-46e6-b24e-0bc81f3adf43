package org.yixz.service;

import com.vladsch.flexmark.html2md.converter.FlexmarkHtmlConverter;
import com.vladsch.flexmark.util.data.MutableDataSet;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.jsoup.Jsoup;
import org.jsoup.nodes.Document;
import org.jsoup.nodes.Element;
import org.openqa.selenium.Cookie;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.yixz.common.enums.LinkNoteTypeEnum;
import org.yixz.common.util.OkHttpUtil;
import org.yixz.common.util.RedisUtil;
import org.yixz.entity.vo.LinkNoteVo;
import java.time.Duration;
import java.util.HashMap;
import java.util.Map;
import java.util.regex.Matcher;
import java.util.regex.Pattern;

/**
 * 提取知乎分享链接文案
 */
@Slf4j
@Service
public class ZhihuService {

    @Autowired
    private ChromeService chromeService;

    private static String LINK_URL = "https?://zhuanlan\\.zhihu\\.com/\\S+";

    private static String RDS_ZHIHU_ZSE_CK = "zhihu_zse_ck";

    private static String COOKIE_NAME = "__zse_ck";


    public LinkNoteVo getNote(String shareLink) {
        LinkNoteVo linkNoteVo = new LinkNoteVo();
        linkNoteVo.setLinkType(LinkNoteTypeEnum.ZHIHU.getCode());
        //提取链接
        String link = extractLink(shareLink);
        if(StringUtils.isEmpty(link)) {
            return linkNoteVo;
        }
        //获取cookie
        String zseCk = getCookie(link);
        if(StringUtils.isEmpty(zseCk)) {
            return linkNoteVo;
        }
        String html = getHtml(link, zseCk);
        if(StringUtils.isEmpty(html)) {
            return linkNoteVo;
        }
        initLinkNoteVo(linkNoteVo, html);
        return linkNoteVo;
    }

    public String extractLink(String link) {
        Pattern pattern1 = Pattern.compile(LINK_URL);
        Matcher matcher1 = pattern1.matcher(link);
        if (matcher1.find()) {
            return matcher1.group();
        }
        return null;
    }

    public void initLinkNoteVo(LinkNoteVo linkNoteVo, String html) {
        // 使用 Jsoup 解析 HTML
        Document document = Jsoup.parse(html);
        //标题 <h1 class="PoDocument documentst-Title">Python神器：三行代码让你的程序秒变GUI界面</h1>
        // 使用 CSS 选择器定位 <h1 class="Post-Title"> 标签
        Element h1Element = document.select("h1.Post-Title").first();
        // 提取并打印文本内容
        if (h1Element != null) {
            linkNoteVo.setTitle(h1Element.text());
        }
        Element element = document.select("div.Post-RichText").first();
        if(element!=null) {
            linkNoteVo.setContent(convertHtml2Markdown(element.html()));
        }
    }

    public String convertHtml2Markdown(String html) {
        MutableDataSet options = new MutableDataSet();
        // 设置转换选项（可选）
        FlexmarkHtmlConverter converter = FlexmarkHtmlConverter.builder(options).build();
        return converter.convert(html);
    }

    public String getHtml(String url, String zseCk) {
        Map<String, String> headers = new HashMap<>();
        String cookie = String.format("%s=%s", COOKIE_NAME, zseCk);
        headers.put("Cookie", cookie);
        return OkHttpUtil.doGet(url, headers);
    }

    /**
     * 获取知乎的zse_ck
     * @param link
     * @return
     */
    public String getCookie(String link) {
        String zseCk = RedisUtil.getCacheObject(RDS_ZHIHU_ZSE_CK);
        if(StringUtils.isNotEmpty(zseCk)) {
            return zseCk;
        }
        Cookie cookie = chromeService.getCookie(link, COOKIE_NAME);
        if(cookie!=null) {
            RedisUtil.setCacheObject(RDS_ZHIHU_ZSE_CK, cookie.getValue(), Duration.ofHours(6));
            return cookie.getValue();
        }
        return null;
    }
}
