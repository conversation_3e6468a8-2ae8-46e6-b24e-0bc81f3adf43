package org.yixz.service;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.yixz.entity.vo.LinkNoteVo;

@Service
public class LinkNoteHandler {
    @Autowired
    private XhsService xhsService;

    @Autowired
    private ZhihuService zhihuService;

    @Autowired
    private ToutService toutService;

    public LinkNoteVo getNote(String link) {
        if(link.contains(".xiaohongshu.com")) {
            return xhsService.getNote(link);
        }else if(link.contains(".zhihu.com")) {
            return zhihuService.getNote(link);
        }else if(link.contains(".toutiao.com")) {
            return toutService.getNote(link);
        }
        return null;
    }
}
