package org.yixz.service;

import org.yixz.entity.dto.AsrRequestDto;
import org.yixz.entity.dto.OcrRequestDto;
import org.yixz.entity.vo.AsrResponseVo;
import org.yixz.entity.vo.OcrResponseVo;

/**
 * 语音识别服务接口
 *
 * <AUTHOR>
 * @date 2025-07-20
 */
public interface ProviderService {
    
    /**
     * 语音转文字
     *
     * @param request 语音识别请求参数
     * @return 识别结果
     */
    AsrResponseVo speechToText(AsrRequestDto request);

    /**
     * 图片转文字
     *
     * @param request 图片识别请求参数
     * @return 识别结果
     */
    OcrResponseVo imageToText(OcrRequestDto request);
    
    /**
     * 获取服务提供商名称
     *
     * @return 提供商名称
     */
    String getProviderName();
    
    /**
     * 检查服务是否可用
     *
     * @return 是否可用
     */
    boolean isAvailable();
}
