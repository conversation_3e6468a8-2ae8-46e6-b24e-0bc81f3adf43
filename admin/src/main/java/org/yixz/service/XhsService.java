package org.yixz.service;

import com.alibaba.fastjson2.JSONObject;
import lombok.extern.slf4j.Slf4j;
import org.jsoup.Jsoup;
import org.jsoup.nodes.Document;
import org.jsoup.nodes.Element;
import org.jsoup.select.Elements;
import org.springframework.beans.BeanUtils;
import org.springframework.stereotype.Component;
import org.yixz.common.enums.LinkNoteTypeEnum;
import org.yixz.common.util.OkHttpUtil;
import org.yixz.entity.vo.LinkNoteVo;
import org.yixz.entity.vo.xhs.XhsNote;
import org.yixz.entity.vo.xhs.XhsNoteImage;
import org.yixz.entity.vo.xhs.XhsNoteStreamVideo;

import java.util.ArrayList;
import java.util.Iterator;
import java.util.List;
import java.util.Set;
import java.util.regex.Matcher;
import java.util.regex.Pattern;

/**
 * 描述
 *
 * <AUTHOR>
 * @date 2021年11月29日 16:47
 */
@Slf4j
@Component
public class XhsService {
    public static String LINK_URL = "https?://www\\.xiaohongshu\\.com/explore/\\S+";
    public static String SHARE_URL = "https?://www\\.xiaohongshu\\.com/discovery/item/\\S+";
    public static String SHORT_URL = "https?://xhslink\\.com/[^\\s\"<>\\\\^`{|}，。；！？、【】《》]+";

    /**
     * 提取小红书链接
     * @param link
     * @return
     */
    public String extractLink(String link) {
        Pattern pattern1 = Pattern.compile(LINK_URL);
        Matcher matcher1 = pattern1.matcher(link);
        if (matcher1.find()) {
            return matcher1.group();
        }

        Pattern pattern2 = Pattern.compile(SHARE_URL);
        Matcher matcher2 = pattern2.matcher(link);
        if (matcher2.find()) {
            return matcher2.group();
        }

        Pattern pattern3 = Pattern.compile(SHORT_URL);
        Matcher matcher3 = pattern3.matcher(link);
        if (matcher3.find()) {
            return matcher3.group();
        }
        return null;
    }

    public String getHtml(String url) {
        return OkHttpUtil.doGet(url);
    }

    public String getDataFromHtml(String html) {
        // 使用 Jsoup 解析 HTML
        Document document = Jsoup.parse(html);
        Elements scripts = document.select("script");
        String result = null;
        for (Element script : scripts) {
            String scriptData = script.html();
            if (scriptData.contains("window.__INITIAL_STATE__=")) {
                result = scriptData.replace("window.__INITIAL_STATE__=", "");
                result = result.replace("undefined", "null");
                break;
            }
        }
        return result;
    }

    public XhsNote parseStrToXhsNote(String json) {
        JSONObject jsonObject = JSONObject.parseObject(json);
        // 获取嵌套属性值
        if (jsonObject.containsKey("note") && jsonObject.getJSONObject("note") != null) {
            JSONObject note = jsonObject.getJSONObject("note");
            if (note.containsKey("noteDetailMap") && note.getJSONObject("noteDetailMap") != null) {
                JSONObject noteDetailMap = note.getJSONObject("noteDetailMap");
                Set<String> set = noteDetailMap.keySet();
                Iterator<String> iterator = set.iterator();
                String key = null;
                while (iterator.hasNext()) {
                    key = iterator.next();
                }
                if (noteDetailMap.containsKey(key) && noteDetailMap.getJSONObject(key) != null) {
                    JSONObject keyJson = noteDetailMap.getJSONObject(key);
                    if (keyJson.containsKey("note") && keyJson.getJSONObject("note") != null) {
                        return JSONObject.parseObject(keyJson.getString("note"), XhsNote.class);
                    }
                }
            }
        }
        return null;
    }

    /**
     * 获取小红书的作品
     * @param link
     * @return
     */
    public LinkNoteVo getNote(String link) {
        String url = this.extractLink(link);
        //获取内容
        String html = this.getHtml(url);
        String result = this.getDataFromHtml(html);
        XhsNote xhsNote = this.parseStrToXhsNote(result);
        LinkNoteVo noteVo = new LinkNoteVo();
        BeanUtils.copyProperties(xhsNote, noteVo);
        noteVo.setContent(xhsNote.getDesc());
        noteVo.setLinkType(LinkNoteTypeEnum.XHS.getCode());
        //处理图片
        List<String> imageList = new ArrayList<>();
        List<String> videoList = new ArrayList<>();
        for (XhsNoteImage xhsNoteImage : xhsNote.getImageList()) {
            String[] splitUrl = xhsNoteImage.getUrlDefault().split("/");
            String imageUrl = splitUrl[splitUrl.length-1];
            imageList.add(String.format("https://ci.xiaohongshu.com/%s?imageView2/format/png", imageUrl.split("!")[0]));
            //图文中的视频
            videoList.addAll(xhsNoteImage.getStream().getAv1().stream().map(XhsNoteStreamVideo::getMasterUrl).toList());
            videoList.addAll(xhsNoteImage.getStream().getH265().stream().map(XhsNoteStreamVideo::getMasterUrl).toList());
            videoList.addAll(xhsNoteImage.getStream().getH266().stream().map(XhsNoteStreamVideo::getMasterUrl).toList());
            videoList.addAll(xhsNoteImage.getStream().getH264().stream().map(XhsNoteStreamVideo::getMasterUrl).toList());
        }
        //作品是视频时，获取视频
        if(xhsNote.getVideo()!=null && xhsNote.getVideo().getConsumer()!=null){
            videoList.add(String.format("%s%s", "https://sns-video-bd.xhscdn.com/", xhsNote.getVideo().getConsumer().getOriginVideoKey()));
        }
        noteVo.setImageList(imageList);
        noteVo.setVideoList(videoList);
        return noteVo;
    }


}
