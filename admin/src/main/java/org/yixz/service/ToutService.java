package org.yixz.service;

import com.vladsch.flexmark.html2md.converter.FlexmarkHtmlConverter;
import com.vladsch.flexmark.util.data.MutableDataSet;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.jsoup.Jsoup;
import org.jsoup.nodes.Document;
import org.jsoup.nodes.Element;
import org.openqa.selenium.Cookie;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.yixz.common.enums.LinkNoteTypeEnum;
import org.yixz.common.util.OkHttpUtil;
import org.yixz.common.util.RedisUtil;
import org.yixz.entity.vo.LinkNoteVo;
import java.time.Duration;
import java.util.HashMap;
import java.util.Map;

/**
 * 提取头条分享链接文案
 */
@Slf4j
@Service
public class ToutService {

    @Autowired
    private ChromeService chromeService;

    private static String RDS_TOUTIAO_TTWID = "toutiao_ttwid";

    private static String COOKIE_NAME = "ttwid";


    public LinkNoteVo getNote(String link) {
        LinkNoteVo linkNoteVo = new LinkNoteVo();
        linkNoteVo.setLinkType(LinkNoteTypeEnum.TOUTIAO.getCode());
        //获取cookie
        String zseCk = getCookie(link);
        if(StringUtils.isEmpty(zseCk)) {
            return linkNoteVo;
        }
        String html = getHtml(link, zseCk);
        if(StringUtils.isEmpty(html)) {
            return linkNoteVo;
        }
        initLinkNoteVo(linkNoteVo, html);
        return linkNoteVo;
    }

    public void initLinkNoteVo(LinkNoteVo linkNoteVo, String html) {
        // 使用 Jsoup 解析 HTML
        Document document = Jsoup.parse(html);
        //标题，从 element 中选择 h1 元素
        Element h1Element = document.select("h1.article__title").first();
        // 提取并打印文本内容
        if (h1Element != null) {
            linkNoteVo.setTitle(h1Element.text());
        }
        Element element = document.select("article").first();
        if(element!=null) {
            linkNoteVo.setContent(convertHtml2Markdown(element.html()));
        }
    }

    public String convertHtml2Markdown(String html) {
        MutableDataSet options = new MutableDataSet();
        // 设置转换选项（可选）
        FlexmarkHtmlConverter converter = FlexmarkHtmlConverter.builder(options).build();
        return converter.convert(html);
    }

    public String getHtml(String url, String zseCk) {
        Map<String, String> headers = new HashMap<>();
        String cookie = String.format("%s=%s", COOKIE_NAME, zseCk);
        headers.put("Cookie", cookie);
        return OkHttpUtil.doGet(url, headers);
    }

    /**
     * 获取头头条的cookie
     * @param link
     * @return
     */
    public String getCookie(String link) {
        String zseCk = RedisUtil.getCacheObject(RDS_TOUTIAO_TTWID);
        if(StringUtils.isNotEmpty(zseCk)) {
            return zseCk;
        }
        Cookie cookie = chromeService.getCookie(link, COOKIE_NAME);
        if(cookie!=null) {
            RedisUtil.setCacheObject(RDS_TOUTIAO_TTWID, cookie.getValue(), Duration.ofHours(6));
            return cookie.getValue();
        }
        return null;
    }
}
