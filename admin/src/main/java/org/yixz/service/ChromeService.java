package org.yixz.service;

import lombok.extern.slf4j.Slf4j;
import org.openqa.selenium.Cookie;
import org.openqa.selenium.WebDriver;
import org.openqa.selenium.chrome.ChromeDriver;
import org.openqa.selenium.chrome.ChromeOptions;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;

/**
 * 操作谷歌浏览器
 */
@Slf4j
@Service
public class ChromeService {

    @Value("${chrome.path:}")
    private String chromePath;

    @Value("${chrome.driver.path:}")
    private String chromeDriverPath;

    /**
     * 从浏览器获取指定的cookie
     * @param link
     * @param cookieName
     * @return
     */
    public Cookie getCookie(String link, String cookieName) {
        log.info("谷歌浏览器地址：{}， 驱动地址：{}", chromePath, chromeDriverPath);
        //设置驱动路径
        System.setProperty("webdriver.chrome.driver", chromeDriverPath);
        // 创建ChromeOptions实例
        ChromeOptions chromeOptions = new ChromeOptions();
        //设置浏览器测试版二进制文件路径
        chromeOptions.setBinary(chromePath);
        // 禁用自动化控制标志
        chromeOptions.addArguments("--disable-blink-features=AutomationControlled");
        // 设置实验性选项（隐藏自动化特征）
        chromeOptions.setExperimentalOption("excludeSwitches", new String[]{"enable-automation"});
        //后台运行
        chromeOptions.addArguments("--headless");
        chromeOptions.addArguments("--disable-gpu");
        chromeOptions.addArguments("--no-sandbox");
        //使用真实浏览器的User-Agent
        chromeOptions.addArguments("user-agent=Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/123.0.0.0 Safari/537.36");
        WebDriver driver = new ChromeDriver(chromeOptions);
        Cookie cookie = null;
        try {
            driver.get(link);
            // 随机等待 1~3 秒
            Thread.sleep((int) (Math.random() * 2000) + 1000);
            // 获取Cookies并打印
            cookie =  driver.manage().getCookieNamed(cookieName);
            if(cookie!=null) {
                log.info("获取{}的cookie成功，cookie信息：{}", link, cookie);
            }
        }catch (Exception e) {
            log.error("获取网页失败", e);
        }finally {
            driver.quit();
        }
        return cookie;
    }
}
