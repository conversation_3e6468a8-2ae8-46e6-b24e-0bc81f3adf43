package org.yixz.service;

import com.alibaba.fastjson.JSONObject;
import org.yixz.entity.vo.TokenVO;
import org.yixz.entity.vo.WeChatMiniAuthorizeVo;



public interface WechatService {

    /**
     * 微信登录小程序授权登录
     *
     * @param code code
     * @return 登录成功用户信息
     */
    TokenVO weChatAuthorizeProgramLogin(JSONObject code);

//    /**
//     * 微信登录小程序获取手机号，返回
//     * @param json
//     * @return
//     */
//    TokenVO getBindingPhone(JSONObject json);

    /**
     * 小程序登录凭证校验
     *
     * @return 小程序登录校验对象
     */
    WeChatMiniAuthorizeVo miniAuthCode(JSONObject code);

    /**
     * 获取小程序accessToken
     *
     * @return 小程序accessToken
     */
    String getMiniAccessToken();

    /**
     * 获取公众号accessToken
     *
     * @return 公众号accessToken
     */
    String getPublicAccessToken();






}
