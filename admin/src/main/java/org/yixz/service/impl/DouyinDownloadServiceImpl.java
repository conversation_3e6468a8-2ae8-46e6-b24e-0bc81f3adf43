package org.yixz.service.impl;

import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.yixz.common.properties.VideoDownloadProperties;
import org.yixz.common.enums.VideoPlatformEnum;
import org.yixz.common.util.BiliUtil;
import org.yixz.entity.dto.VideoDownloadRequestDto;
import org.yixz.entity.vo.VideoDownloadResponseVo;
import org.yixz.service.VideoDownloadService;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * 抖音视频下载服务实现
 *
 * <AUTHOR>
 * @date 2025-07-26
 */
@Slf4j
@Service("douyin")
public class DouyinDownloadServiceImpl implements VideoDownloadService {
    
    @Autowired
    private VideoDownloadProperties videoDownloadProperties;
    
    @Override
    public VideoDownloadResponseVo downloadVideo(VideoDownloadRequestDto request) {
        long startTime = System.currentTimeMillis();
        
        try {
            log.info("开始下载抖音视频: {}", request.getUrl());
            
            // 验证配置
            if (!videoDownloadProperties.getDouyin().isEnabled()) {
                throw new RuntimeException("抖音下载器已禁用");
            }
            
            // 验证URL
            if (!supports(request.getUrl())) {
                throw new RuntimeException("不支持的抖音URL格式");
            }
            
            // 构建响应
            VideoDownloadResponseVo response = new VideoDownloadResponseVo();
            response.setPlatform(VideoPlatformEnum.DOUYIN.getCode());
            response.setDownloader(getDownloaderName());
            response.setProcessTime(System.currentTimeMillis() - startTime);
            
            // TODO: 实现具体的抖音视频下载逻辑
            // 这里需要调用抖音API或解析页面获取视频信息
            response = parseDouyinVideo(request, response);
            
            log.info("抖音视频下载完成，耗时: {}ms", response.getProcessTime());
            return response;
            
        } catch (Exception e) {
            log.error("抖音视频下载失败: {}", e.getMessage(), e);
            
            VideoDownloadResponseVo errorResponse = new VideoDownloadResponseVo();
            errorResponse.setPlatform(VideoPlatformEnum.DOUYIN.getCode());
            errorResponse.setDownloader(getDownloaderName());
            errorResponse.setErrorMessage("抖音视频下载失败: " + e.getMessage());
            errorResponse.setProcessTime(System.currentTimeMillis() - startTime);
            
            return errorResponse;
        }
    }
    
    @Override
    public VideoDownloadResponseVo getVideoInfo(VideoDownloadRequestDto request) {
        try {
            log.info("获取抖音视频信息: {}", request.getUrl());
            
            // 验证URL
            if (!supports(request.getUrl())) {
                throw new RuntimeException("不支持的抖音URL格式");
            }

            List<Map<String, String>> res = new ArrayList<Map<String, String>>();
            String parseEntry = BiliUtil.parseEntry(request.getUrl());
            String api = "";
            if (parseEntry.contains("BV")) {
                api = "https://api.bilibili.com/x/web-interface/view?bvid=" + parseEntry.substring(2, parseEntry.length());
            }
            if (parseEntry.contains("av")) {
                api = "https://api.bilibili.com/x/web-interface/view?aid=" + parseEntry.substring(2, parseEntry.length());
            }
            String serchPersion = BiliUtil.httpGetBili(api, "UTF-8", null);
            JSONObject videoData = JSONObject.parseObject(serchPersion);
            if (videoData.getString("code").equals("0")) {
                // 优化多集问题 从page 里取

                String bvid = videoData.getJSONObject("data").getString("bvid");
                String aid = videoData.getJSONObject("data").getString("aid");
                String desc = videoData.getJSONObject("data").getString("desc");
                JSONObject dimension = videoData.getJSONObject("data").getJSONObject("dimension");
                Integer width = dimension.getInteger("width");
                Integer height = dimension.getInteger("height");

                JSONArray jsonArray = videoData.getJSONObject("data").getJSONArray("pages");

                for (int i = 0; i < jsonArray.size(); i++) {
                    Map<String, String> data = new HashMap<String, String>();
                    JSONObject jsonObject = jsonArray.getJSONObject(i);
                    String cid = jsonObject.getString("cid");
                    String title = jsonObject.getString("part");
                    String pic = videoData.getJSONObject("data").getString("pic");
                    data.put("aid", aid);
                    data.put("bvid", bvid);
                    data.put("desc", desc);
                    if (width >= 1920 || height >= 1080) {
                        data.put("quality", "1");
                    } else {
                        data.put("quality", "0");
                    }
                    if (null == pic) {
                        pic = jsonObject.getString("first_frame");
                    }
                    data.put("cid", cid);
                    data.put("title", title);
                    data.put("pic", pic);
                    data.put("owner", videoData.getJSONObject("data").getString("owner"));
                    data.put("ctime", videoData.getJSONObject("data").getString("ctime"));
                    res.add(data);
                }
            }
            
            // TODO: 实现获取视频信息的逻辑
            VideoDownloadResponseVo response = new VideoDownloadResponseVo();
            response.setPlatform(VideoPlatformEnum.DOUYIN.getCode());
            response.setDownloader(getDownloaderName());
            
            return response;
            
        } catch (Exception e) {
            log.error("获取抖音视频信息失败: {}", e.getMessage(), e);
            throw new RuntimeException("获取抖音视频信息失败: " + e.getMessage(), e);
        }
    }
    
    @Override
    public String getDownloaderName() {
        return VideoPlatformEnum.DOUYIN.getName() + "专用下载器";
    }
    
    @Override
    public boolean isAvailable() {
        return videoDownloadProperties.getDouyin().isEnabled();
    }
    
    @Override
    public boolean supports(String url) {
        if (url == null || url.trim().isEmpty()) {
            return false;
        }
        
        String lowerUrl = url.toLowerCase();
        return lowerUrl.contains("douyin.com") || lowerUrl.contains("iesdouyin.com");
    }
    
    /**
     * 解析抖音视频
     */
    private VideoDownloadResponseVo parseDouyinVideo(VideoDownloadRequestDto request, VideoDownloadResponseVo response) {
        // TODO: 实现抖音视频解析逻辑
        // 这里应该包含：
        // 1. 解析视频页面或调用API
        // 2. 提取视频信息（标题、作者、描述等）
        // 3. 获取视频文件URL
        // 4. 处理不同质量的视频
        
        // 临时模拟数据
        response.setTitle("抖音视频标题");
        response.setAuthor("抖音作者");
        response.setDescription("抖音视频描述");
        response.setDuration(30L);
        response.setThumbnail("https://example.com/thumbnail.jpg");
        
        // 模拟视频文件信息
        List<VideoDownloadResponseVo.VideoFileInfo> videoFiles = new ArrayList<>();
        VideoDownloadResponseVo.VideoFileInfo videoFile = new VideoDownloadResponseVo.VideoFileInfo();
        videoFile.setUrl("https://example.com/video.mp4");
        videoFile.setFormat("mp4");
        videoFile.setQuality("720p");
        videoFile.setResolution("720x1280");
        videoFile.setVideoCodec("h264");
        videoFile.setAudioCodec("aac");
        videoFiles.add(videoFile);
        
        response.setVideoFiles(videoFiles);
        
        return response;
    }
}
