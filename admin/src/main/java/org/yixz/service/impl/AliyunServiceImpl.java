package org.yixz.service.impl;

import cn.hutool.core.util.ObjectUtil;
import com.alibaba.fastjson2.JSONObject;
import com.alibaba.nls.client.protocol.InputFormatEnum;
import com.alibaba.nls.client.protocol.NlsClient;
import com.alibaba.nls.client.protocol.SampleRateEnum;
import com.alibaba.nls.client.protocol.asr.SpeechRecognizer;
import com.alibaba.nls.client.protocol.asr.SpeechTranscriber;
import com.aliyun.ocr_api20210707.Client;
import com.aliyun.ocr_api20210707.models.RecognizeGeneralRequest;
import com.aliyun.ocr_api20210707.models.RecognizeGeneralResponse;
import com.aliyun.tea.TeaException;
import com.aliyun.teaopenapi.models.Config;
import com.aliyun.teautil.models.RuntimeOptions;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.util.StringUtils;
import org.yixz.common.properties.AiServiceProperties;
import org.yixz.common.converter.AudioFormatDetector;
import org.yixz.common.enums.AiProviderEnum;
import org.yixz.common.exception.BizException;
import org.yixz.common.handler.AsrCallbackHandler;
import org.yixz.common.manager.AliyunNlsClientManager;
import org.yixz.common.util.RetryUtil;
import org.yixz.entity.dto.AsrRequestDto;
import org.yixz.entity.dto.OcrRequestDto;
import org.yixz.entity.vo.AsrResponseVo;
import org.yixz.entity.vo.OcrResponseVo;
import org.yixz.service.AudioConversionService;
import org.yixz.service.ProviderService;

import java.io.InputStream;

/**
 * 阿里云语音识别/图片识别服务实现（优化版）
 * 
 * 主要优化点：
 * 1. AccessToken自动管理和刷新
 * 2. NlsClient生命周期管理
 * 3. 统一的异步回调处理
 * 4. 重试机制和异常处理
 * 5. 配置化参数
 *
 * @date 2025-07-21
 */
@Slf4j
@Service("aliyun")
public class AliyunServiceImpl implements ProviderService {

    @Autowired
    private AiServiceProperties aiServiceProperties;

    @Autowired
    private AliyunNlsClientManager nlsClientManager;

    @Autowired
    private AudioConversionService audioConversionService;

    @Override
    public AsrResponseVo speechToText(AsrRequestDto request) {
        long startTime = System.currentTimeMillis();

        return RetryUtil.executeWithConditionalRetry(() -> {
            AudioConversionService.ConversionResult conversionResult = null;
            try {
                // 验证配置
                validateConfig();

                // 音频格式转换（如果需要）
                conversionResult = audioConversionService.convertForAliyunASR(request.getAudioFile());

                if (!conversionResult.isSuccess()) {
                    throw new RuntimeException("音频格式转换失败: " + conversionResult.getMessage());
                }

                log.info("音频转换结果: {}", conversionResult);

                // 使用转换后的音频流进行识别
                String result = callAliyunAsrApiWithStream(conversionResult.getAudioStream(), conversionResult.getTargetInfo());

                // 构建响应
                AsrResponseVo response = new AsrResponseVo();
                response.setText(result);
                response.setProvider(getProviderName());
                response.setProcessTime(System.currentTimeMillis() - startTime);

                // 添加转换信息到响应中
                if (conversionResult.getSourceInfo() != null) {
                    response.setOriginalFormat(conversionResult.getSourceInfo().getFormat());
                    response.setConvertedFormat(conversionResult.getTargetInfo() != null ?
                        conversionResult.getTargetInfo().getFormat() : "未知");
                    response.setConverterUsed(conversionResult.getConverterUsed());
                    response.setConversionTime(conversionResult.getConversionTimeMs());
                }

                log.info("阿里云ASR识别完成，耗时: {}ms", response.getProcessTime());
                return response;

            } catch (org.yixz.common.exception.AudioConversionException e) {
                // 音频转换异常直接抛出，不进行重试
                log.error("音频转换失败: {}", e.getMessage());
                throw e;
            } catch (Exception e) {
                log.error("阿里云ASR识别失败: {}", e.getMessage());
                throw new RuntimeException("语音识别失败: " + e.getMessage(), e);
            } finally {
                // 清理临时文件
                if (conversionResult != null) {
                    conversionResult.cleanup();
                }
            }
        },
        aiServiceProperties.getAliyun().getAsr().getMaxRetryAttempts(),
        aiServiceProperties.getAliyun().getAsr().getRetryDelayMs(),
        "阿里云ASR识别",
        RetryUtil::isRetryableException);
    }

    @Override
    public OcrResponseVo imageToText(OcrRequestDto request) {
        long startTime = System.currentTimeMillis();
        
        return RetryUtil.executeWithConditionalRetry(() -> {
            try {
                // 验证配置
                validateConfig();
                
                // 构建请求
                OcrResponseVo response = callAliyunOcrApi(request);
                
                // 设置响应信息
                response.setProvider(getProviderName());
                response.setProcessTime(System.currentTimeMillis() - startTime);
                
                log.info("阿里云OCR识别完成，耗时: {}ms", response.getProcessTime());
                return response;
                
            } catch (Exception e) {
                log.error("阿里云OCR识别失败", e);
                throw new RuntimeException("图片文字识别失败: " + e.getMessage(), e);
            }
        },
        aiServiceProperties.getAliyun().getOcr().getMaxRetryAttempts(),
        aiServiceProperties.getAliyun().getOcr().getRetryDelayMs(),
        "阿里云OCR识别",
        RetryUtil::isRetryableException);
    }

    private void validateConfig() {
        AiServiceProperties.AliyunConfig config = aiServiceProperties.getAliyun();
        if (!StringUtils.hasText(config.getAccessKeyId()) ||
                !StringUtils.hasText(config.getAccessKeySecret())) {
            throw new IllegalStateException("阿里云ASR/OCR配置不完整，请检查accessKeyId和accessKeySecret");
        }
        if (!StringUtils.hasText(config.getAsr().getAppKey())) {
            throw new IllegalStateException("阿里云ASR配置不完整，请检查appKey");
        }
    }

    /**
     * 统一的ASR调用入口
     * 根据音频长度自动选择合适的识别方式
     */
    @Deprecated
    private String callAliyunAsrApi(AsrRequestDto request) {
        // 这里可以根据音频文件大小或时长选择不同的识别方式
        // 目前默认使用一句话识别，后续可以扩展
        // return callAliyunRecognizerAsrApi(request);
        return callAliyunTranscriberAsrApi(request);
    }

    /**
     * 使用InputStream进行ASR识别
     */
    private String callAliyunAsrApiWithStream(InputStream audioStream, AudioFormatDetector.AudioInfo audioInfo) {
        // 目前使用一句话识别，后续可以扩展
        // return callAliyunRecognizerAsrApiWithStream(audioStream, audioInfo);
        return callAliyunTranscriberAsrApiWithStream(audioStream, audioInfo);
    }

    /**
     * 一句话识别(60秒内) - 使用MultipartFile（已废弃，建议使用带音频转换的版本）
     * @deprecated 使用 callAliyunRecognizerAsrApiWithStream 替代
     */
    @Deprecated
    private String callAliyunRecognizerAsrApi(AsrRequestDto request) {
        log.warn("使用了已废弃的方法 callAliyunRecognizerAsrApi，建议使用音频转换流程");

        // 先进行音频转换，然后调用新的方法
        AudioConversionService.ConversionResult conversionResult = null;
        try {
            conversionResult = audioConversionService.convertForAliyunASR(request.getAudioFile());

            if (!conversionResult.isSuccess()) {
                throw new RuntimeException("音频格式转换失败: " + conversionResult.getMessage());
            }

            return callAliyunRecognizerAsrApiWithStream(conversionResult.getAudioStream(), conversionResult.getTargetInfo());

        } finally {
            // 清理临时文件
            if (conversionResult != null) {
                conversionResult.cleanup();
            }
        }
    }

    /**
     * 实时语音识别（已废弃，建议使用带音频转换的版本）
     * @deprecated 使用 callAliyunTranscriberAsrApiWithStream 替代
     */
    @Deprecated
    private String callAliyunTranscriberAsrApi(AsrRequestDto request) {
        log.warn("使用了已废弃的方法 callAliyunTranscriberAsrApi，建议使用音频转换流程");

        // 先进行音频转换，然后调用新的方法
        AudioConversionService.ConversionResult conversionResult = null;
        try {
            conversionResult = audioConversionService.convertForAliyunASR(request.getAudioFile());

            if (!conversionResult.isSuccess()) {
                throw new RuntimeException("音频格式转换失败: " + conversionResult.getMessage());
            }

            return callAliyunTranscriberAsrApiWithStream(conversionResult.getAudioStream(), conversionResult.getTargetInfo());

        } finally {
            // 清理临时文件
            if (conversionResult != null) {
                conversionResult.cleanup();
            }
        }
    }

    /**
     * 一句话识别(60秒内) - 使用InputStream
     */
    private String callAliyunRecognizerAsrApiWithStream(InputStream audioStream, AudioFormatDetector.AudioInfo audioInfo) {
        AiServiceProperties.AliyunConfig config = aiServiceProperties.getAliyun();
        AiServiceProperties.AliyunConfig.AsrConfig asrConfig = config.getAsr();
        NlsClient client = nlsClientManager.getClient();
        SpeechRecognizer recognizer = null;

        try {
            // 创建回调处理器
            AsrCallbackHandler.RecognizerResultHandler resultHandler =
                new AsrCallbackHandler.RecognizerResultHandler("user-param", 1);

            // 创建识别器
            recognizer = new SpeechRecognizer(client, resultHandler.createListener());
            recognizer.setAppKey(asrConfig.getAppKey());

            // 根据转换后的音频信息配置识别器
            if (audioInfo != null) {
                // 使用实际的音频格式信息
                String basicFormat = AudioFormatDetector.extractBasicFormat(audioInfo.getFormat(), AiProviderEnum.ALIYUN);
                recognizer.setFormat(getInputFormat(basicFormat));
                recognizer.setSampleRate(getSampleRate(audioInfo.getSampleRate()));
                log.debug("使用音频信息配置识别器: 格式={}, 采样率={}Hz", basicFormat, audioInfo.getSampleRate());
            } else {
                // 使用默认配置
                recognizer.setFormat(getInputFormat(asrConfig.getDefaultFormat()));
                recognizer.setSampleRate(getSampleRate(asrConfig.getDefaultSampleRate()));
                log.debug("使用默认配置: 格式={}, 采样率={}Hz", asrConfig.getDefaultFormat(), asrConfig.getDefaultSampleRate());
            }

            // 配置识别参数
            recognizer.setEnableIntermediateResult(asrConfig.isEnableIntermediateResult());
            recognizer.addCustomedParam("enable_voice_detection", asrConfig.isEnableVoiceDetection());

            // 开始识别
            long startTime = System.currentTimeMillis();
            recognizer.start();
            log.debug("ASR启动耗时: {}ms", System.currentTimeMillis() - startTime);

            // 发送音频数据 - 使用实际的采样率
            int actualSampleRate = audioInfo != null ? audioInfo.getSampleRate() : asrConfig.getDefaultSampleRate();
            sendAudioData(recognizer, audioStream, actualSampleRate);

            // 停止识别
            startTime = System.currentTimeMillis();
            recognizer.stop();
            log.debug("ASR停止耗时: {}ms", System.currentTimeMillis() - startTime);

            // 等待结果
            return resultHandler.waitForResult(asrConfig.getMaxWaitTimeMs());

        } catch (Exception e) {
            log.error("阿里云一句话识别失败: {}", e.getMessage());
            throw new RuntimeException("语音识别失败: " + e.getMessage(), e);
        } finally {
            // 只关闭recognizer，不关闭client
            if (recognizer != null) {
                try {
                    recognizer.close();
                } catch (Exception e) {
                    log.warn("关闭SpeechRecognizer时发生异常", e);
                }
            }
        }
    }

    /**
     * 实时语音识别 - 使用InputStream
     */
    private String callAliyunTranscriberAsrApiWithStream(InputStream audioStream, AudioFormatDetector.AudioInfo audioInfo) {
        AiServiceProperties.AliyunConfig config = aiServiceProperties.getAliyun();
        AiServiceProperties.AliyunConfig.AsrConfig asrConfig = config.getAsr();
        NlsClient client = nlsClientManager.getClient();
        SpeechTranscriber transcriber = null;

        try {
            // 创建回调处理器
            AsrCallbackHandler.TranscriberResultHandler resultHandler =
                new AsrCallbackHandler.TranscriberResultHandler();

            // 创建转录器
            transcriber = new SpeechTranscriber(client, resultHandler.createListener());
            transcriber.setAppKey(asrConfig.getAppKey());

            // 根据转换后的音频信息配置转录器
            if (audioInfo != null) {
                // 使用实际的音频格式信息
                String basicFormat = AudioFormatDetector.extractBasicFormat(audioInfo.getFormat(), AiProviderEnum.ALIYUN);
                transcriber.setFormat(getInputFormat(basicFormat));
                transcriber.setSampleRate(getSampleRate(audioInfo.getSampleRate()));
                log.debug("使用音频信息配置转录器: 格式={}, 采样率={}Hz", basicFormat, audioInfo.getSampleRate());
            } else {
                // 使用默认配置
                transcriber.setFormat(getInputFormat(asrConfig.getDefaultFormat()));
                transcriber.setSampleRate(getSampleRate(asrConfig.getDefaultSampleRate()));
                log.debug("使用默认配置: 格式={}, 采样率={}Hz", asrConfig.getDefaultFormat(), asrConfig.getDefaultSampleRate());
            }

            // 配置转录参数
            transcriber.setEnableIntermediateResult(asrConfig.isEnableIntermediateResult());
            transcriber.setEnablePunctuation(asrConfig.isEnablePunctuation());
            transcriber.setEnableITN(asrConfig.isEnableITN());

            // 开始转录
            transcriber.start();

            // 发送音频数据 - 使用实际的采样率
            int actualSampleRate = audioInfo != null ? audioInfo.getSampleRate() : asrConfig.getDefaultSampleRate();
            sendAudioData(transcriber, audioStream, actualSampleRate);

            // 停止转录
            long startTime = System.currentTimeMillis();
            transcriber.stop();
            log.debug("ASR转录停止耗时: {}ms", System.currentTimeMillis() - startTime);

            // 等待结果
            return resultHandler.waitForResult(asrConfig.getMaxWaitTimeMs());

        } catch (Exception e) {
            log.error("阿里云实时语音识别失败: {}", e.getMessage());
            throw new RuntimeException("语音识别失败: " + e.getMessage(), e);
        } finally {
            // 只关闭transcriber，不关闭client
            if (transcriber != null) {
                try {
                    transcriber.close();
                } catch (Exception e) {
                    log.warn("关闭SpeechTranscriber时发生异常", e);
                }
            }
        }
    }

    /**
     * 发送音频数据的通用方法
     */
    private void sendAudioData(Object recognizer, InputStream audioStream, int sampleRate) throws Exception {
        byte[] buffer = new byte[3200];
        int len;
        while ((len = audioStream.read(buffer)) > 0) {
            log.debug("发送音频数据包，长度: {}", len);
            
            if (recognizer instanceof SpeechRecognizer) {
                ((SpeechRecognizer) recognizer).send(buffer, len);
            } else if (recognizer instanceof SpeechTranscriber) {
                ((SpeechTranscriber) recognizer).send(buffer, len);
            }
            
            // 模拟实时音频流的延迟
            int sleepTime = AsrCallbackHandler.calculateSleepDelta(len, sampleRate);
            Thread.sleep(sleepTime);
        }
        audioStream.close();
    }

    /**
     * 获取输入格式枚举
     */
    private InputFormatEnum getInputFormat(String format) {
        switch (format.toUpperCase()) {
            case "PCM":
                return InputFormatEnum.PCM;
            case "OPUS":
                return InputFormatEnum.OPUS;
            default:
                log.warn("不支持的音频格式: {}, 使用默认PCM格式", format);
                return InputFormatEnum.PCM;
        }
    }

    /**
     * 获取采样率枚举
     */
    private SampleRateEnum getSampleRate(int sampleRate) {
        switch (sampleRate) {
            case 8000:
                return SampleRateEnum.SAMPLE_RATE_8K;
            case 16000:
                return SampleRateEnum.SAMPLE_RATE_16K;
            default:
                log.warn("不支持的采样率: {}, 使用默认16K", sampleRate);
                return SampleRateEnum.SAMPLE_RATE_16K;
        }
    }

    private OcrResponseVo callAliyunOcrApi(OcrRequestDto request) throws Exception {
        AiServiceProperties.AliyunConfig aliyunConfig = aiServiceProperties.getAliyun();
        Client client = createOcrClient(aliyunConfig);

        try {
            RecognizeGeneralRequest recognizeGeneralRequest = new RecognizeGeneralRequest();
            recognizeGeneralRequest.setBody(request.getImageFile().getInputStream());

            RuntimeOptions runtime = new RuntimeOptions();
            // 设置超时时间
            runtime.connectTimeout = aliyunConfig.getOcr().getConnectTimeoutMs();
            runtime.readTimeout = aliyunConfig.getOcr().getReadTimeoutMs();

            RecognizeGeneralResponse response = client.recognizeGeneralWithOptions(recognizeGeneralRequest, runtime);

            if (ObjectUtil.isNull(response) || ObjectUtil.isNull(response.body)) {
                throw new BizException("OCR识别响应为空");
            }

            OcrResponseVo result = new OcrResponseVo();
            JSONObject resObj = JSONObject.parseObject(response.body.data);
            result.setText(resObj.getString("content"));

            return result;

        } catch (TeaException error) {
            log.error("OCR识别失败 - TeaException: {}, 诊断地址: {}",
                    error.getMessage(), error.getData().get("Recommend"));
            throw new RuntimeException("OCR识别失败: " + error.getMessage(), error);
        } catch (Exception error) {
            log.error("OCR识别失败 - Exception: {}", error.getMessage());
            throw new RuntimeException("OCR识别失败: " + error.getMessage(), error);
        }
    }

    /**
     * 创建OCR客户端
     */
    private Client createOcrClient(AiServiceProperties.AliyunConfig aliyunConfig) throws Exception {
        try {
            Config config = new Config();
            config.setAccessKeyId(aliyunConfig.getAccessKeyId());
            config.setAccessKeySecret(aliyunConfig.getAccessKeySecret());
            config.setEndpoint(aliyunConfig.getOcr().getEndpoint());

            // 设置超时时间
            config.setConnectTimeout(aliyunConfig.getOcr().getConnectTimeoutMs());
            config.setReadTimeout(aliyunConfig.getOcr().getReadTimeoutMs());

            return new Client(config);
        } catch (Exception e) {
            log.error("创建OCR客户端失败", e);
            throw new RuntimeException("创建OCR客户端失败", e);
        }
    }

    @Override
    public String getProviderName() {
        return AiProviderEnum.ALIYUN.getCode();
    }

    @Override
    public boolean isAvailable() {
        try {
            validateConfig();
            // 检查NlsClient是否可用
            return nlsClientManager.isAvailable();
        } catch (Exception e) {
            log.warn("阿里云ASR服务不可用: {}", e.getMessage());
            return false;
        }
    }
}
