package org.yixz.service.impl;

import cn.hutool.core.bean.BeanUtil;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.yixz.common.converter.MediaSeparationResult;
import org.yixz.common.enums.VideoPlatformEnum;
import org.yixz.common.properties.VideoDownloadProperties;
import org.yixz.common.util.BiliUtil;
import org.yixz.common.util.FileUtil;
import org.yixz.common.util.RedisUtil;
import org.yixz.common.util.StringUtil;
import org.yixz.entity.dto.VideoDownloadRequestDto;
import org.yixz.entity.vo.VideoDownloadResponseVo;
import org.yixz.service.AudioConversionService;
import org.yixz.service.VideoDownloadService;

import java.io.File;
import java.util.ArrayList;
import java.util.List;

import static org.yixz.common.constants.RedisConstants.VIDEO_LOGIN_COOKIE;

/**
 * B站视频下载服务实现
 *
 * <AUTHOR>
 * @date 2025-07-26
 */
@Slf4j
@Service("bilibili")
@RequiredArgsConstructor
public class BilibiliDownloadServiceImpl implements VideoDownloadService {
    
    private final VideoDownloadProperties videoDownloadProperties;
    private final AudioConversionService audioConversionService;
    
    @Override
    public VideoDownloadResponseVo downloadVideo(VideoDownloadRequestDto request) {
        long startTime = System.currentTimeMillis();

        log.info("开始下载B站视频: {}", request.getUrl());

        // 验证配置
        if (!videoDownloadProperties.getBilibili().isEnabled()) {
            throw new RuntimeException("B站下载器已禁用");
        }

        // 验证URL
        if (!supports(request.getUrl())) {
            throw new RuntimeException("不支持的B站URL格式");
        }

        // 构建响应
        VideoDownloadResponseVo response = parseBilibiliVideo(request);
        response.setPlatform(VideoPlatformEnum.BILIBILI.getCode());
        response.setDownloader(getDownloaderName());
        response.setProcessTime(System.currentTimeMillis() - startTime);

        log.info("B站视频下载完成，耗时: {}ms", response.getProcessTime());
        return response;
    }
    
    @Override
    public VideoDownloadResponseVo getVideoInfo(VideoDownloadRequestDto request) {
        try {
            log.info("获取B站视频信息: {}", request.getUrl());
            
            // 验证URL
            if (!supports(request.getUrl())) {
                throw new RuntimeException("不支持的B站URL格式");
            }
            
            // TODO: 实现获取视频信息的逻辑
            VideoDownloadResponseVo response = new VideoDownloadResponseVo();
            response.setPlatform(VideoPlatformEnum.BILIBILI.getCode());
            response.setDownloader(getDownloaderName());
            
            return response;
            
        } catch (Exception e) {
            log.error("获取B站视频信息失败: {}", e.getMessage(), e);
            throw new RuntimeException("获取B站视频信息失败: " + e.getMessage(), e);
        }
    }
    
    @Override
    public String getDownloaderName() {
        return VideoPlatformEnum.BILIBILI.getName() + "专用下载器";
    }
    
    @Override
    public boolean isAvailable() {
        return videoDownloadProperties.getBilibili().isEnabled();
    }
    
    @Override
    public boolean supports(String url) {
        if (url == null || url.trim().isEmpty()) {
            return false;
        }
        
        String lowerUrl = url.toLowerCase();
        return lowerUrl.contains("bilibili.com") || lowerUrl.contains("b23.tv");
    }
    
    /**
     * 解析B站视频
     */
    private VideoDownloadResponseVo parseBilibiliVideo(VideoDownloadRequestDto request) {
        // 获取视频源信息
        String cookie = RedisUtil.getCacheMapValue(VIDEO_LOGIN_COOKIE, VideoPlatformEnum.BILIBILI.getCode());
        VideoDownloadResponseVo responseVo = BiliUtil.findVideoStreaming(request.getUrl(), cookie);

        log.info("找到{}个视频流", responseVo.getVideoFiles().size());
        List<VideoDownloadResponseVo.VideoFileInfo> audioFileInfos = new ArrayList<>();
        for (VideoDownloadResponseVo.VideoFileInfo videoFileInfo : responseVo.getVideoFiles()) {
            String cid = videoFileInfo.getCid();
            String title = responseVo.getTitle();
            String pic = responseVo.getThumbnail();

            if (cid == null || cid.isEmpty() || title == null || title.isEmpty()) {
                log.warn("视频信息不完整: cid={}, title={}", cid, title);
                continue;
            }
            // 生成文件名和路径
            String filename = StringUtil.getFileName(title, cid);
            String dir = FileUtil.generateVideoDownloadDir(VideoPlatformEnum.BILIBILI.getCode(), false, filename);
            // 下载封面
            try {
                BiliUtil.downBiliFromUrl(pic, filename + ".jpg", dir, cookie);
                log.debug("封面下载完成: {}", filename);
            } catch (Exception e) {
                log.warn("封面下载失败: {}, 原因: {}", filename, e.getMessage());
            }
            // 如果需要mp3,从视频中分离
            if (request.getAudio()) {
                File videoFile = new File(videoFileInfo.getPath());
                MediaSeparationResult mediaSeparationResult = audioConversionService.separateAudioFromVideo(videoFile);
                if (mediaSeparationResult.isSuccess()){
                    log.debug("从{}分离音频成功", videoFileInfo.getName());
                    // 将音频保存至视频同目录
                    File audioFile = new File(dir, filename + "." + mediaSeparationResult.getAudioInfo().getFormat().toLowerCase());
                    try {
                        FileUtil.moveFile(mediaSeparationResult.getAudioFile(), audioFile);
                        VideoDownloadResponseVo.VideoFileInfo _audioFileInfo = BeanUtil.copyProperties(videoFile, VideoDownloadResponseVo.VideoFileInfo.class);
                        _audioFileInfo.setName(audioFile.getName());
                        _audioFileInfo.setPath(audioFile.getAbsolutePath() + File.separator + audioFile.getName());
                        _audioFileInfo.setFormat(mediaSeparationResult.getAudioInfo().getFormat());
                        _audioFileInfo.setAudioCodec(mediaSeparationResult.getAudioInfo().getChannels());
                        audioFileInfos.add(_audioFileInfo);
                        log.debug("音频保存成功，音频地址 {}", audioFile.getAbsolutePath());
                    }catch (Exception e){
                        log.error("音频保存失败: ", e);
                    }
                }
            }
        }
        responseVo.setAudioFiles(audioFileInfos);
        log.info("哔哩哔哩视频解析下载流程结束");
        return responseVo;
    }
}
