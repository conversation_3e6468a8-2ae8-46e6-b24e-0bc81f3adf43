package org.yixz.service.impl;

import cn.hutool.core.collection.CollUtil;
import com.baidu.aip.ocr.AipOcr;
import com.baidu.aip.speech.AipSpeech;
import lombok.extern.slf4j.Slf4j;
import okhttp3.OkHttpClient;
import okhttp3.Request;
import okhttp3.Response;
import okhttp3.WebSocket;
import okhttp3.WebSocketListener;
import okio.ByteString;
import org.jetbrains.annotations.NotNull;
import org.jetbrains.annotations.Nullable;
import org.json.JSONArray;
import org.json.JSONException;
import org.json.JSONObject;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.util.StringUtils;
import org.yixz.common.properties.AiServiceProperties;
import org.yixz.common.converter.AudioFormatDetector;
import org.yixz.common.enums.AiProviderEnum;
import org.yixz.common.exception.BizException;
import org.yixz.entity.dto.AsrRequestDto;
import org.yixz.entity.dto.OcrRequestDto;
import org.yixz.entity.vo.AsrResponseVo;
import org.yixz.entity.vo.OcrResponseVo;
import org.yixz.service.AudioConversionService;
import org.yixz.service.ProviderService;

import java.io.IOException;
import java.io.InputStream;
import java.util.HashMap;
import java.util.UUID;
import java.util.concurrent.CountDownLatch;
import java.util.concurrent.TimeUnit;
import java.util.concurrent.atomic.AtomicReference;

/**
 * 百度云语音识别服务实现
 *
 * <AUTHOR>
 * @date 2025-07-20
 */
@Slf4j
@Service("baidu")
public class BaiduServiceImpl implements ProviderService {

    private static volatile AipSpeech aipSpeechClient;
    private static volatile AipOcr aipOcrClient;

    @Autowired
    private AiServiceProperties aiServiceProperties;

    @Autowired
    private AudioConversionService audioConversionService;

    @Override
    public AsrResponseVo speechToText(AsrRequestDto request) {
        long startTime = System.currentTimeMillis();
        AudioConversionService.ConversionResult conversionResult = null;

        try {
            // 验证配置
            validateConfig();

            // 音频格式转换（如果需要）
            conversionResult = audioConversionService.convertForBaiduASR(request.getAudioFile());

            if (!conversionResult.isSuccess()) {
                throw new RuntimeException("音频格式转换失败: " + conversionResult.getMessage());
            }

            log.info("音频转换结果: {}", conversionResult);

            // 短语音识别
            // AsrResponseVo response = callBaiduRecognizerAsrApiWithStream(conversionResult.getAudioStream(), conversionResult.getTargetInfo());

            // 实时语音识别
            AsrResponseVo response = callBaiduRealtimeAsrApiWithStream(conversionResult.getAudioStream(), conversionResult.getTargetInfo());

            response.setProvider(getProviderName());
            response.setProcessTime(System.currentTimeMillis() - startTime);

            // 添加转换信息到响应中
            if (conversionResult.getSourceInfo() != null) {
                response.setOriginalFormat(conversionResult.getSourceInfo().getFormat());
                response.setConvertedFormat(conversionResult.getTargetInfo() != null ?
                    conversionResult.getTargetInfo().getFormat() : "未知");
                response.setConverterUsed(conversionResult.getConverterUsed());
                response.setConversionTime(conversionResult.getConversionTimeMs());
            }

            log.info("百度云ASR识别完成，耗时: {}ms", response.getProcessTime());
            return response;

        } catch (Exception e) {
            log.error("百度云ASR识别失败", e);
            throw new RuntimeException("语音识别失败: " + e.getMessage(), e);
        } finally {
            // 清理临时文件
            if (conversionResult != null) {
                conversionResult.cleanup();
            }
        }
    }

    @Override
    public OcrResponseVo imageToText(OcrRequestDto request) {
        long startTime = System.currentTimeMillis();

        try {
            // 验证配置
            validateConfig();
            // 构建请求
            OcrResponseVo response = callBaiduOcrApi(request);
            response.setProvider(getProviderName());
            response.setProcessTime(System.currentTimeMillis() - startTime);

            log.info("百度云OCR识别完成，耗时: {}ms", response.getProcessTime());
            return response;

        } catch (Exception e) {
            log.error("百度云OCR识别失败", e);
            throw new RuntimeException("图片文字识别失败: " + e.getMessage(), e);
        }
    }

    /**
     * 短语音识别(60s内)
     */
    private OcrResponseVo callBaiduRecognizerAsrApi(AsrRequestDto request) throws IOException {
        AiServiceProperties.BaiduConfig config = aiServiceProperties.getBaidu();

        // 初始化一个AipSpeech
        AipSpeech client = getAipSpeechInstance(config);

        // 可选：设置网络连接参数
        client.setConnectionTimeoutInMillis(20000);
        client.setSocketTimeoutInMillis(60000);

        // 可选：设置代理服务器地址, http和socket二选一，或者均不设置
        // client.setHttpProxy("proxy_host", proxy_port);  // 设置http代理
        // client.setSocketProxy("proxy_host", proxy_port);  // 设置socket代理

        // 可选：设置log4j日志输出格式，若不设置，则使用默认配置
        // 也可以直接通过jvm启动参数设置此环境变量
        // System.setProperty("aip.log4j.conf", "path/to/your/log4j.properties");

        // 调用接口
        // 对本地语音文件进行识别
        /*String path = "D:\\code\\java-sdk\\speech_sdk\\src\\test\\resources\\16k_test.pcm";
        JSONObject asrRes = client.asr(path, "pcm", 16000, null);
        System.out.println(asrRes);

        // 对语音二进制数据进行识别
        byte[] data = Util.readFileByBytes(path);     //readFileByBytes仅为获取二进制数据示例
        JSONObject asrRes2 = client.asr(data, "pcm", 16000, null);
        System.out.println(asrRes2);*/

        /**
         * format: 语音文件的格式，pcm 或者 wav 或者 amr。不区分大小写。推荐pcm文件
         * rate: 采样率，16000、8000，固定值
         */
        org.json.JSONObject res = client.asr(request.getAudioFile().getBytes(), "pcm", 16000, null);
        log.info("百度短语音ASR识别返回结果：{}", res);
        return parseOcrResponse(res);
    }

    /**
     * 短语音识别(60s内) - 使用InputStream
     */
    private AsrResponseVo callBaiduRecognizerAsrApiWithStream(InputStream audioStream, AudioFormatDetector.AudioInfo audioInfo) throws IOException {
        AiServiceProperties.BaiduConfig config = aiServiceProperties.getBaidu();

        // 初始化一个AipSpeech
        AipSpeech client = getAipSpeechInstance(config);

        // 可选：设置网络连接参数
        client.setConnectionTimeoutInMillis(20000);
        client.setSocketTimeoutInMillis(60000);

        // 读取音频流数据
        byte[] audioData = audioStream.readAllBytes();

        // 确定音频格式和采样率
        String format = determineAudioFormat(audioInfo);
        int sampleRate = determineSampleRate(audioInfo);

        log.info("百度ASR识别参数: 格式={}, 采样率={}Hz, 数据大小={}字节", format, sampleRate, audioData.length);

        /**
         * format: 语音文件的格式，pcm 或者 wav 或者 amr 或者 m4a。不区分大小写。
         * rate: 采样率，16000、8000，固定值
         */
        org.json.JSONObject res = client.asr(audioData, format, sampleRate, null);
        log.info("百度短语音ASR识别返回结果：{}", res);
        AsrResponseVo response = parseRecognizerAsrResponse(res);
        return response;
    }

    /**
     * 实时语音识别（原始方法，保持不变）
     */
    private AsrResponseVo callBaiduRealtimeAsrApi(AsrRequestDto request) {
        long startTime = System.currentTimeMillis();
        AiServiceProperties.BaiduConfig config = aiServiceProperties.getBaidu();

        // 创建WebSocket客户端，增加超时时间
        OkHttpClient client = new OkHttpClient.Builder()
                .connectTimeout(10, TimeUnit.SECONDS)
                .readTimeout(30, TimeUnit.SECONDS)
                .writeTimeout(30, TimeUnit.SECONDS)
                .build();

        String url = config.getAsr().getRealtimeAsr() + "?sn=" + UUID.randomUUID();
        log.info("百度ASR实时语音识别开始: {}", url);

        Request httpRequest = new Request.Builder().url(url).build();
        WListener listener = new WListener(request);
        client.newWebSocket(httpRequest, listener);

        try {
            // 等待识别完成，最多等待30秒
            boolean completed = listener.getLatch().await(30, TimeUnit.SECONDS);

            if (!completed) {
                throw new RuntimeException("识别超时，请检查网络连接或音频文件大小");
            }

            // 检查是否有错误
            String errorMsg = listener.getErrorMessage();
            if (errorMsg != null) {
                throw new RuntimeException(errorMsg);
            }

            // 构建响应对象
            AsrResponseVo response = new AsrResponseVo();
            response.setText(listener.getResult());
            response.setDuration(0.0); // 实时识别暂不计算时长
            response.setProcessTime(System.currentTimeMillis() - startTime);
            response.setProvider(getProviderName());

            log.info("百度ASR实时语音识别完成，结果长度: {}, 耗时: {}ms",
                    response.getText().length(), response.getProcessTime());

            return response;

        } catch (InterruptedException e) {
            Thread.currentThread().interrupt();
            throw new RuntimeException("识别过程被中断", e);
        } finally {
            // 关闭客户端
            client.dispatcher().executorService().shutdown();
        }
    }

    /**
     * 实时语音识别（支持音频格式转换）
     */
    private AsrResponseVo callBaiduRealtimeAsrApiWithStream(InputStream audioStream, AudioFormatDetector.AudioInfo audioInfo) {
        AiServiceProperties.BaiduConfig config = aiServiceProperties.getBaidu();

        // 创建WebSocket客户端，增加超时时间
        OkHttpClient client = new OkHttpClient.Builder()
                .connectTimeout(10, TimeUnit.SECONDS)
                .readTimeout(30, TimeUnit.SECONDS)
                .writeTimeout(30, TimeUnit.SECONDS)
                .build();

        String url = config.getAsr().getRealtimeAsr() + "?sn=" + UUID.randomUUID();
        log.info("百度ASR实时语音识别开始: {}", url);

        Request httpRequest = new Request.Builder().url(url).build();
        WListenerWithConversion listener = new WListenerWithConversion(audioStream, audioInfo);
        client.newWebSocket(httpRequest, listener);

        try {
            // 等待识别完成，最多等待30秒
            boolean completed = listener.getLatch().await(300, TimeUnit.SECONDS);

            if (!completed) {
                throw new RuntimeException("识别超时，请检查网络连接或音频文件大小");
            }

            // 检查是否有错误
            String errorMsg = listener.getErrorMessage();
            if (errorMsg != null) {
                throw new RuntimeException(errorMsg);
            }

            // 构建响应对象
            AsrResponseVo response = new AsrResponseVo();
            response.setText(listener.getResult());

            log.info("百度ASR实时语音识别完成，结果长度: {}, 耗时: {}ms",
                    response.getText().length(), response.getProcessTime());

            return response;

        } catch (InterruptedException e) {
            Thread.currentThread().interrupt();
            throw new RuntimeException("识别过程被中断", e);
        } finally {
            // 关闭客户端
            client.dispatcher().executorService().shutdown();
        }
    }

    private AsrResponseVo parseRecognizerAsrResponse(org.json.JSONObject res) {
        AsrResponseVo response = new AsrResponseVo();

        // 检查错误
        if (res.has("err_no") && res.getInt("err_no") != 0) {
            throw new RuntimeException("百度云ASR识别失败: " + res.getString("err_msg"));
        }

        // 解析结果
        if (res.has("result")) {
            // 识别结果数组，提供1-5 个候选结果，string 类型为识别的字符串， utf-8 编码
            JSONArray results = res.getJSONArray("result");
            // 默认取第一个
            response.setText(results.get(0).toString());
        }

        response.setDuration(0.0);

        return response;
    }

    private OcrResponseVo callBaiduOcrApi(OcrRequestDto request) throws IOException {
        AiServiceProperties.BaiduConfig config = aiServiceProperties.getBaidu();

        // 初始化一个AipOcr
        AipOcr client = getAipOcrInstance(config);

        // 可选：设置网络连接参数
        client.setConnectionTimeoutInMillis(2000);
        client.setSocketTimeoutInMillis(60000);

        // 可选：设置代理服务器地址, http和socket二选一，或者均不设置
        // client.setHttpProxy("proxy_host", proxy_port);  // 设置http代理
        // client.setSocketProxy("proxy_host", proxy_port);  // 设置socket代理

        // 可选：设置log4j日志输出格式，若不设置，则使用默认配置
        // 也可以直接通过jvm启动参数设置此环境变量
        // System.setProperty("aip.log4j.conf", "path/to/your/log4j.properties");

        // 调用接口
        // 传入可选参数调用接口
        HashMap<String, String> options = new HashMap<String, String>();
        // options.put("language_type", "CHN_ENG");
        // options.put("detect_direction", "true");
        // options.put("detect_language", "true");
        // options.put("probability", "true");
        // 参数为本地图片二进制数组
        org.json.JSONObject res = client.basicGeneral(request.getImageFile().getBytes(), options);
        log.info("百度OCR识别返回结果：{}", res);
        return parseOcrResponse(res);
    }

    private OcrResponseVo parseOcrResponse(org.json.JSONObject res) {
        OcrResponseVo response = new OcrResponseVo();
        // 检查错误
        if (res.has("error_code")) {
            throw new BizException("百度云OCR识别失败: " + res.getString("error_msg"));
        }
        response.setLogId(String.valueOf(res.getLong("log_id")));
        org.json.JSONArray wordsResult = res.getJSONArray("words_result");

        StringBuilder result = new StringBuilder();
        if (CollUtil.isNotEmpty(wordsResult)) {
            for (int i = 0; i < wordsResult.length(); i++) {
                org.json.JSONObject jsonObject = wordsResult.getJSONObject(i);
                result.append(jsonObject.getString("words"));
            }
        }
        response.setText(result.toString());
        return response;
    }

    @Override
    public String getProviderName() {
        return AiProviderEnum.BAIDU.getCode();
    }

    private void validateConfig() {
        AiServiceProperties.BaiduConfig config = aiServiceProperties.getBaidu();
        if (!StringUtils.hasText(config.getApiKey()) ||
                !StringUtils.hasText(config.getSecretKey()) ||
                !StringUtils.hasText(config.getAppId())
        ) {
            throw new IllegalStateException("百度云ASR配置不完整，请检查appId、apiKey和secretKey");
        }
    }

    @Override
    public boolean isAvailable() {
        try {
            validateConfig();
            return true;
        } catch (Exception e) {
            log.warn("百度云ASR服务不可用: {}", e.getMessage());
            return false;
        }
    }

    /**
     * 根据音频信息确定百度ASR支持的格式
     */
    private String determineAudioFormat(AudioFormatDetector.AudioInfo audioInfo) {
        if (audioInfo == null) {
            return "wav"; // 默认格式
        }

        String basicFormat = AudioFormatDetector.extractBasicFormat(audioInfo.getFormat(), AiProviderEnum.BAIDU);

        // 百度ASR支持的格式映射
        return switch (basicFormat.toUpperCase()) {
            case "PCM" -> "pcm";
            case "WAV" -> "wav";
            case "AMR" -> "amr";
            case "M4A" -> "m4a";
            default -> {
                log.warn("未知音频格式: {}, 使用默认格式wav", basicFormat);
                yield "wav";
            }
        };
    }

    /**
     * 根据音频信息确定采样率
     */
    private int determineSampleRate(AudioFormatDetector.AudioInfo audioInfo) {
        if (audioInfo == null) {
            return 16000; // 默认采样率
        }

        int sampleRate = audioInfo.getSampleRate();

        // 百度ASR只支持8000和16000
        if (sampleRate == 8000 || sampleRate == 16000) {
            return sampleRate;
        } else {
            log.warn("不支持的采样率: {}Hz, 使用默认采样率16000Hz", sampleRate);
            return 16000;
        }
    }

    public static AipSpeech getAipSpeechInstance(AiServiceProperties.BaiduConfig config) {
        if (aipSpeechClient == null) {
            synchronized (AipSpeech.class) {
                if (aipSpeechClient == null) {
                    aipSpeechClient = new AipSpeech(config.getAppId(), config.getApiKey(), config.getSecretKey());
                }
            }
        }
        return aipSpeechClient;
    }

    public static AipOcr getAipOcrInstance(AiServiceProperties.BaiduConfig config) {
        if (aipOcrClient == null) {
            synchronized (AipSpeech.class) {
                if (aipOcrClient == null) {
                    aipOcrClient = new AipOcr(config.getAppId(), config.getApiKey(), config.getSecretKey());
                }
            }
        }
        return aipOcrClient;
    }

    /**
     * WebSocket 事件回调
     */
    private class WListener extends WebSocketListener {

        private final StringBuilder result = new StringBuilder();
        private final CountDownLatch latch = new CountDownLatch(1);
        private final AtomicReference<String> errorMessage = new AtomicReference<>();
        private final AsrRequestDto request;

        public WListener(AsrRequestDto request) {
            this.request = request;
        }

        /**
         * STEP 2. 连接成功后发送数据
         *
         * @param webSocket WebSocket类
         * @param response  结果
         */
        @Override
        public void onOpen(@NotNull WebSocket webSocket, @NotNull Response response) {
            super.onOpen(webSocket, response);
            // 这里一定不要阻塞
            new Thread(() -> {
                try {
                    // STEP 2.1 发送发送开始参数帧
                    sendStartFrame(webSocket);
                    // STEP 2.2 实时发送音频数据帧
                    sendAudioFrames(webSocket);
                    // STEP 2.4 发送结束帧
                    sendFinishFrame(webSocket);
                } catch (Exception e) {
                    log.error("百度ASR实时语音识别处理失败", e);
                    errorMessage.set("音频处理失败: " + e.getMessage());
                    latch.countDown();
                }
            }).start();
            // 这里千万别阻塞，包括WebSocketListener其它回调
        }

        /**
         * STEP 2.1 发送发送开始参数帧
         *
         * @param webSocket WebSocket类
         * @throws JSONException Json解析异常
         */
        private void sendStartFrame(WebSocket webSocket) throws JSONException {
            AiServiceProperties.BaiduConfig config = aiServiceProperties.getBaidu();

            JSONObject params = new JSONObject();
            params.put("appid", config.getAppId());
            params.put("appkey", config.getApiKey());
            params.put("dev_pid", 1537); // 普通话(支持简单的英文识别)
            params.put("cuid", "baidu_asr_client_" + System.currentTimeMillis());
            params.put("format", "pcm");
            params.put("sample", 16000);

            JSONObject json = new JSONObject();
            json.put("type", "START");
            json.put("data", params);
            log.info("发送开始帧: {}", json);
            webSocket.send(json.toString());
        }

        /**
         * STEP 2.2 实时发送音频数据帧
         *
         * @param webSocket WebSocket类
         */
        private void sendAudioFrames(WebSocket webSocket) {
            log.info("开始发送音频数据帧");
            try {
                // 获取音频文件的字节数据
                byte[] audioData = request.getAudioFile().getBytes();
                int bytesPerFrame = Util.BYTES_PER_FRAME; // 一个帧 160ms的音频数据
                int totalBytes = audioData.length;
                int offset = 0;

                while (offset < totalBytes) {
                    int frameSize = Math.min(bytesPerFrame, totalBytes - offset);
                    ByteString bytesToSend = ByteString.of(audioData, offset, frameSize);

                    // 数据帧之间需要有间隔时间，间隔时间为上一帧的音频长度
                    long waitTime = Util.bytesToTime(frameSize);
                    if (waitTime > 0) {
                        Util.sleep(waitTime);
                    }

                    log.debug("发送音频数据帧: 大小={}字节, 等待时间={}ms", frameSize, waitTime);
                    webSocket.send(bytesToSend);

                    offset += frameSize;
                }
                log.info("音频数据发送完成，总大小: {}字节", totalBytes);
            } catch (IOException e) {
                log.error("读取音频文件失败", e);
                errorMessage.set("读取音频文件失败: " + e.getMessage());
                latch.countDown();
            }
        }

        /**
         * STEP 2.4 发送结束帧
         *
         * @param webSocket WebSocket 类
         * @throws JSONException Json解析错误
         */
        private void sendFinishFrame(WebSocket webSocket) throws JSONException {
            JSONObject json = new JSONObject();
            json.put("type", "FINISH");
            log.info("发送结束帧: {}", json);
            webSocket.send(json.toString());
        }

        /**
         * STEP 2.3 库接收识别结果
         *
         * @param webSocket WebSocket 类
         * @param text      返回的json
         */
        @Override
        public void onMessage(@NotNull WebSocket webSocket, @NotNull String text) {
            super.onMessage(webSocket, text);
            try {
                // 解析识别结果
                JSONObject jsonResponse = new JSONObject(text);
                String type = jsonResponse.getString("type");
                if ("HEARTBEAT".equals(type)) {
                    log.debug("收到心跳: {}", text.trim());
                }else if ("MID_TEXT".equals(type)) {
                    log.info("收到临时识别结果识别结果: {}", text.trim());
                }else if ("FIN_TEXT".equals(type)) {
                    log.info("一句话识别结果：{}", text.trim());
                    int errNo = jsonResponse.getInt("err_no");
                    if (errNo != 0) {
                        String errorMsg = jsonResponse.optString("err_msg", "未知错误");
                        log.error("识别出错: {}", errorMsg);
                        // 如果是 -3003、-3005、-3013只打印，不终止
                        if (-3003 == errNo || -3005 == errNo || -3013 == errNo) {
                            return;
                        }
                        errorMessage.set("识别失败: " + errorMsg);
                        latch.countDown();
                    }
                    if (jsonResponse.has("result")) {
                        String recognizedText = jsonResponse.getString("result");
                        if (StringUtils.hasText(recognizedText)) {
                            synchronized (result) {
                                result.append(recognizedText);
                            }
                            log.info("识别到文本: {}", recognizedText);
                        }
                    }
                }
            } catch (JSONException e) {
                log.error("解析识别结果失败: {}", text, e);
            }
        }

        /**
         * STEP 3. 关闭连接
         * 服务端关闭连接事件
         *
         * @param webSocket WebSocket 类
         * @param code      状态码
         * @param reason    状态描述
         */
        @Override
        public void onClosing(@NotNull WebSocket webSocket, int code, @NotNull String reason) {
            super.onClosing(webSocket, code, reason);
            // 收到服务端关闭
            // 需要停止发任何数据，为了简单，这个demo里遇见报错后没有这段逻辑，具体运行full.Main查看
            log.info("websocket event closing :" + code + " | " + reason);
            // 客户端关闭
            webSocket.close(1000, "");
        }

        /**
         * 客户端关闭回调
         *
         * @param webSocket WebSocket 类
         * @param code      状态码
         * @param reason    状态描述
         */
        @Override
        public void onClosed(@NotNull WebSocket webSocket, int code, @NotNull String reason) {
            super.onClosed(webSocket, code, reason);
            log.info("WebSocket连接已关闭: code={}, reason={}", code, reason);
            // 确保latch被释放
            latch.countDown();
        }


        /**
         * 库自身的报错，如断网
         *
         * @param webSocket WebSocket 类
         * @param t         异常
         * @param response  返回
         */
        @Override
        public void onFailure(@NotNull WebSocket webSocket, @NotNull Throwable t, @Nullable Response response) {
            super.onFailure(webSocket, t, response);
            // 这里千万别阻塞，包括WebSocketListener其它回调
            log.error("百度ASR实时语音识别WebSocket异常", t);
            errorMessage.set("连接失败: " + t.getMessage());
            // 确保latch被释放
            latch.countDown();
        }

        public String getResult() {
            return result.toString();
        }

        public String getErrorMessage() {
            return errorMessage.get();
        }

        public CountDownLatch getLatch() {
            return latch;
        }
    }

    /**
     * WebSocket 事件回调（支持音频转换）
     */
    private class WListenerWithConversion extends WebSocketListener {

        private final StringBuilder result = new StringBuilder();
        private final CountDownLatch latch = new CountDownLatch(1);
        private final AtomicReference<String> errorMessage = new AtomicReference<>();
        private final InputStream audioStream;
        private final AudioFormatDetector.AudioInfo audioInfo;

        public WListenerWithConversion(InputStream audioStream, AudioFormatDetector.AudioInfo audioInfo) {
            this.audioStream = audioStream;
            this.audioInfo = audioInfo;
        }

        /**
         * STEP 2. 连接成功后发送数据
         */
        @Override
        public void onOpen(@NotNull WebSocket webSocket, @NotNull Response response) {
            super.onOpen(webSocket, response);
            // 这里一定不要阻塞
            new Thread(() -> {
                try {
                    // STEP 2.1 发送发送开始参数帧
                    sendStartFrameWithConversion(webSocket);
                    // STEP 2.2 实时发送音频数据帧
                    sendAudioFramesWithConversion(webSocket);
                    // STEP 2.4 发送结束帧
                    sendFinishFrame(webSocket);
                } catch (Exception e) {
                    log.error("百度ASR实时语音识别处理失败", e);
                    errorMessage.set("音频处理失败: " + e.getMessage());
                    latch.countDown();
                }
            }).start();
        }

        /**
         * 发送开始参数帧（使用转换后的音频信息）
         */
        private void sendStartFrameWithConversion(WebSocket webSocket) throws JSONException {
            AiServiceProperties.BaiduConfig config = aiServiceProperties.getBaidu();

            JSONObject params = new JSONObject();
            params.put("appid", Integer.parseInt(config.getAppId()));
            params.put("appkey", config.getApiKey());
            params.put("dev_pid", 15372); // 普通话(支持简单的英文识别)
            params.put("cuid", "baidu_asr_client_" + System.currentTimeMillis());

            // 使用转换后的音频格式信息
            // String format = determineAudioFormat(audioInfo);
            // int sampleRate = determineSampleRate(audioInfo);

            params.put("format", "pcm");
            params.put("sample", 16000);

            JSONObject json = new JSONObject();
            json.put("type", "START");
            json.put("data", params);
            log.info("发送开始帧（转换后）: {}", json);
            webSocket.send(json.toString());
        }

        /**
         * 发送音频数据帧（使用转换后的音频流）
         */
        private void sendAudioFramesWithConversion(WebSocket webSocket) {
            log.info("开始发送转换后的音频数据帧");
            try {
                // 获取转换后的音频流数据
                byte[] audioData = audioStream.readAllBytes();
                int bytesPerFrame = Util.BYTES_PER_FRAME; // 一个帧 160ms的音频数据
                int totalBytes = audioData.length;
                int offset = 0;

                while (offset < totalBytes) {
                    int frameSize = Math.min(bytesPerFrame, totalBytes - offset);
                    ByteString bytesToSend = ByteString.of(audioData, offset, frameSize);

                    // 数据帧之间需要有间隔时间，间隔时间为上一帧的音频长度
                    long waitTime = Util.bytesToTime(frameSize);
                    if (waitTime > 0) {
                        Util.sleep(waitTime);
                    }

                    log.debug("发送转换后音频数据帧: 大小={}字节, 等待时间={}ms", frameSize, waitTime);
                    webSocket.send(bytesToSend);

                    offset += frameSize;
                }
                log.info("转换后音频数据发送完成，总大小: {}字节", totalBytes);
            } catch (IOException e) {
                log.error("读取转换后音频流失败", e);
                errorMessage.set("读取转换后音频流失败: " + e.getMessage());
                latch.countDown();
            }
        }

        /**
         * 发送结束帧
         */
        private void sendFinishFrame(WebSocket webSocket) throws JSONException {
            JSONObject json = new JSONObject();
            json.put("type", "FINISH");
            log.info("发送结束帧: {}", json);
            webSocket.send(json.toString());
        }

        /**
         * 接收识别结果
         */
        @Override
        public void onMessage(@NotNull WebSocket webSocket, @NotNull String text) {
            super.onMessage(webSocket, text);
            try {
                // 解析识别结果
                JSONObject jsonResponse = new JSONObject(text);
                String type = jsonResponse.getString("type");
                if ("HEARTBEAT".equals(type)) {
                    log.debug("收到心跳: {}", text.trim());
                }else if ("MID_TEXT".equals(type)) {
                    log.info("收到临时识别结果识别结果: {}", text.trim());
                }else if ("FIN_TEXT".equals(type)) {
                    log.info("一句话识别结果：{}", text.trim());
                    int errNo = jsonResponse.getInt("err_no");
                    if (errNo != 0) {
                        String errorMsg = jsonResponse.optString("err_msg", "未知错误");
                        log.error("识别出错: {}", errorMsg);
                        // 如果是 -3003、-3005、-3013只打印，不终止
                        if (-3003 == errNo || -3005 == errNo || -3013 == errNo) {
                            return;
                        }
                        errorMessage.set("识别失败: " + errorMsg);
                        latch.countDown();
                    }
                    if (jsonResponse.has("result")) {
                        String recognizedText = jsonResponse.getString("result");
                        if (StringUtils.hasText(recognizedText)) {
                            synchronized (result) {
                                result.append(recognizedText);
                            }
                            log.info("识别到文本: {}", recognizedText);
                        }
                    }
                }
            } catch (JSONException e) {
                log.error("解析识别结果失败: {}", text, e);
            }
        }

        @Override
        public void onClosing(@NotNull WebSocket webSocket, int code, @NotNull String reason) {
            super.onClosing(webSocket, code, reason);
            log.info("WebSocket连接关闭中: code={}, reason={}", code, reason);
            webSocket.close(1000, "");
        }

        @Override
        public void onClosed(@NotNull WebSocket webSocket, int code, @NotNull String reason) {
            super.onClosed(webSocket, code, reason);
            log.info("WebSocket连接已关闭: code={}, reason={}", code, reason);
            latch.countDown();
        }

        @Override
        public void onFailure(@NotNull WebSocket webSocket, @NotNull Throwable t, @Nullable Response response) {
            super.onFailure(webSocket, t, response);
            log.error("百度ASR实时语音识别WebSocket异常", t);
            errorMessage.set("连接失败: " + t.getMessage());
            latch.countDown();
        }

        public String getResult() {
            return result.toString();
        }

        public String getErrorMessage() {
            return errorMessage.get();
        }

        public CountDownLatch getLatch() {
            return latch;
        }
    }

    public class Util {

        public static final int BYTES_PER_MS = 16000 * 2 / 1000; // 16000的采样率，16bits=2bytes， 1000ms
        public static final int FRAME_MS = 160; // websocket一个数据帧 160ms
        public static final int BYTES_PER_FRAME = BYTES_PER_MS * FRAME_MS; // 一个数据帧的大小=5120bytes

        /**
         * 毫秒转为字节数
         *
         * @param durationMs 毫秒
         * @return 字节数
         */
        public static long timeToBytes(long durationMs) {
            return durationMs * BYTES_PER_MS;
        }

        /**
         * 字节数转为毫秒
         *
         * @param size 字节数
         * @return 毫秒
         */
        public static int bytesToTime(int size) {
            return size / BYTES_PER_MS;
        }

        /**
         * sleep， 转为RuntimeException
         *
         * @param millis 毫秒
         */
        public static void sleep(long millis) {
            if (millis <= 0) {
                return;
            }
            try {
                Thread.sleep(millis);
            } catch (InterruptedException e) {
                e.printStackTrace();
                throw new RuntimeException(e);
            }
        }
    }
}
