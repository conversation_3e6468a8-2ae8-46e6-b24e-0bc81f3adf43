package org.yixz.service.impl;

import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.yixz.common.properties.VideoDownloadProperties;
import org.yixz.entity.dto.VideoDownloadRequestDto;
import org.yixz.entity.vo.VideoDownloadResponseVo;
import org.yixz.service.VideoDownloadService;

import java.io.BufferedReader;
import java.io.InputStreamReader;
import java.util.ArrayList;
import java.util.List;
import java.util.concurrent.TimeUnit;

/**
 * yt-dlp通用视频下载服务实现
 *
 * <AUTHOR>
 * @date 2025-07-26
 */
@Slf4j
@Service("yt-dlp")
public class YtDlpDownloadServiceImpl implements VideoDownloadService {
    
    @Autowired
    private VideoDownloadProperties videoDownloadProperties;
    
    @Override
    public VideoDownloadResponseVo downloadVideo(VideoDownloadRequestDto request) {
        long startTime = System.currentTimeMillis();
        
        try {
            log.info("开始使用yt-dlp下载视频: {}", request.getUrl());
            
            // 检查yt-dlp是否可用
            if (!isAvailable()) {
                throw new RuntimeException("yt-dlp不可用，请确保已正确安装");
            }
            
            // 构建响应
            VideoDownloadResponseVo response = new VideoDownloadResponseVo();
            response.setPlatform("unknown"); // yt-dlp可以处理多种平台
            response.setDownloader(getDownloaderName());
            response.setProcessTime(System.currentTimeMillis() - startTime);
            
            // 执行yt-dlp下载
            response = executeYtDlpDownload(request, response);
            
            log.info("yt-dlp视频下载完成，耗时: {}ms", response.getProcessTime());
            return response;
            
        } catch (Exception e) {
            log.error("yt-dlp视频下载失败: {}", e.getMessage(), e);
            
            VideoDownloadResponseVo errorResponse = new VideoDownloadResponseVo();
            errorResponse.setPlatform("unknown");
            errorResponse.setDownloader(getDownloaderName());
            errorResponse.setErrorMessage("yt-dlp视频下载失败: " + e.getMessage());
            errorResponse.setProcessTime(System.currentTimeMillis() - startTime);
            
            return errorResponse;
        }
    }
    
    @Override
    public VideoDownloadResponseVo getVideoInfo(VideoDownloadRequestDto request) {
        try {
            log.info("使用yt-dlp获取视频信息: {}", request.getUrl());
            
            // 检查yt-dlp是否可用
            if (!isAvailable()) {
                throw new RuntimeException("yt-dlp不可用，请确保已正确安装");
            }
            
            // 执行yt-dlp信息获取
            return executeYtDlpInfo(request);
            
        } catch (Exception e) {
            log.error("yt-dlp获取视频信息失败: {}", e.getMessage(), e);
            throw new RuntimeException("yt-dlp获取视频信息失败: " + e.getMessage(), e);
        }
    }
    
    @Override
    public String getDownloaderName() {
        return "yt-dlp通用下载器";
    }
    
    @Override
    public boolean isAvailable() {
        try {
            // 检查yt-dlp是否安装
            ProcessBuilder pb = new ProcessBuilder(
                videoDownloadProperties.getYtDlp().getExecutablePath(), "--version"
            );
            Process process = pb.start();
            boolean finished = process.waitFor(5, TimeUnit.SECONDS);
            
            if (!finished) {
                process.destroyForcibly();
                return false;
            }
            
            return process.exitValue() == 0;
            
        } catch (Exception e) {
            log.debug("yt-dlp可用性检查失败: {}", e.getMessage());
            return false;
        }
    }
    
    @Override
    public boolean supports(String url) {
        // yt-dlp支持大多数视频网站
        return url != null && !url.trim().isEmpty();
    }
    
    /**
     * 执行yt-dlp下载
     */
    private VideoDownloadResponseVo executeYtDlpDownload(VideoDownloadRequestDto request, VideoDownloadResponseVo response) {
        try {
            // 构建yt-dlp命令
            List<String> command = buildYtDlpCommand(request, false);
            
            // 执行命令
            ProcessBuilder pb = new ProcessBuilder(command);
            Process process = pb.start();
            
            // 读取输出
            StringBuilder output = new StringBuilder();
            try (BufferedReader reader = new BufferedReader(new InputStreamReader(process.getInputStream()))) {
                String line;
                while ((line = reader.readLine()) != null) {
                    output.append(line).append("\n");
                    log.debug("yt-dlp输出: {}", line);
                }
            }
            
            // 等待完成
            boolean finished = process.waitFor(videoDownloadProperties.getDownloadTimeoutMs(), TimeUnit.MILLISECONDS);
            
            if (!finished) {
                process.destroyForcibly();
                throw new RuntimeException("yt-dlp下载超时");
            }
            
            if (process.exitValue() != 0) {
                throw new RuntimeException("yt-dlp下载失败，退出码: " + process.exitValue());
            }
            
            // 解析输出并填充响应
            parseYtDlpOutput(output.toString(), response);
            
            return response;
            
        } catch (Exception e) {
            log.error("执行yt-dlp下载失败", e);
            throw new RuntimeException("执行yt-dlp下载失败: " + e.getMessage(), e);
        }
    }
    
    /**
     * 执行yt-dlp信息获取
     */
    private VideoDownloadResponseVo executeYtDlpInfo(VideoDownloadRequestDto request) {
        try {
            // 构建yt-dlp信息获取命令
            List<String> command = buildYtDlpCommand(request, true);
            
            // 执行命令
            ProcessBuilder pb = new ProcessBuilder(command);
            Process process = pb.start();
            
            // 读取输出
            StringBuilder output = new StringBuilder();
            try (BufferedReader reader = new BufferedReader(new InputStreamReader(process.getInputStream()))) {
                String line;
                while ((line = reader.readLine()) != null) {
                    output.append(line).append("\n");
                }
            }
            
            // 等待完成
            boolean finished = process.waitFor(30, TimeUnit.SECONDS);
            
            if (!finished) {
                process.destroyForcibly();
                throw new RuntimeException("yt-dlp信息获取超时");
            }
            
            if (process.exitValue() != 0) {
                throw new RuntimeException("yt-dlp信息获取失败，退出码: " + process.exitValue());
            }
            
            // 解析输出
            VideoDownloadResponseVo response = new VideoDownloadResponseVo();
            response.setDownloader(getDownloaderName());
            parseYtDlpOutput(output.toString(), response);
            
            return response;
            
        } catch (Exception e) {
            log.error("执行yt-dlp信息获取失败", e);
            throw new RuntimeException("执行yt-dlp信息获取失败: " + e.getMessage(), e);
        }
    }
    
    /**
     * 构建yt-dlp命令
     */
    private List<String> buildYtDlpCommand(VideoDownloadRequestDto request, boolean infoOnly) {
        List<String> command = new ArrayList<>();
        VideoDownloadProperties.YtDlpConfig config = videoDownloadProperties.getYtDlp();
        
        command.add(config.getExecutablePath());
        
        if (infoOnly) {
            command.add("--dump-json");
        } else {
            // 下载模式
            command.add("--format");
            command.add(request.getFormat() != null ? request.getFormat() : config.getDefaultFormat());
            
            if (request.getAudio() != null && request.getAudio()) {
                command.add("--extract-audio");
                command.add("--audio-format");
                command.add(config.getAudioFormat());
            }
            
            command.add("--output");
            command.add(config.getOutputTemplate());
        }
        
        // 添加URL
        command.add(request.getUrl());
        
        return command;
    }
    
    /**
     * 解析yt-dlp输出
     */
    private void parseYtDlpOutput(String output, VideoDownloadResponseVo response) {
        // TODO: 解析yt-dlp的JSON输出
        // 这里应该解析yt-dlp返回的JSON格式数据
        // 提取标题、作者、时长、格式等信息
        
        // 临时模拟数据
        response.setTitle("yt-dlp下载的视频");
        response.setAuthor("未知作者");
        response.setDescription("通过yt-dlp获取的视频");
        response.setDuration(0L);
        
        // 模拟视频文件信息
        List<VideoDownloadResponseVo.VideoFileInfo> videoFiles = new ArrayList<>();
        VideoDownloadResponseVo.VideoFileInfo videoFile = new VideoDownloadResponseVo.VideoFileInfo();
        videoFile.setUrl("local_file_path");
        videoFile.setFormat("mp4");
        videoFile.setQuality("best");
        videoFiles.add(videoFile);
        
        response.setVideoFiles(videoFiles);
    }
}
