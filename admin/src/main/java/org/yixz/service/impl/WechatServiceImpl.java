package org.yixz.service.impl;

import cn.hutool.core.date.DateUtil;
import cn.hutool.core.util.ObjectUtil;
import cn.hutool.core.util.RandomUtil;
import cn.hutool.core.util.StrUtil;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.util.StringUtils;
import org.yixz.common.constants.WeChatConstants;
import org.yixz.common.util.ConverterUtil;
import org.yixz.common.util.RedisUtil;
import org.yixz.common.util.RestTemplateUtil;
import org.yixz.common.util.TokenUtil;
import org.yixz.entity.mysql.WechatExceptions;
import org.yixz.entity.mysql.XcUser;
import org.yixz.entity.vo.TokenVO;
import org.yixz.entity.vo.UserVO;
import org.yixz.entity.vo.WeChatAccessTokenVo;
import org.yixz.entity.vo.WeChatMiniAuthorizeVo;
import org.yixz.service.WechatExceptionsService;
import org.yixz.service.WechatService;
import org.yixz.service.XcUserService;

import java.time.Duration;


@Service
public class WechatServiceImpl implements WechatService {

    private static final Logger logger = LoggerFactory.getLogger(WechatServiceImpl.class);

    @Autowired
    private RestTemplateUtil restTemplateUtil;

    @Autowired
    private XcUserService xcUserService;


    @Autowired
    private WechatExceptionsService wechatExceptionsService;


    @Autowired
    private TokenUtil tokenUtil;




    /**
     * 微信登录小程序授权登录
     * @param code code
     * @return
     */
    @Override
    public TokenVO weChatAuthorizeProgramLogin(JSONObject code) {
        //System.out.println("wechat登录参数:"+code.toJSONString());
        WeChatMiniAuthorizeVo response = this.miniAuthCode(code);
//        WeChatMiniAuthorizeVo response = new WeChatMiniAuthorizeVo();
//        response.setOpenId("ospFe43DQKDHoUjfDFdlOGZD-hmI");
        System.out.println("小程序登陆成功 = " + JSON.toJSONString(response));
        //检测是否存在
        XcUser xcUser = xcUserService.getOne(new LambdaQueryWrapper<XcUser>()
                .eq(XcUser::getOpenid, response.getOpenId()));

        boolean isSave = false;
        if (ObjectUtil.isEmpty(xcUser)){
            isSave = true;
            xcUser = XcUser.builder().openid(response.getOpenId()).unionid(response.getUnionId()).build();
            // 如果推荐人ID有传过来，则绑定
            String inviteUid = code.getString("shareId");
            if (StringUtils.hasText(inviteUid)){
                XcUser inviteUser = xcUserService.getById(inviteUid);
                if (inviteUser != null){
                    xcUser.setInviteUid(String.valueOf(inviteUser.getId()));
                }
            }
        }


        //OcUser user = ocUserList.get(0);
        return this.login(xcUser, isSave);
    }



    /**
     * 小程序登录凭证校验
     * @return 小程序登录校验对象
     */
    @Override
    public WeChatMiniAuthorizeVo miniAuthCode(JSONObject code) {
        String appId = WeChatConstants.WECHAT_MINI_APPID;
        if (StrUtil.isBlank(appId)) {
            throw new RuntimeException("微信小程序appId未配置");
        }
        String secret = WeChatConstants.WECHAT_MINI_APPSECRET;
        if (StrUtil.isBlank(secret)) {
            throw new RuntimeException("微信小程序secret未配置");
        }
        String url = StrUtil.format(WeChatConstants.WECHAT_MINI_SNS_AUTH_CODE2SESSION_URL, appId, secret, code.getString("code"));
        JSONObject data = restTemplateUtil.getData(url);
        System.out.println("========data:" + data);
        if (ObjectUtil.isNull(data)) {
            throw new RuntimeException("微信平台接口异常，没任何数据返回！");
        }
        if (data.containsKey("errcode") && !data.getString("errcode").equals("0")) {
            if (data.containsKey("errmsg")) {
                // 保存到微信异常表
                wxExceptionDispose(data, "微信小程序登录凭证校验异常");
//                throw new RuntimeException("微信接口调用失败：" + data.getString("errcode") + data.getString("errmsg"));
                throw new RuntimeException("微信接口调用失败");
            }
        }
        return JSONObject.parseObject(data.toJSONString(), WeChatMiniAuthorizeVo.class);
    }


    /**
     * 获取小程序accessToken
     * @return accessToken
     */
    @Override
    public String getMiniAccessToken() {
        boolean exists = RedisUtil.hasKey(WeChatConstants.REDIS_WECAHT_MINI_ACCESS_TOKEN_KEY);
        if (exists) {
            Object accessToken = RedisUtil.getCacheObject(WeChatConstants.REDIS_WECAHT_MINI_ACCESS_TOKEN_KEY);
            return accessToken.toString();
        }
        String appId = WeChatConstants.WECHAT_MINI_APPID;
        if (StrUtil.isBlank(appId)) {
            throw new RuntimeException("微信小程序appId未设置");
        }
        String secret = WeChatConstants.WECHAT_MINI_APPSECRET;
        if (StrUtil.isBlank(secret)) {
            throw new RuntimeException("微信小程序secret未设置");
        }
        WeChatAccessTokenVo accessTokenVo = this.getAccessToken(appId, secret, "mini");
        // 缓存accessToken
        RedisUtil.setCacheObject(WeChatConstants.REDIS_WECAHT_MINI_ACCESS_TOKEN_KEY, accessTokenVo.getAccessToken(),
                Duration.ofSeconds(accessTokenVo.getExpiresIn().longValue() - 1800L));
        return accessTokenVo.getAccessToken();
    }


    /**
     * 获取公众号accessToken
     */
    @Override
    public String getPublicAccessToken() {
        boolean exists = RedisUtil.hasKey(WeChatConstants.REDIS_WECAHT_PUBLIC_ACCESS_TOKEN_KEY);
        if (exists) {
            Object accessToken = RedisUtil.getCacheObject(WeChatConstants.REDIS_WECAHT_PUBLIC_ACCESS_TOKEN_KEY);
            return accessToken.toString();
        }
//        String appId = systemConfigService.getValueByKey(WeChatConstants.WECHAT_PUBLIC_APPID);
//        if (StrUtil.isBlank(appId)) {
//            throw new MyErrorException("微信公众号appId未设置");
//        }
        String appId = WeChatConstants.WECHAT_PUBLIC_APPID;
        String secret = WeChatConstants.WECHAT_PUBLIC_APPSECRET;
//        String secret = systemConfigService.getValueByKey(WeChatConstants.WECHAT_PUBLIC_APPSECRET);
//        if (StrUtil.isBlank(secret)) {
//            throw new MyErrorException("微信公众号secret未设置");
//        }
        WeChatAccessTokenVo accessTokenVo = getAccessToken(appId, secret, "public");
        // 缓存accessToken
        RedisUtil.setCacheObject(WeChatConstants.REDIS_WECAHT_PUBLIC_ACCESS_TOKEN_KEY, accessTokenVo.getAccessToken(),
                Duration.ofSeconds(accessTokenVo.getExpiresIn().longValue() - 1800L));
        return accessTokenVo.getAccessToken();
    }


    /**
     * 小程序获取用户手机号
     * @return 小程序获取用户手机号
     */
    private JSONObject getuserphonenumber(JSONObject json) {
        String url = StrUtil.format(WeChatConstants.WECHAT_MINI_GET_USER_PHONE_NUMBER_URL, getMiniAccessToken());
        JSONObject data = restTemplateUtil.postJsonDataAndReturnJson(url, new JSONObject() {{put("code", json.getString("code"));}});
        if (ObjectUtil.isNull(data)) {
            throw new RuntimeException("微信平台接口异常，没任何数据返回！");
        }
        if (data.containsKey("errcode") && !data.getString("errcode").equals("0")) {
            if (data.containsKey("errmsg")) {
                // 保存到微信异常表
                wxExceptionDispose(data, "微信小程序获取用户手机号异常");
//                throw new RuntimeException("微信接口调用失败：" + data.getString("errcode") + data.getString("errmsg"));
                throw new RuntimeException("微信接口调用失败");
            }
        }
        return data;
    }

    /**
     * 获取微信accessToken
     * @param appId appId
     * @param secret secret
     * @param type mini-小程序，public-公众号，app-app
     * @return WeChatAccessTokenVo
     */
    private WeChatAccessTokenVo getAccessToken(String appId, String secret, String type) {
        String url = StrUtil.format(WeChatConstants.WECHAT_ACCESS_TOKEN_URL, appId, secret);
        JSONObject data = restTemplateUtil.getData(url);
        if (ObjectUtil.isNull(data)) {
            throw new RuntimeException("微信平台接口异常，没任何数据返回！");
        }
        if (data.containsKey("errcode") && !data.getString("errcode").equals("0")) {
            if (data.containsKey("errmsg")) {
                // 保存到微信异常表
                wxExceptionDispose(data, StrUtil.format("微信获取accessToken异常，{}端", type));
//                throw new RuntimeException("微信接口调用失败：" + data.getString("errcode") + data.getString("errmsg"));
                throw new RuntimeException("微信接口调用失败");
            }
        }
        return JSONObject.parseObject(data.toJSONString(), WeChatAccessTokenVo.class);
    }

    /**
     * 微信异常处理
     * @param jsonObject 微信返回数据
     * @param remark 备注
     */
    private void wxExceptionDispose(JSONObject jsonObject, String remark) {
        WechatExceptions wechatExceptions = new WechatExceptions();
        wechatExceptions.setErrcode(jsonObject.getString("errcode"));
        wechatExceptions.setErrmsg(StrUtil.isNotBlank(jsonObject.getString("errmsg")) ? jsonObject.getString("errmsg") : "");
        wechatExceptions.setData(jsonObject.toJSONString());
        wechatExceptions.setRemark(remark);
        wechatExceptions.setCreateTime(DateUtil.date());
        wechatExceptions.setUpdateTime(DateUtil.date());
        wechatExceptionsService.save(wechatExceptions);
    }



    private TokenVO login(XcUser user, boolean isSave) {

        if (isSave) {

            // ID为空则为新增
            if (!StringUtils.hasText(String.valueOf(user.getId()))){
                // user.setId(UUID.randomUUID().toString());
                // 设置默认昵称
                user.setNickname("小龙人" + RandomUtil.randomNumbers(8));
                // 设置默认头像
                //user.setAvatar(CommonConstants.AVATAR_URL);


                // 失败了则报错
                if (!xcUserService.save(user)){
                    throw new RuntimeException("账号注册失败~");
                }

            }
            // 更新
            else {
                //保存关联的信息
                xcUserService.updateById(user);
            }

        }


        TokenVO tokenVO = new TokenVO();
        UserVO userVO = ConverterUtil.convert(user, UserVO.class);
        // userVO.setUserId(user.getId());
        //userVO.setShareId(nuserShare.getId());
        tokenVO.setUser(userVO);
        try {
            tokenUtil.createToken(tokenVO);
        } catch (Exception e) {
            logger.error(StrUtil.format("小程序登录生成token失败，uid={}", user.getId()));
            e.printStackTrace();
        }

        return tokenVO;
    }



}
