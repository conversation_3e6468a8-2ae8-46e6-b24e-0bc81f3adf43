package org.yixz.service;

import jakarta.annotation.PostConstruct;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.web.multipart.MultipartFile;
import org.yixz.common.enums.AiProviderEnum;
import org.yixz.common.exception.AudioConversionException;
import org.yixz.common.converter.AudioConverter;
import org.yixz.common.converter.AudioFormatDetector;
import org.yixz.common.converter.FFmpegAudioConverter;
import org.yixz.common.converter.MediaSeparationResult;
import org.yixz.common.util.MediaConverterUtil;

import java.io.File;
import java.io.InputStream;

/**
 * 音频转换服务
 * 统一管理音频格式检测和转换，自动选择最佳转换方案
 *
 * <AUTHOR>
 * @date 2025-07-22
 */
@Slf4j
@Service
public class AudioConversionService {

    private boolean javeAvailable = false;
    private boolean ffmpegAvailable = false;

    /**
     * 转换结果类
     */
    public static class ConversionResult {
        private boolean success;
        private InputStream audioStream;
        private File tempFile; // 需要手动清理
        private String message;
        private AudioFormatDetector.AudioInfo sourceInfo;
        private AudioFormatDetector.AudioInfo targetInfo;
        private String converterUsed;
        private long conversionTimeMs;

        // Getters and Setters
        public boolean isSuccess() { return success; }
        public void setSuccess(boolean success) { this.success = success; }
        
        public InputStream getAudioStream() { return audioStream; }
        public void setAudioStream(InputStream audioStream) { this.audioStream = audioStream; }
        
        public File getTempFile() { return tempFile; }
        public void setTempFile(File tempFile) { this.tempFile = tempFile; }
        
        public String getMessage() { return message; }
        public void setMessage(String message) { this.message = message; }
        
        public AudioFormatDetector.AudioInfo getSourceInfo() { return sourceInfo; }
        public void setSourceInfo(AudioFormatDetector.AudioInfo sourceInfo) { this.sourceInfo = sourceInfo; }
        
        public AudioFormatDetector.AudioInfo getTargetInfo() { return targetInfo; }
        public void setTargetInfo(AudioFormatDetector.AudioInfo targetInfo) { this.targetInfo = targetInfo; }
        
        public String getConverterUsed() { return converterUsed; }
        public void setConverterUsed(String converterUsed) { this.converterUsed = converterUsed; }
        
        public long getConversionTimeMs() { return conversionTimeMs; }
        public void setConversionTimeMs(long conversionTimeMs) { this.conversionTimeMs = conversionTimeMs; }

        /**
         * 清理临时文件
         */
        public void cleanup() {
            if (tempFile != null && tempFile.exists()) {
                AudioConverter.cleanupTempFile(tempFile);
                tempFile = null;
            }
        }

        @Override
        public String toString() {
            return String.format("ConversionResult{success=%s, converterUsed='%s', message='%s', conversionTime=%dms}",
                    success, converterUsed, message, conversionTimeMs);
        }
    }

    @PostConstruct
    public void init() {
        // 检查可用的转换器
        javeAvailable = MediaConverterUtil.isJaveAvailable();
        ffmpegAvailable = MediaConverterUtil.isFFmpegAvailable();

        // 获取系统信息
        MediaConverterUtil.SystemInfo systemInfo = MediaConverterUtil.getSystemInfo();

        log.info("系统信息: {}", systemInfo);
        log.info("音频转换器可用性 - JAVE2: {}, FFmpeg: {}", javeAvailable, ffmpegAvailable);

        if (!javeAvailable && !ffmpegAvailable) {
            if (MediaConverterUtil.isAppleSiliconMac()) {
                log.warn("检测到Apple Silicon Mac，JAVE2可能不支持。建议安装Homebrew FFmpeg: brew install ffmpeg");
            } else {
                log.warn("没有可用的音频转换器！请安装FFmpeg或确保JAVE2依赖正确");
            }
        } else if (!javeAvailable && ffmpegAvailable) {
            log.info("JAVE2不可用，将使用系统FFmpeg进行音频转换");
        } else if (javeAvailable && !ffmpegAvailable) {
            log.info("FFmpeg不可用，将使用JAVE2进行音频转换");
        } else {
            log.info("JAVE2和FFmpeg都可用，优先使用FFmpeg");
        }
    }

    /**
     * 转换音频文件为阿里云ASR兼容格式
     */
    public ConversionResult convertForAliyunASR(MultipartFile audioFile) {
        ConversionResult result = new ConversionResult();
        
        try {
            // 首先检测音频格式
            AudioFormatDetector.AudioInfo sourceInfo = AudioFormatDetector.detectAudioInfo(audioFile);
            result.setSourceInfo(sourceInfo);

            log.info("音频格式检测: 格式={}, 采样率={}Hz, 声道={}, 时长={}ms",
                    sourceInfo.getFormat(), sourceInfo.getSampleRate(), sourceInfo.getChannels(),
                    sourceInfo.getDuration());

            // 进行阿里云ASR特定的格式验证
            AudioFormatDetector.validateAliyunAudioFormat(sourceInfo);

            log.info("阿里云ASR格式验证: 是否支持={}, 原因={}", sourceInfo.isSupported(), sourceInfo.getReason());

            // 如果已经符合要求，直接返回原文件流
            if (sourceInfo.isSupported()) {
                result.setSuccess(true);
                result.setAudioStream(audioFile.getInputStream());
                result.setMessage("音频格式已符合阿里云ASR要求，无需转换");
                result.setTargetInfo(sourceInfo);
                result.setConverterUsed("无需转换");
                result.setConversionTimeMs(0);
                log.info("音频格式符合阿里云ASR要求，跳过转换");
                return result;
            }

            log.info("音频需要转换: {}", sourceInfo.getReason());
            
            // 需要转换，选择转换器（优先FFmpeg）
            if (ffmpegAvailable) {
                log.info("使用FFmpeg进行音频转换");
                return convertWithFFmpeg(audioFile, result, AiProviderEnum.ALIYUN);
            } else if (javeAvailable) {
                log.info("使用JAVE2进行音频转换");
                return convertWithJave(audioFile, result, AiProviderEnum.ALIYUN);
            } else {
                log.error("没有可用的音频转换器 - FFmpeg: {}, JAVE2: {}", ffmpegAvailable, javeAvailable);
                throw new AudioConversionException("没有可用的音频转换器，请安装FFmpeg或确保JAVE2依赖正确");
            }
            
        } catch (AudioConversionException e) {
            // 音频转换异常直接抛出，不包装
            throw e;
        } catch (Exception e) {
            log.error("音频转换服务异常", e);
            throw new AudioConversionException("音频转换服务异常: " + e.getMessage(), e);
        }
    }

    /**
     * 转换音频文件为百度ASR兼容格式
     */
    public ConversionResult convertForBaiduASR(MultipartFile audioFile) {
        ConversionResult result = new ConversionResult();

        try {
            // 首先检测音频格式
            AudioFormatDetector.AudioInfo sourceInfo = AudioFormatDetector.detectAudioInfo(audioFile);
            result.setSourceInfo(sourceInfo);

            log.info("音频格式检测: 格式={}, 采样率={}Hz, 声道={}, 时长={}ms",
                    sourceInfo.getFormat(), sourceInfo.getSampleRate(), sourceInfo.getChannels(),
                    sourceInfo.getDuration());

            // 使用百度ASR的验证逻辑
            AudioFormatDetector.validateBaiduAudioFormat(sourceInfo);

            log.info("百度ASR格式验证: 是否支持={}, 原因={}", sourceInfo.isSupported(), sourceInfo.getReason());

            // 如果已经符合要求，直接返回原文件流
            if (sourceInfo.isSupported()) {
                result.setSuccess(true);
                result.setAudioStream(audioFile.getInputStream());
                result.setMessage("音频格式已符合百度ASR要求，无需转换");
                result.setTargetInfo(sourceInfo);
                result.setConverterUsed("无需转换");
                result.setConversionTimeMs(0);
                log.info("音频格式符合百度ASR要求，跳过转换");
                return result;
            }

            log.info("音频需要转换: {}", sourceInfo.getReason());

            // 需要转换，选择转换器（优先FFmpeg）
            if (ffmpegAvailable) {
                log.info("使用FFmpeg进行百度ASR音频转换");
                return convertWithFFmpeg(audioFile, result, AiProviderEnum.BAIDU);
            } else if (javeAvailable) {
                log.info("使用JAVE2进行百度ASR音频转换");
                return convertWithJave(audioFile, result, AiProviderEnum.BAIDU);
            } else {
                log.error("没有可用的音频转换器 - FFmpeg: {}, JAVE2: {}", ffmpegAvailable, javeAvailable);
                throw new AudioConversionException("没有可用的音频转换器，请安装FFmpeg或确保JAVE2依赖正确");
            }

        } catch (AudioConversionException e) {
            // 音频转换异常直接抛出，不包装
            throw e;
        } catch (Exception e) {
            log.error("百度ASR音频转换服务异常", e);
            throw new AudioConversionException("百度ASR音频转换服务异常: " + e.getMessage(), e);
        }
    }

    /**
     * 检查音频文件是否需要转换
     */
    public boolean needsConversion(MultipartFile audioFile) {
        try {
            AudioFormatDetector.AudioInfo info = AudioFormatDetector.detectAudioInfo(audioFile);
            return !info.isSupported();
        } catch (Exception e) {
            log.error("检查音频格式失败", e);
            return true; // 出错时假设需要转换
        }
    }

    /**
     * 获取音频文件信息
     */
    public AudioFormatDetector.AudioInfo getAudioInfo(MultipartFile audioFile) {
        return AudioFormatDetector.detectAudioInfo(audioFile);
    }

    /**
     * 使用JAVE2转换音频格式（通用方法）
     */
    private ConversionResult convertWithJave(MultipartFile audioFile, ConversionResult result, AiProviderEnum provider) {
        try {
            AudioConverter.ConversionResult javeResult;
            String providerName;

            // 根据服务提供商选择转换方法
            switch (provider) {
                case ALIYUN:
                    javeResult = AudioConverter.convertToAliyunFormat(audioFile);
                    providerName = "阿里云";
                    break;
                case BAIDU:
                    javeResult = AudioConverter.convertToBaiduFormat(audioFile);
                    providerName = "百度ASR";
                    break;
                default:
                    throw new AudioConversionException("不支持的服务提供商: " + provider);
            }

            result.setSuccess(javeResult.isSuccess());
            result.setMessage(javeResult.getMessage());
            result.setTargetInfo(javeResult.getTargetInfo());
            result.setConverterUsed("JAVE2");
            result.setConversionTimeMs(javeResult.getConversionTimeMs());

            if (javeResult.isSuccess() && javeResult.getConvertedFile() != null) {
                result.setAudioStream(AudioConverter.convertedFileToInputStream(javeResult.getConvertedFile()));
                result.setTempFile(javeResult.getConvertedFile());
            }

            if (javeResult.isSuccess()) {
                log.info("JAVE2{}转换成功，耗时: {}ms", providerName, javeResult.getConversionTimeMs());
            } else {
                log.warn("JAVE2{}转换失败: {}", providerName, javeResult.getMessage());
            }
            return result;

        } catch (Exception e) {
            String providerName = provider == AiProviderEnum.ALIYUN ? "阿里云" : "百度ASR";
            log.error("JAVE2{}转换异常: {}", providerName, e.getMessage());
            throw new AudioConversionException("JAVE2" + providerName + "转换失败: " + e.getMessage(), e);
        }
    }

    /**
     * 使用FFmpeg转换音频格式（通用方法）
     */
    private ConversionResult convertWithFFmpeg(MultipartFile audioFile, ConversionResult result, AiProviderEnum provider) {
        try {
            FFmpegAudioConverter.FFmpegResult ffmpegResult;
            String providerName = switch (provider) {
                case ALIYUN -> {
                    ffmpegResult = FFmpegAudioConverter.convertToAliyunFormat(audioFile);
                    yield "阿里云";
                }
                case BAIDU -> {
                    ffmpegResult = FFmpegAudioConverter.convertToBaiduFormat(audioFile);
                    yield "百度";
                }
                default -> throw new AudioConversionException("不支持的服务提供商: " + provider);
            };

            // 根据服务提供商选择转换方法

            result.setSuccess(ffmpegResult.isSuccess());
            result.setMessage(ffmpegResult.getMessage());
            result.setConverterUsed("FFmpeg");
            result.setConversionTimeMs(ffmpegResult.getConversionTimeMs());

            if (ffmpegResult.isSuccess() && ffmpegResult.getConvertedFile() != null) {
                // 重新检测转换后的文件信息
                AudioFormatDetector.AudioInfo targetInfo = AudioFormatDetector.detectAudioInfo(ffmpegResult.getConvertedFile());
                result.setTargetInfo(targetInfo);

                result.setAudioStream(AudioConverter.convertedFileToInputStream(ffmpegResult.getConvertedFile()));
                result.setTempFile(ffmpegResult.getConvertedFile());
            }

            if (ffmpegResult.isSuccess()) {
                log.info("FFmpeg{}转换成功，耗时: {}ms", providerName, ffmpegResult.getConversionTimeMs());
            } else {
                log.warn("FFmpeg{}转换失败: {}", providerName, ffmpegResult.getMessage());
            }
            return result;

        } catch (Exception e) {
            String providerName = provider == AiProviderEnum.ALIYUN ? "阿里云" : "百度ASR";
            log.error("FFmpeg{}转换异常: {}", providerName, e.getMessage());
            throw new AudioConversionException("FFmpeg" + providerName + "转换失败: " + e.getMessage(), e);
        }
    }

    /**
     * 从视频文件中分离音频
     *
     * @param videoFile 视频文件
     * @return 音频分离结果
     */
    public MediaSeparationResult separateAudioFromVideo(File videoFile) {
        return separateAudioFromVideo(videoFile, "wav");
    }

    /**
     * 从视频文件中分离音频（指定格式）
     *
     * @param videoFile 视频文件
     * @param outputFormat 输出音频格式
     * @return 音频分离结果
     */
    public MediaSeparationResult separateAudioFromVideo(File videoFile, String outputFormat) {
        try {
            log.info("开始音频分离，文件: {}, 输出格式: {}", videoFile.getName(), outputFormat);

            // 检查转换器可用性
            if (!javeAvailable && !ffmpegAvailable) {
                throw new AudioConversionException("没有可用的音频转换器，请安装FFmpeg或确保JAVE2依赖正确");
            }

            // 优先使用FFmpeg进行音频分离
            if (ffmpegAvailable) {
                log.info("使用FFmpeg进行音频分离");
                return FFmpegAudioConverter.separateAudioFromVideo(videoFile, outputFormat);
            } else if (javeAvailable) {
                log.info("使用JAVE2进行音频分离");
                return AudioConverter.separateAudioFromVideo(videoFile, outputFormat);
            } else {
                log.info("没有可用的音频分离器");
                throw new AudioConversionException("音频分离功能需要FFmpeg或JAVE2支持，当前都不可用");
            }

        } catch (AudioConversionException e) {
            // 音频转换异常直接抛出，不包装
            throw e;
        } catch (Exception e) {
            log.error("音频分离服务异常", e);
            throw new AudioConversionException("音频分离服务异常: " + e.getMessage(), e);
        }
    }

    /**
     * 从视频文件中提取指定音频轨道
     *
     * @param videoFile 视频文件
     * @param outputFormat 输出音频格式
     * @param audioTrackIndex 音频轨道索引
     * @return 音频分离结果
     */
    public MediaSeparationResult extractAudioTrack(File videoFile, String outputFormat, int audioTrackIndex) {
        try {
            log.info("开始提取音频轨道，文件: {}, 轨道索引: {}, 输出格式: {}",
                    videoFile.getName(), audioTrackIndex, outputFormat);

            // 检查转换器可用性
            if (!ffmpegAvailable && !javeAvailable) {
                throw new AudioConversionException("音频轨道提取功能需要FFmpeg或JAVE2支持，当前都不可用");
            }

            // 优先使用FFmpeg，但目前只有JAVE2支持指定轨道提取
            if (javeAvailable) {
                log.info("使用JAVE2进行音频轨道提取（支持指定轨道）");
                return AudioConverter.separateAudioFromVideo(videoFile, outputFormat, audioTrackIndex);
            } else {
                log.info("使用FFmpeg进行音频分离（提取默认轨道）");
                return FFmpegAudioConverter.separateAudioFromVideo(videoFile, outputFormat);
            }

        } catch (AudioConversionException e) {
            throw e;
        } catch (Exception e) {
            log.error("音频轨道提取服务异常", e);
            throw new AudioConversionException("音频轨道提取服务异常: " + e.getMessage(), e);
        }
    }

    /**
     * 获取服务状态
     */
    public ServiceStatus getServiceStatus() {
        ServiceStatus status = new ServiceStatus();
        status.setJaveAvailable(javeAvailable);
        status.setFfmpegAvailable(ffmpegAvailable);
        status.setAnyConverterAvailable(javeAvailable || ffmpegAvailable);

        if (javeAvailable && ffmpegAvailable) {
            status.setPreferredConverter("FFmpeg");
            status.setFallbackConverter("JAVE2");
        } else if (ffmpegAvailable) {
            status.setPreferredConverter("FFmpeg");
        } else if (javeAvailable) {
            status.setPreferredConverter("JAVE2");
        } else {
            status.setPreferredConverter("无");
        }

        return status;
    }

    /**
     * 服务状态类
     */
    public static class ServiceStatus {
        private boolean javeAvailable;
        private boolean ffmpegAvailable;
        private boolean anyConverterAvailable;
        private String preferredConverter;
        private String fallbackConverter;

        // Getters and Setters
        public boolean isJaveAvailable() { return javeAvailable; }
        public void setJaveAvailable(boolean javeAvailable) { this.javeAvailable = javeAvailable; }
        
        public boolean isFfmpegAvailable() { return ffmpegAvailable; }
        public void setFfmpegAvailable(boolean ffmpegAvailable) { this.ffmpegAvailable = ffmpegAvailable; }
        
        public boolean isAnyConverterAvailable() { return anyConverterAvailable; }
        public void setAnyConverterAvailable(boolean anyConverterAvailable) { this.anyConverterAvailable = anyConverterAvailable; }
        
        public String getPreferredConverter() { return preferredConverter; }
        public void setPreferredConverter(String preferredConverter) { this.preferredConverter = preferredConverter; }
        
        public String getFallbackConverter() { return fallbackConverter; }
        public void setFallbackConverter(String fallbackConverter) { this.fallbackConverter = fallbackConverter; }

        @Override
        public String toString() {
            return String.format("ServiceStatus{jave=%s, ffmpeg=%s, preferred='%s', fallback='%s'}",
                    javeAvailable, ffmpegAvailable, preferredConverter, fallbackConverter);
        }
    }
}
