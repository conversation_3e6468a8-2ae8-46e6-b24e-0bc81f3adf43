package org.yixz.service;

import org.yixz.entity.dto.VideoDownloadRequestDto;
import org.yixz.entity.vo.VideoDownloadResponseVo;

/**
 * 视频下载服务接口
 *
 * <AUTHOR>
 * @date 2025-07-26
 */
public interface VideoDownloadService {
    
    /**
     * 下载视频
     *
     * @param request 下载请求参数
     * @return 下载结果
     */
    VideoDownloadResponseVo downloadVideo(VideoDownloadRequestDto request);
    
    /**
     * 获取视频信息（不下载）
     *
     * @param request 请求参数
     * @return 视频信息
     */
    VideoDownloadResponseVo getVideoInfo(VideoDownloadRequestDto request);
    
    /**
     * 获取下载器名称
     *
     * @return 下载器名称
     */
    String getDownloaderName();
    
    /**
     * 检查下载器是否可用
     *
     * @return 是否可用
     */
    boolean isAvailable();
    
    /**
     * 检查是否支持指定URL
     *
     * @param url 视频URL
     * @return 是否支持
     */
    boolean supports(String url);
}
