package org.yixz.factory;

import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import org.yixz.common.properties.VideoDownloadProperties;
import org.yixz.common.enums.VideoPlatformEnum;
import org.yixz.common.util.PlatformUrlDetector;
import org.yixz.service.VideoDownloadService;

import java.util.Map;
import java.util.Optional;

/**
 * 视频下载服务工厂
 *
 * <AUTHOR>
 * @date 2025-07-26
 */
@Slf4j
@Component
public class VideoDownloadFactory {
    
    @Autowired
    private VideoDownloadProperties videoDownloadProperties;
    
    @Autowired
    private Map<String, VideoDownloadService> videoDownloadServiceMap;
    
    /**
     * 根据URL自动选择视频下载服务
     *
     * @param url 视频URL
     * @return 视频下载服务实例
     */
    public VideoDownloadService getVideoDownloadService(String url) {
        return getVideoDownloadService(url, null, false);
    }
    
    /**
     * 根据URL和可选平台参数获取视频下载服务
     *
     * @param url 视频URL
     * @param platform 可选的平台指定
     * @param forceYtDlp 是否强制使用yt-dlp
     * @return 视频下载服务实例
     */
    public VideoDownloadService getVideoDownloadService(String url, Optional<String> platform, boolean forceYtDlp) {
        try {
            // 如果强制使用yt-dlp
            if (forceYtDlp) {
                log.debug("强制使用yt-dlp下载器");
                return getYtDlpService();
            }
            
            // 确定平台
            VideoPlatformEnum targetPlatform;
            if (platform != null && platform.isPresent()) {
                // 使用指定的平台
                targetPlatform = VideoPlatformEnum.getByCode(platform.get());
                log.debug("使用指定平台: {}", targetPlatform.getName());
            } else {
                // 自动检测平台
                targetPlatform = PlatformUrlDetector.detectPlatform(url);
                log.debug("自动检测到平台: {}", targetPlatform.getName());
            }
            
            // 获取对应的服务实例
            VideoDownloadService service = getServiceByPlatform(targetPlatform);
            
            // 检查服务是否可用且支持该URL
            if (service.isAvailable() && service.supports(url)) {
                log.debug("使用{}下载器", service.getDownloaderName());
                return service;
            }
            
            // 如果平台特定服务不可用，且启用了降级机制，则使用yt-dlp
            if (videoDownloadProperties.isEnableFallback() && targetPlatform != VideoPlatformEnum.YT_DLP) {
                log.warn("平台特定下载器不可用，降级使用yt-dlp: {}", targetPlatform.getName());
                return getYtDlpService();
            }
            
            throw new IllegalArgumentException("没有可用的下载器支持该URL: " + url);
            
        } catch (Exception e) {
            log.error("获取视频下载服务失败: {}", e.getMessage(), e);
            
            // 最后的降级尝试
            if (videoDownloadProperties.isEnableFallback()) {
                log.warn("尝试最后的降级方案，使用yt-dlp");
                return getYtDlpService();
            }
            
            throw new RuntimeException("获取视频下载服务失败: " + e.getMessage(), e);
        }
    }
    
    /**
     * 根据平台获取对应的服务实例
     *
     * @param platform 视频平台
     * @return 服务实例
     */
    private VideoDownloadService getServiceByPlatform(VideoPlatformEnum platform) {
        String serviceKey = platform.getCode();
        
        // 检查是否启用平台特定下载器
        if (!videoDownloadProperties.isEnablePlatformSpecific() && platform != VideoPlatformEnum.YT_DLP) {
            log.debug("平台特定下载器已禁用，使用yt-dlp");
            return getYtDlpService();
        }
        
        VideoDownloadService service = videoDownloadServiceMap.get(serviceKey);
        if (service == null) {
            throw new IllegalArgumentException("未找到视频下载服务实现: " + serviceKey);
        }
        
        return service;
    }
    
    /**
     * 获取yt-dlp服务实例
     *
     * @return yt-dlp服务实例
     */
    private VideoDownloadService getYtDlpService() {
        VideoDownloadService ytDlpService = videoDownloadServiceMap.get(VideoPlatformEnum.YT_DLP.getCode());
        if (ytDlpService == null) {
            throw new IllegalArgumentException("未找到yt-dlp服务实现");
        }
        return ytDlpService;
    }
    
    /**
     * 获取所有可用的下载器
     *
     * @return 可用下载器列表
     */
    public Map<String, VideoDownloadService> getAvailableDownloaders() {
        return videoDownloadServiceMap;
    }
    
    /**
     * 检查指定平台的下载器是否可用
     *
     * @param platform 平台代码
     * @return 是否可用
     */
    public boolean isDownloaderAvailable(String platform) {
        try {
            VideoPlatformEnum platformEnum = VideoPlatformEnum.getByCode(platform);
            VideoDownloadService service = getServiceByPlatform(platformEnum);
            return service.isAvailable();
        } catch (Exception e) {
            log.debug("检查下载器可用性失败: {}", platform, e);
            return false;
        }
    }
}
