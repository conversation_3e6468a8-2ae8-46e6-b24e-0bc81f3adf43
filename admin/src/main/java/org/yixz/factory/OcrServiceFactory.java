package org.yixz.factory;

import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import org.yixz.common.properties.AiServiceProperties;
import org.yixz.common.enums.AiProviderEnum;
import org.yixz.service.ProviderService;

import java.util.Map;
import java.util.Optional;

/**
 * OCR服务工厂
 *
 * <AUTHOR>
 * @date 2025-07-20
 */
@Slf4j
@Component
public class OcrServiceFactory {
    
    @Autowired
    private AiServiceProperties aiServiceProperties;

    @Autowired
    private Map<String, ProviderService> providerServiceMap;
    
    /**
     * 获取OCR服务实例
     *
     * @return OCR服务实例
     */
    public ProviderService getOcrService() {
        return getOcrService(aiServiceProperties.getProvider());
    }

    /**
     * 获取OCR服务实例（支持可选provider参数）
     *
     * @param provider 可选的服务提供商，为空时使用默认配置
     * @return OCR服务实例
     */
    public ProviderService getOcrService(Optional<String> provider) {
        return getOcrService(provider.orElse(aiServiceProperties.getProvider()));
    }
    
    /**
     * 根据提供商获取OCR服务实例
     *
     * @param providerCode 提供商代码
     * @return OCR服务实例
     */
    public ProviderService getOcrService(String providerCode) {
        // 验证提供商代码是否有效
        AiProviderEnum.getByCode(providerCode); // 这会在无效时抛出异常

        ProviderService service = providerServiceMap.get(providerCode);
        if (service == null) {
            throw new IllegalArgumentException("未找到OCR服务提供商实现: " + providerCode);
        }

        log.debug("使用{}OCR服务", service.getProviderName());
        return service;
    }
}
