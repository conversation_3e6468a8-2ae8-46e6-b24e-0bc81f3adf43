package org.yixz.entity.mysql;

import com.baomidou.mybatisplus.annotation.*;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.experimental.Accessors;

import java.io.Serial;
import java.io.Serializable;
import java.util.Date;

/**
 * <AUTHOR>
 * @Date 2023/12/13 21:24
 * @Description
 **/
@Data
@Accessors(chain = true)
@Builder
@AllArgsConstructor
@NoArgsConstructor
@TableName("xc_user")
@Tag(name = "XcUser对象", description="用户表")
public class XcUser implements Serializable {

    @Serial
    private static final long serialVersionUID = -500162522845306294L;

    /**
     * 用户ID
     */
    @TableId(type = IdType.AUTO)
    private Integer id;

//    /**
//     * 手机号
//     */
//    private String phone;

    /**
     * 昵称
     */
    private String nickname;

    /**
     * 头像url
     */
    private String avatar;


    /**
     * 微信openid
     */
    private String openid;

    /**
     * 微信unionid
     */
    private String unionid;


    /**
     * 账号状态（0禁用/1启用）
     */
    private Boolean enabled;

    /**
     * 邀请此用户的用户ID
     */
    private String inviteUid;


    /**
     * 创建时间
     */
    @TableField(fill = FieldFill.INSERT)
    private Date createTime;

    /**
     * 更新时间
     */
    @TableField(fill = FieldFill.UPDATE)
    private Date updateTime;



}
