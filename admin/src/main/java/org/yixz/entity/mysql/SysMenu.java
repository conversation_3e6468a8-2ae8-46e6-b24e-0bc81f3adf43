package org.yixz.entity.mysql;

import com.baomidou.mybatisplus.annotation.TableName;
import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.extension.activerecord.Model;
import com.baomidou.mybatisplus.annotation.TableId;
import java.time.LocalDateTime;
import com.baomidou.mybatisplus.annotation.FieldFill;
import com.baomidou.mybatisplus.annotation.TableField;
import java.io.Serializable;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

/**
 * <p>
 * 菜单表
 * </p>
 *
 * <AUTHOR>
 * @since 2022-09-24
 */
@Data
@TableName("sys_menu")
public class SysMenu {
    /**
     * 主键
     */
    @TableId(value = "id", type = IdType.AUTO)
    private Integer id;

    /**
     * 父菜单id
     */
    private Integer parentId;

    /**
     * 菜单名称
     */
    private String name;

    /**
     * 菜单类型，button-按钮，directory-目录，menu-菜单
     */
    private String type;

    /**
     * 菜单类型，button-按钮，directory-目录，menu-菜单
     */
    private String route;

    /**
     * 图标
     */
    private String icon;

    /**
     * 路由
     */
    private String url;

    /**
     * 路由
     */
    private String permission;

    /**
     * 排序
     */
    private Integer sortNo;

    /**
     * 创建时间
     */
    @TableField(value = "created_date")
    private LocalDateTime createdDate;

    /**
     * 更新时间
     */
    @TableField(value = "updated_date")
    private LocalDateTime updatedDate;

    /**
     * 创建人员
     */
    @TableField(fill = FieldFill.INSERT)
    private String createdBy;

    /**
     * 更新人员
     */
    @TableField(fill = FieldFill.INSERT_UPDATE)
    private String updatedBy;

}
