package org.yixz.entity.vo;

import com.alibaba.fastjson.annotation.JSONField;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.*;
import lombok.experimental.Accessors;

import java.io.Serializable;


@Builder
@Accessors(chain = true)
@Setter
@Getter
@NoArgsConstructor
@AllArgsConstructor
@ToString
public class UserVO implements Serializable {

    private static final long serialVersionUID = -4148485233053987111L;

    @Schema(description = "用户ID")
    @JSONField(ordinal = 1)
    private String userId;


    @Schema(description = "账号状态（启动/禁用）")
    @JSONField(ordinal = 2)
    private Boolean enabled;


    @Schema(description = "用户openid")
    @JSONField(ordinal = 3)
    private String openid;

    @Schema(description = "用户unionid")
    @JSONField(ordinal = 4)
    private String unionid;


    @Schema(description = "头像")
    @JSONField(ordinal = 5)
    private String avatar;

    @Schema(description = "昵称")
    @JSONField(ordinal = 6)
    private String nickname;


//    @ApiModelProperty(value = "分享ID", position = 8)
//    @JSONField(ordinal = 8)
//    private Integer shareId;




}
