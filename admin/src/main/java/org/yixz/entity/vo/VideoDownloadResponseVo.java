package org.yixz.entity.vo;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.util.List;

/**
 * 视频下载响应VO
 *
 * <AUTHOR>
 * @date 2025-07-26
 */
@Data
@Schema(description = "视频下载响应")
public class VideoDownloadResponseVo {

    @Schema(description = "视频AID")
    private String aid;

    @Schema(description = "视频BVID")
    private String bvid;

    @Schema(description = "视频标题")
    private String title;
    
    @Schema(description = "视频描述")
    private String description;
    
    @Schema(description = "视频时长（秒）")
    private Long duration;
    
    @Schema(description = "视频作者")
    private String author;
    
    @Schema(description = "检测到的平台")
    private String platform;

    @Schema(description = "视频创建时间")
    private String createTime;
    
    @Schema(description = "使用的下载器")
    private String downloader;
    
    @Schema(description = "视频文件URL列表")
    private List<VideoFileInfo> videoFiles;
    
    @Schema(description = "音频文件URL列表")
    private List<VideoFileInfo> audioFiles;

    @Schema(description = "字幕文件URL列表")
    private List<CaptionsFileInfo> captionsFiles;
    
    @Schema(description = "缩略图URL")
    private String thumbnail;
    
    @Schema(description = "处理时间（毫秒）")
    private Long processTime;
    
    @Schema(description = "错误信息")
    private String errorMessage;
    
    /**
     * 视频文件信息
     */
    @Data
    @Schema(description = "视频文件信息")
    public static class VideoFileInfo {

        @Schema(description = "视频CID")
        private String cid;

        @Schema(description = "文件名")
        private String name;
        
        @Schema(description = "文件URL")
        private String url;

        @Schema(description = "文件路径")
        private String path;
        
        @Schema(description = "文件格式")
        private String format;

        @Schema(description = "文件质量")
        private String quality;
        
        @Schema(description = "文件大小（字节）")
        private Long fileSize;
        
        @Schema(description = "视频编码")
        private String videoCodec;
        
        @Schema(description = "音频编码")
        private String audioCodec;
        
        @Schema(description = "分辨率")
        private String resolution;
        
        @Schema(description = "帧率")
        private String fps;
    }

    @Data
    @Schema(description = "字幕文件信息")
    public static class CaptionsFileInfo {

        @Schema(description = "文件名")
        private String name;

        @Schema(description = "文件路径")
        private String path;

        @Schema(description = "文件格式")
        private String format;

        @Schema(description = "文件大小（字节）")
        private Long fileSize;
    }
}
