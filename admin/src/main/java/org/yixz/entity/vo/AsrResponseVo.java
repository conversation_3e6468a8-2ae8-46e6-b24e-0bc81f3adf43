package org.yixz.entity.vo;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

/**
 * 语音识别响应VO
 *
 * <AUTHOR>
 * @date 2025-07-20
 */
@Data
@Schema(description = "语音识别响应结果")
public class AsrResponseVo {
    
    @Schema(description = "识别结果文本")
    private String text;
    
    @Schema(description = "音频时长(秒)", example = "10.5")
    private Double duration;
    
    @Schema(description = "处理耗时(毫秒)", example = "1500")
    private Long processTime;
    
    @Schema(description = "服务提供商", example = "aliyun")
    private String provider;

    @Schema(description = "原始音频格式", example = "MP3")
    private String originalFormat;

    @Schema(description = "转换后音频格式", example = "WAV")
    private String convertedFormat;

    @Schema(description = "使用的转换器", example = "JAVE2")
    private String converterUsed;

    @Schema(description = "音频转换耗时(毫秒)", example = "500")
    private Long conversionTime;

}
