package org.yixz.entity.vo;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

/**
 * 图片文字识别响应VO
 *
 * <AUTHOR>
 * @date 2025-07-20
 */
@Data
@Schema(description = "图片文字识别响应结果")
public class OcrResponseVo {

    @Schema(description = "厂商记录Id，用于定位")
    private String logId;
    
    @Schema(description = "识别结果文本")
    private String text;
    
    @Schema(description = "服务提供商", example = "aliyun")
    private String provider;

    @Schema(description = "处理耗时(毫秒)", example = "800")
    private Long processTime;
}
