package org.yixz.entity.vo;

import com.alibaba.fastjson.annotation.JSONField;
import io.swagger.v3.oas.annotations.media.Schema;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.*;
import lombok.experimental.Accessors;

import java.io.Serializable;



@Builder
@Accessors(chain = true)
@Setter
@Getter
@NoArgsConstructor
@AllArgsConstructor
@ToString
@Tag(name = "登陆返回信息")
public class TokenVO implements Serializable {

    private static final long serialVersionUID = -4017770971773876263L;

    @Schema(description = "登陆密钥")
    @JSONField(ordinal = 1)
    private String token;

    @Schema(description = "用户信息")
    @JSONField(ordinal = 2)
    private UserVO user;


//    @ApiModelProperty(value = "用户业委会列表", position = 6)
//    @JSONField(ordinal = 6)
//    private List<OwnersCommitteeInformationSelectVO> ownersCommitteeList = new ArrayList<>();

}
