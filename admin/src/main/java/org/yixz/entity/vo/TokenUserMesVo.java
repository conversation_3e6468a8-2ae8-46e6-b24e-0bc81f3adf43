package org.yixz.entity.vo;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

import java.io.Serial;
import java.io.Serializable;

/**
 * Token解析的用户信息
 */
@Data
@Accessors(chain = true)
@EqualsAndHashCode(callSuper = false)
public class TokenUserMesVo implements Serializable {

    @Serial
    private static final long serialVersionUID = -3943429619986969984L;

    @Schema(description = "用户id")
    private String userId;

//    @ApiModelProperty(value = "手机号")
//    private String phone;


}
