package org.yixz.entity.vo.xhs;

import lombok.Data;
import java.util.ArrayList;
import java.util.List;

/**
 * 小红书作品类
 */
@Data
public class XhsNote {
    /**
     * 作品id
     */
    private String noteId;

    /**
     * 标题
     */
    private String title;

    /**
     * 内容
     */
    private String desc;

    /**
     * 类型，video-视频，normal-图文
     */
    private String type;

    private XhsNoteVideo video;


    private List<XhsNoteImage> imageList = new ArrayList<>();

    private List<XhsNoteTag> tagList = new ArrayList<>();
}
