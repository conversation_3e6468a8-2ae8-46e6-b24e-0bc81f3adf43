package org.yixz.entity.vo;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import java.util.ArrayList;
import java.util.List;

/**
 * 小红书作品类
 */
@Data
@Schema(description = "获取链接文案vo")
public class LinkNoteVo {

    @Schema(description = "作品id")
    private String noteId;

    @Schema(description = "标题")
    private String title;

    @Schema(description = "内容")
    private String content;

    //@JsonRawValue
    @Schema(description = "原始html")
    private String html;

    @Schema(description = "链接类型")
    private String linkType;


    @Schema(description = "图片")
    private List<String> imageList = new ArrayList<>();

    @Schema(description = "视频")
    private List<String> videoList = new ArrayList<>();
}
