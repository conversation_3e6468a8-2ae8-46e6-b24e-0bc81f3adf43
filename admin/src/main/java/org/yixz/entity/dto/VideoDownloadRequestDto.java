package org.yixz.entity.dto;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import jakarta.validation.constraints.NotBlank;

/**
 * 视频下载请求DTO
 *
 * <AUTHOR>
 * @date 2025-07-26
 */
@Data
@Schema(description = "视频下载请求参数")
public class VideoDownloadRequestDto {
    
    @NotBlank(message = "视频URL不能为空")
    @Schema(description = "视频URL", required = true, example = "https://www.douyin.com/video/7123456789")
    private String url;
    
    @Schema(description = "指定平台类型，不指定时自动识别", example = "douyin")
    private String platform;
    
    @Schema(description = "视频质量偏好", example = "best", defaultValue = "best")
    private String quality = "best";
    
    @Schema(description = "是否需要音频", defaultValue = "false")
    private Boolean audio = false;

    @Schema(description = "是否需要字幕", defaultValue = "false")
    private Boolean captions = false;
    
    @Schema(description = "输出格式", example = "mp4")
    private String format;
    
    @Schema(description = "是否强制使用yt-dlp下载器", defaultValue = "false")
    private Boolean forceYtDlp = false;
}
