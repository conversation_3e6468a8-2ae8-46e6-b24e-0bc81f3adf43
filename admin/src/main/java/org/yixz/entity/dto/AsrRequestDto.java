package org.yixz.entity.dto;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import org.springframework.web.multipart.MultipartFile;

import jakarta.validation.constraints.NotNull;

/**
 * 语音识别请求DTO
 *
 * <AUTHOR>
 * @date 2025-07-20
 */
@Data
@Schema(description = "语音识别请求参数")
public class AsrRequestDto {
    
    @NotNull(message = "音频文件不能为空")
    @Schema(description = "音频文件", required = true)
    private MultipartFile audioFile;
    

}
