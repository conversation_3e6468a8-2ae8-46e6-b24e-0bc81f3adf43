package org.yixz.entity.dto;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import org.springframework.web.multipart.MultipartFile;

import jakarta.validation.constraints.NotNull;

/**
 * 图片文字识别请求DTO
 *
 * <AUTHOR>
 * @date 2025-07-20
 */
@Data
@Schema(description = "图片文字识别请求参数")
public class OcrRequestDto {
    
    @NotNull(message = "图片文件不能为空")
    @Schema(description = "图片文件", required = true)
    private MultipartFile imageFile;
    
    @Schema(description = "语言类型", example = "zh", defaultValue = "zh")
    private String language = "zh";
}
