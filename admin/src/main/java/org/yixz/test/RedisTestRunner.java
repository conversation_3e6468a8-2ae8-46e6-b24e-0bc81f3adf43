package org.yixz.test;

import org.springframework.boot.CommandLineRunner;
import org.springframework.stereotype.Component;
import org.yixz.common.util.RedisUtil;

/**
 * Redis测试运行器
 * 用于验证字符串序列化是否正常
 */
@Component
public class RedisTestRunner implements CommandLineRunner {

    @Override
    public void run(String... args) throws Exception {
        // 只在测试模式下运行
        if (args.length > 0 && "test-redis".equals(args[0])) {
            testRedisStringStorage();
        }
    }

    private void testRedisStringStorage() {
        System.out.println("=== Redis字符串存储测试 ===");
        
        String testKey = "test:string:verification";
        String testValue = "Hello Redis 测试字符串 123";
        
        try {
            // 保存字符串
            RedisUtil.setCacheObject(testKey, testValue);
            System.out.println("✓ 字符串已保存到Redis");
            
            // 读取字符串
            String retrievedValue = RedisUtil.getCacheObject(testKey);
            
            System.out.println("原始值: " + testValue);
            System.out.println("读取值: " + retrievedValue);
            System.out.println("类型: " + (retrievedValue != null ? retrievedValue.getClass().getSimpleName() : "null"));
            
            if (testValue.equals(retrievedValue)) {
                System.out.println("✓ 测试成功：字符串正确存储和读取");
            } else {
                System.out.println("✗ 测试失败：字符串不匹配");
            }
            
            // 清理测试数据
            RedisUtil.deleteObject(testKey);
            System.out.println("✓ 测试数据已清理");
            
        } catch (Exception e) {
            System.out.println("✗ 测试出错: " + e.getMessage());
            e.printStackTrace();
        }
        
        System.out.println("=== 测试完成 ===");
    }
}
