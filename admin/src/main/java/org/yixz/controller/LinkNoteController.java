package org.yixz.controller;

import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;
import org.yixz.entity.dto.LinkNoteDto;
import org.yixz.entity.vo.LinkNoteVo;
import org.yixz.service.LinkNoteService;

@Slf4j
@Tag(name = "获取链接中的文案")
@RestController
@RequestMapping("/LinkNote")
public class LinkNoteController {
    @Autowired
    private LinkNoteService linkNoteService;

    @Operation(summary = "获取文案")
    @PostMapping("/getNote")
    public LinkNoteVo getNote(@RequestBody LinkNoteDto dto) {
        return linkNoteService.getNote(dto.getLink());
    }
}
