package org.yixz.controller;

import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.tags.Tag;
import jakarta.validation.constraints.NotNull;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.MediaType;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;
import org.springframework.web.multipart.MultipartFile;
import org.yixz.common.response.ResponseResult;
import org.yixz.entity.dto.AsrRequestDto;
import org.yixz.entity.dto.OcrRequestDto;
import org.yixz.entity.vo.AsrResponseVo;
import org.yixz.entity.vo.OcrResponseVo;
import org.yixz.factory.AsrServiceFactory;
import org.yixz.factory.OcrServiceFactory;
import org.yixz.service.ProviderService;

import java.util.Optional;

/**
 * AI服务控制器
 *
 * @date 2025-07-20
 */
@Slf4j
@Tag(name = "AI服务", description = "语音识别和图片文字识别服务")
@RestController
@RequestMapping("/ai")
@Validated
public class AiController {
    
    @Autowired
    private AsrServiceFactory asrServiceFactory;
    
    @Autowired
    private OcrServiceFactory ocrServiceFactory;
    
    @Operation(summary = "语音转文字", description = "上传音频文件进行语音识别")
    @PostMapping(value = "/asr", consumes = MediaType.MULTIPART_FORM_DATA_VALUE)
    public ResponseResult speechToText(
            @Parameter(description = "音频文件", required = true)
            @RequestParam("audioFile") @NotNull MultipartFile audioFile,
            
            @Parameter(description = "服务提供商", example = "aliyun")
            @RequestParam(value = "provider", required = false) String provider) {
        
        try {
            // 验证文件
            validateAudioFile(audioFile);
            
            // 构建请求对象
            AsrRequestDto request = new AsrRequestDto();
            request.setAudioFile(audioFile);

            // 获取服务实例
            ProviderService providerService = asrServiceFactory.getAsrService(Optional.ofNullable(provider));
            
            // 执行识别
            AsrResponseVo response = providerService.speechToText(request);

            log.info("语音识别成功 - 提供商: {}, 文件: {}, 结果长度: {}, 处理时间: {}ms{}",
                    response.getProvider(),
                    audioFile.getOriginalFilename(),
                    response.getText() != null ? response.getText().length() : 0,
                    response.getProcessTime(),
                    response.getConverterUsed() != null ? ", 转换器: " + response.getConverterUsed() : "");

            return ResponseResult.success(response);

        } catch (Exception e) {
            log.error("语音识别失败: {}", e.getMessage());
            return ResponseResult.error("ERROR", "语音识别失败: " + e.getMessage());
        }
    }
    
    @Operation(summary = "图片转文字", description = "上传图片文件进行文字识别")
    @PostMapping(value = "/ocr", consumes = MediaType.MULTIPART_FORM_DATA_VALUE)
    public ResponseResult imageToText(
            @Parameter(description = "图片文件", required = true)
            @RequestParam("imageFile") @NotNull MultipartFile imageFile,

            @Parameter(description = "服务提供商", example = "aliyun")
            @RequestParam(value = "provider", required = false) String provider) {
        
        try {
            // 验证文件
            validateImageFile(imageFile);
            
            // 构建请求对象
            OcrRequestDto request = new OcrRequestDto();
            request.setImageFile(imageFile);
            
            // 获取服务实例
            ProviderService ocrService = ocrServiceFactory.getOcrService(Optional.ofNullable(provider));
            
            // 执行识别
            OcrResponseVo response = ocrService.imageToText(request);

            log.info("图片文字识别成功 - 提供商: {}, 文件: {}, 结果长度: {}, 处理时间: {}ms",
                    response.getProvider(),
                    imageFile.getOriginalFilename(),
                    response.getText() != null ? response.getText().length() : 0,
                    response.getProcessTime());

            return ResponseResult.success(response);

        } catch (Exception e) {
            log.error("图片文字识别失败: {}", e.getMessage());
            return ResponseResult.error("ERROR", "图片文字识别失败: " + e.getMessage());
        }
    }
    
    @Operation(summary = "获取服务状态", description = "检查AI服务的可用性")
    @GetMapping("/status")
    public ResponseResult getServiceStatus() {
        try {
            ProviderService providerService = asrServiceFactory.getAsrService();
            ProviderService ocrService = ocrServiceFactory.getOcrService();
            
            return ResponseResult.success(new Object() {
                public final String asrProvider = providerService.getProviderName();
                public final boolean asrAvailable = providerService.isAvailable();
                public final String ocrProvider = ocrService.getProviderName();
                public final boolean ocrAvailable = ocrService.isAvailable();
            });

        } catch (Exception e) {
            log.error("获取服务状态失败", e);
            return ResponseResult.error("ERROR", "获取服务状态失败: " + e.getMessage());
        }
    }
    
    private void validateAudioFile(MultipartFile file) {
        if (file.isEmpty()) {
            throw new IllegalArgumentException("音频文件不能为空");
        }
        
        if (file.getSize() > 50 * 1024 * 1024) { // 50MB限制
            throw new IllegalArgumentException("音频文件大小不能超过50MB");
        }
    }
    
    private void validateImageFile(MultipartFile file) {
        if (file.isEmpty()) {
            throw new IllegalArgumentException("图片文件不能为空");
        }
        
        if (file.getSize() > 10 * 1024 * 1024) { // 10MB限制
            throw new IllegalArgumentException("图片文件大小不能超过10MB");
        }
        
        String originalFilename = file.getOriginalFilename();
        if (originalFilename == null) {
            throw new IllegalArgumentException("无法获取文件名");
        }
    }
}
