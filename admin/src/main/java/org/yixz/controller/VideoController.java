package org.yixz.controller;

import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.tags.Tag;
import jakarta.validation.Valid;
import jakarta.validation.constraints.NotBlank;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;
import org.yixz.common.response.ResponseResult;
import org.yixz.entity.dto.VideoDownloadRequestDto;
import org.yixz.entity.vo.VideoDownloadResponseVo;
import org.yixz.factory.VideoDownloadFactory;
import org.yixz.service.VideoDownloadService;

import java.util.Optional;

/**
 * 视频下载控制器
 *
 * <AUTHOR>
 * @date 2025-07-26
 */
@Slf4j
@Tag(name = "视频下载服务", description = "支持多平台视频下载，包括抖音、快手、B站等")
@RestController
@RequestMapping("/video")
@Validated
public class VideoController {

    @Autowired
    private VideoDownloadFactory videoDownloadFactory;

    @Operation(summary = "下载视频", description = "根据URL下载视频，支持自动平台识别")
    @PostMapping("/download")
    public ResponseResult downloadVideo(@Valid @RequestBody VideoDownloadRequestDto request) {
        try {
            log.info("开始下载视频: {}", request.getUrl());

            // 获取下载服务
            VideoDownloadService downloadService = videoDownloadFactory.getVideoDownloadService(
                request.getUrl(),
                Optional.ofNullable(request.getPlatform()),
                request.getForceYtDlp() != null && request.getForceYtDlp()
            );

            // 执行下载
            VideoDownloadResponseVo response = downloadService.downloadVideo(request);

            log.info("视频下载完成 - URL: {}, 平台: {}, 下载器: {}, 耗时: {}ms",
                    request.getUrl(),
                    response.getPlatform(),
                    response.getDownloader(),
                    response.getProcessTime());

            return ResponseResult.success(response);

        } catch (Exception e) {
            log.error("视频下载失败: {}", e.getMessage(), e);
            return ResponseResult.error("ERROR", "视频下载失败: " + e.getMessage());
        }
    }

    @Operation(summary = "获取视频信息", description = "获取视频信息而不下载")
    @PostMapping("/info")
    public ResponseResult getVideoInfo(@Valid @RequestBody VideoDownloadRequestDto request) {
        try {
            log.info("获取视频信息: {}", request.getUrl());

            // 获取下载服务
            VideoDownloadService downloadService = videoDownloadFactory.getVideoDownloadService(
                request.getUrl(),
                Optional.ofNullable(request.getPlatform()),
                request.getForceYtDlp() != null && request.getForceYtDlp()
            );

            // 获取视频信息
            VideoDownloadResponseVo response = downloadService.getVideoInfo(request);

            log.info("视频信息获取完成 - URL: {}, 平台: {}, 下载器: {}",
                    request.getUrl(),
                    response.getPlatform(),
                    response.getDownloader());

            return ResponseResult.success(response);

        } catch (Exception e) {
            log.error("获取视频信息失败: {}", e.getMessage(), e);
            return ResponseResult.error("ERROR", "获取视频信息失败: " + e.getMessage());
        }
    }

    @Operation(summary = "通过URL参数下载视频", description = "简化的下载接口，通过URL参数传递")
    @GetMapping("/download")
    public ResponseResult downloadVideoByUrl(
            @Parameter(description = "视频URL", required = true)
            @RequestParam("url") @NotBlank String url,

            @Parameter(description = "平台类型", example = "douyin")
            @RequestParam(value = "platform", required = false) String platform,

            @Parameter(description = "是否强制使用yt-dlp", example = "false")
            @RequestParam(value = "forceYtDlp", required = false, defaultValue = "false") Boolean forceYtDlp) {

        try {
            // 构建请求对象
            VideoDownloadRequestDto request = new VideoDownloadRequestDto();
            request.setUrl(url);
            request.setPlatform(platform);
            request.setForceYtDlp(forceYtDlp);

            return downloadVideo(request);

        } catch (Exception e) {
            log.error("视频下载失败: {}", e.getMessage(), e);
            return ResponseResult.error("ERROR", "视频下载失败: " + e.getMessage());
        }
    }

    @Operation(summary = "获取服务状态", description = "检查视频下载服务的可用性")
    @GetMapping("/status")
    public ResponseResult getServiceStatus() {
        try {
            return ResponseResult.success(new Object() {
                public final boolean ytDlpAvailable = videoDownloadFactory.isDownloaderAvailable("yt-dlp");
                public final boolean douyinAvailable = videoDownloadFactory.isDownloaderAvailable("douyin");
                public final boolean kuaishouAvailable = videoDownloadFactory.isDownloaderAvailable("kuaishou");
                public final boolean bilibiliAvailable = videoDownloadFactory.isDownloaderAvailable("bilibili");
                public final String[] supportedPlatforms = {"douyin", "kuaishou", "bilibili", "xiaohongshu", "weibo", "youtube", "tiktok", "yt-dlp"};
            });

        } catch (Exception e) {
            log.error("获取服务状态失败", e);
            return ResponseResult.error("ERROR", "获取服务状态失败: " + e.getMessage());
        }
    }
}
