package org.yixz.controller;

import com.alibaba.fastjson.JSONObject;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.Parameters;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import org.yixz.entity.vo.TokenVO;
import org.yixz.service.WechatService;


@Slf4j
@RestController("WeChatController")
@RequestMapping("/wechat")
@Tag(name = "微信")
public class WeChatController {

    @Autowired
    private WechatService wechatService;


    /**
     * 微信登录小程序授权登录
     */
    @Operation(summary = "微信登录小程序授权登录")
    @PostMapping(value = "/authorize/program/login")
    @Parameters({@Parameter(name = "code", description = "微信code", required = true),
            @Parameter(name = "shareId",description = "邀请人的用户ID，有则传，没有则不用传", required = true),})
    public TokenVO programLogin(@RequestBody JSONObject code){
        return wechatService.weChatAuthorizeProgramLogin(code);
    }




}



