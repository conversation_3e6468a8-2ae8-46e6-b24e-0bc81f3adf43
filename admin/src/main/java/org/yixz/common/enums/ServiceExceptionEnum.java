package org.yixz.common.enums;

/**
 * <AUTHOR>
 * @Date 2023/8/19 19:47
 * @Description
 **/
public enum ServiceExceptionEnum {
    SUCESS("200", "操作成功"),
    FIAL("200500", "操作失败"),
    MISSING_PARAM_ERROR("200001", "参数缺失"),
    SYS_ACCOUNT_NOT_EXIST("200101", "账号不存在"),
    SYS_ACCOUNT_IS_EXIST("200102", "账号已存在"),
    SYS_ROLE_NOT_EXIST("200103", "角色不存在"),
    SYS_PASSWORD_ERROR("200104", "密码错误"),
    SYS_UNKNOWN_ERROR("200105", "未知错误"),
    SYS_UNAUTHORIZED("200106", "请登录"),
    SYS_FORBIDDEN("200107", "权限不足"),
    SYS_PARAMETER_ERROR("200108", "参数不合法"),
    SYS_USER_NOT_ENABLED("200121", "用户状态为禁用！"),
    ;

    ServiceExceptionEnum(String code, String message) {
        this.code = code;
        this.message = message;
    }

    public String code;

    public String message;

}
