package org.yixz.common.enums;

import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * AI服务提供商枚举
 *
 * <AUTHOR>
 * @date 2025-07-20
 */
@Getter
@AllArgsConstructor
public enum AiProviderEnum {
    
    ALIYUN("aliyun", "阿里云"),
    BAIDU("baidu", "百度云"),
    TENCENT("tencent", "腾讯云"),
    HUAWEI("huawei", "华为云");
    
    private final String code;
    private final String name;
    
    /**
     * 根据代码获取枚举
     */
    public static AiProviderEnum getByCode(String code) {
        for (AiProviderEnum provider : values()) {
            if (provider.getCode().equals(code)) {
                return provider;
            }
        }
        throw new IllegalArgumentException("不支持的AI服务提供商: " + code);
    }
}
