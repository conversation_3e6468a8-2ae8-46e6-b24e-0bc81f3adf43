package org.yixz.common.enums;

import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * 视频平台枚举
 *
 * <AUTHOR>
 * @date 2025-07-26
 */
@Getter
@AllArgsConstructor
public enum VideoPlatformEnum {
    
    DOUYIN("douyin", "抖音", "douyin\\.com|iesdouyin\\.com"),
    KUAISHOU("kuaishou", "快手", "kuaishou\\.com|chenzhongtech\\.com"),
    BILIBILI("bilibili", "哔哩哔哩", "bilibili\\.com|b23\\.tv"),
    XIAOHONGSHU("xiaohongshu", "小红书", "xiaohongshu\\.com|xhslink\\.com"),
    WEIBO("weibo", "微博", "weibo\\.com|weibo\\.cn"),
    YOUTUBE("youtube", "YouTube", "youtube\\.com|youtu\\.be"),
    TIKTOK("tiktok", "TikTok", "tiktok\\.com"),
    YT_DLP("yt-dlp", "通用下载器", ".*"); // 通用匹配，优先级最低
    
    private final String code;
    private final String name;
    private final String urlPattern;
    
    /**
     * 根据代码获取枚举
     */
    public static VideoPlatformEnum getByCode(String code) {
        for (VideoPlatformEnum platform : values()) {
            if (platform.getCode().equals(code)) {
                return platform;
            }
        }
        throw new IllegalArgumentException("不支持的视频平台: " + code);
    }
    
    /**
     * 根据URL检测平台
     */
    public static VideoPlatformEnum detectByUrl(String url) {
        if (url == null || url.trim().isEmpty()) {
            return YT_DLP; // 默认使用通用下载器
        }
        
        String lowerUrl = url.toLowerCase();
        
        // 按优先级检测，排除通用下载器
        for (VideoPlatformEnum platform : values()) {
            if (platform != YT_DLP && lowerUrl.matches(".*(" + platform.getUrlPattern() + ").*")) {
                return platform;
            }
        }
        
        return YT_DLP; // 未匹配到特定平台时使用通用下载器
    }
}
