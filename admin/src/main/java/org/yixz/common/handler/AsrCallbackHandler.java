package org.yixz.common.handler;

import com.alibaba.nls.client.protocol.asr.SpeechRecognizerListener;
import com.alibaba.nls.client.protocol.asr.SpeechRecognizerResponse;
import com.alibaba.nls.client.protocol.asr.SpeechTranscriberListener;
import com.alibaba.nls.client.protocol.asr.SpeechTranscriberResponse;
import lombok.extern.slf4j.Slf4j;

import java.util.concurrent.CountDownLatch;
import java.util.concurrent.TimeUnit;
import java.util.concurrent.atomic.AtomicReference;

/**
 * ASR异步回调处理工具类
 * 统一处理语音识别的异步回调逻辑
 *
 * <AUTHOR>
 * @date 2025-07-21
 */
@Slf4j
public class AsrCallbackHandler {

    /**
     * 一句话识别结果处理器
     */
    public static class RecognizerResultHandler {
        private final AtomicReference<String> result = new AtomicReference<>();
        private final AtomicReference<Exception> error = new AtomicReference<>();
        private final CountDownLatch latch = new CountDownLatch(1);
        private final String userParam;
        private final int order;

        public RecognizerResultHandler(String userParam, int order) {
            this.userParam = userParam;
            this.order = order;
        }

        public SpeechRecognizerListener createListener() {
            return new SpeechRecognizerListener() {
                @Override
                public void onRecognitionResultChanged(SpeechRecognizerResponse response) {
                    log.debug("识别中间结果 - task_id: {}, status: {}, result: {}", 
                            response.getTaskId(), response.getStatus(), response.getRecognizedText());
                }

                @Override
                public void onRecognitionCompleted(SpeechRecognizerResponse response) {
                    log.info("识别完成 - task_id: {}, status: {}, result: {}", 
                            response.getTaskId(), response.getStatus(), response.getRecognizedText());
                    result.set(response.getRecognizedText());
                    latch.countDown();
                }

                @Override
                public void onStarted(SpeechRecognizerResponse response) {
                    log.info("识别开始 - order: {}, userParam: {}, task_id: {}", 
                            order, userParam, response.getTaskId());
                }

                @Override
                public void onFail(SpeechRecognizerResponse response) {
                    log.error("识别失败 - task_id: {}, status: {}, status_text: {}", 
                            response.getTaskId(), response.getStatus(), response.getStatusText());
                    error.set(new RuntimeException("语音识别失败: " + response.getStatusText()));
                    latch.countDown();
                }
            };
        }

        public String waitForResult(long timeoutMs) throws InterruptedException {
            if (latch.await(timeoutMs, TimeUnit.MILLISECONDS)) {
                Exception ex = error.get();
                if (ex != null) {
                    throw new RuntimeException(ex);
                }
                return result.get();
            } else {
                throw new RuntimeException("语音识别超时，等待时间: " + timeoutMs + "ms");
            }
        }
    }

    /**
     * 实时语音识别结果处理器
     */
    public static class TranscriberResultHandler {
        private final StringBuilder result = new StringBuilder();
        private final AtomicReference<Exception> error = new AtomicReference<>();
        private final CountDownLatch latch = new CountDownLatch(1);

        public SpeechTranscriberListener createListener() {
            return new SpeechTranscriberListener() {
                @Override
                public void onTranscriptionResultChange(SpeechTranscriberResponse response) {
                    log.debug("转录中间结果 - task_id: {}, index: {}, result: {}, time: {}",
                            response.getTaskId(),
                            response.getTransSentenceIndex(),
                            response.getTransSentenceText(),
                            response.getTransSentenceTime());
                }

                @Override
                public void onTranscriberStart(SpeechTranscriberResponse response) {
                    log.info("转录开始 - task_id: {}, status: {}", 
                            response.getTaskId(), response.getStatus());
                }

                @Override
                public void onSentenceBegin(SpeechTranscriberResponse response) {
                    log.debug("句子开始 - task_id: {}", response.getTaskId());
                }

                @Override
                public void onSentenceEnd(SpeechTranscriberResponse response) {
                    log.info("句子结束 - task_id: {}, index: {}, result: {}, confidence: {}, time: {}",
                            response.getTaskId(),
                            response.getTransSentenceIndex(),
                            response.getTransSentenceText(),
                            response.getConfidence(),
                            response.getTransSentenceTime());
                    
                    synchronized (result) {
                        result.append(response.getTransSentenceText());
                    }
                }

                @Override
                public void onTranscriptionComplete(SpeechTranscriberResponse response) {
                    log.info("转录完成 - task_id: {}, status: {}", 
                            response.getTaskId(), response.getStatus());
                    latch.countDown();
                }

                @Override
                public void onFail(SpeechTranscriberResponse response) {
                    log.error("转录失败 - task_id: {}, status: {}, status_text: {}", 
                            response.getTaskId(), response.getStatus(), response.getStatusText());
                    error.set(new RuntimeException("语音转录失败: " + response.getStatusText()));
                    latch.countDown();
                }
            };
        }

        public String waitForResult(long timeoutMs) throws InterruptedException {
            if (latch.await(timeoutMs, TimeUnit.MILLISECONDS)) {
                Exception ex = error.get();
                if (ex != null) {
                    throw new RuntimeException(ex);
                }
                synchronized (result) {
                    return result.toString();
                }
            } else {
                throw new RuntimeException("语音转录超时，等待时间: " + timeoutMs + "ms");
            }
        }
    }

    /**
     * 根据二进制数据大小计算对应的同等语音长度
     * sampleRate仅支持8000或16000
     */
    public static int calculateSleepDelta(int dataSize, int sampleRate) {
        // 仅支持16位采样，单通道
        return (dataSize * 10 * 8000) / (160 * sampleRate);
    }
}
