package org.yixz.common.handler;

import jakarta.validation.ConstraintViolationException;
import org.apache.catalina.connector.ClientAbortException;
import org.springframework.validation.BindException;
import org.springframework.web.bind.MethodArgumentNotValidException;
import org.springframework.web.context.request.async.AsyncRequestNotUsableException;
import org.yixz.common.exception.AudioConversionException;
import org.yixz.common.exception.BizException;
import org.yixz.common.response.ResponseCode;
import org.yixz.common.response.ResponseResult;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.ExceptionHandler;
import org.springframework.web.bind.annotation.RestControllerAdvice;

/**
 * 全局异常处理
 *
 * <AUTHOR>
 * @date 2021年07月22日 18:37
 */
@Slf4j
@RestControllerAdvice
public class GlobalExceptionHandler {
    /**
     * 处理音频转换异常
     * @param e
     * @return
     */
    @ExceptionHandler(value = AudioConversionException.class)
    public ResponseResult audioConversionExceptionHandler(AudioConversionException e){
        log.error("音频转换失败: {}", e);
        return ResponseResult.error(ResponseCode.BUSINESS_ERROR.code, "音频格式不支持或转换失败: " + e);
    }

    /**
     * 处理自定义的业务异常
     * @param e
     * @return
     */
    @ExceptionHandler(value = BizException.class)
    public ResponseResult bizExceptionHandler(Exception e){
        log.error("业务异常:", e);
        return ResponseResult.error(ResponseCode.BUSINESS_ERROR.code, e.getMessage());
    }

    /**
     * 处理客户端连接断开异常（Broken pipe等）
     * @param e
     * @return
     */
    @ExceptionHandler(value = {ClientAbortException.class, AsyncRequestNotUsableException.class})
    public ResponseResult clientAbortExceptionHandler(Exception e){
        // 客户端断开连接是正常现象，只记录简要日志
        log.warn("客户端连接断开: {}", e);
        // 由于客户端已断开，返回值实际上不会被发送
        return ResponseResult.error(ResponseCode.ERROR.code, "客户端连接断开");
    }

    /**
     * 参数异常 -- ConstraintViolationException()
     * 用于处理类似http://localhost:8080/user/getUser?age=30&name=yoyo请求中age和name的校验引发的异常
     * @param e
     * @return
     */
    @ExceptionHandler(value = ConstraintViolationException.class)
    public ResponseResult urlParametersExceptionHandle(Exception e){
        log.error("请求参数异常: {}", e);
        return ResponseResult.error(ResponseCode.ILLEGAL_ARGUMENT.code, ResponseCode.ILLEGAL_ARGUMENT.msg);
    }

    /***
     * 参数异常 --- MethodArgumentNotValidException和BindException
     * MethodArgumentNotValidException --- 用于处理请求参数为实体类时校验引发的异常 --- Content-Type为application/json
     * BindException --- 用于处理请求参数为实体类时校验引发的异常  --- Content-Type为application/x-www-form-urlencoded
     * @param e
     * @return
     */
    @ExceptionHandler(value = {MethodArgumentNotValidException.class, BindException.class})
    public ResponseResult bodyExceptionHandle(Exception e) {
        log.error("请求参数异常: {}", e);
        return ResponseResult.error(ResponseCode.ILLEGAL_ARGUMENT.code, ResponseCode.ILLEGAL_ARGUMENT.msg);
    }

    /**
     * 处理其他异常
     * @param e
     * @return
     */
    @ExceptionHandler(value = Exception.class)
    public ResponseResult exceptionHandler(Exception e){
        // 检查是否是网络连接相关异常
        if (isNetworkException(e)) {
            log.warn("网络连接异常:", e);
            return ResponseResult.error(ResponseCode.ERROR.code, "网络连接异常");
        }

        log.error("操作失败:", e);
        return ResponseResult.error(ResponseCode.ERROR.code, ResponseCode.ERROR.msg);
    }

    /**
     * 判断是否为网络连接异常
     */
    private boolean isNetworkException(Exception e) {
        String message = e.getMessage();
        if (message != null) {
            String lowerMessage = message.toLowerCase();
            return lowerMessage.contains("broken pipe") ||
                   lowerMessage.contains("connection reset") ||
                   lowerMessage.contains("connection aborted") ||
                   lowerMessage.contains("client abort") ||
                   lowerMessage.contains("async request not usable");
        }

        // 检查异常类型
        Throwable cause = e;
        while (cause != null) {
            if (cause instanceof ClientAbortException ||
                cause instanceof AsyncRequestNotUsableException ||
                cause.getClass().getSimpleName().contains("ClientAbort")) {
                return true;
            }
            cause = cause.getCause();
        }

        return false;
    }
}
