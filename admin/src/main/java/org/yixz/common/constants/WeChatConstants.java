package org.yixz.common.constants;


public class WeChatConstants {

    //-------------------------------------------微信系统配置------------------------------------------------------------
    /** 小程序appId key */
//    public static final String WECHAT_MINI_APPID = "routine_appid";
    public static final String WECHAT_MINI_APPID = "wx8b11119a3ca";
    /** 小程序appSecret key */
//    public static final String WECHAT_MINI_APPSECRET = "routine_appsecret";
    public static final String WECHAT_MINI_APPSECRET = "1d7e4c71111e0b25be8207f5cf";

    /** 公众号appId key */
    public static final String WECHAT_PUBLIC_APPID = "wx22b461111f4258";
    /** 公众号appSecret key */
    public static final String WECHAT_PUBLIC_APPSECRET = "dbad072dd1111a3d4a8ad002fd";
    /** 公众号服务器对接token */
    public static final String WECHAT_PUBLIC_SERVER_TOKEN = "lvp111s4iq";


    /** 获取accessToken的url */
    public static final String WECHAT_ACCESS_TOKEN_URL = "https://api.weixin.qq.com/cgi-bin/token?grant_type=client_credential&appid={}&secret={}";

    /** 小程序登录凭证校验的url */
    public static final String WECHAT_MINI_SNS_AUTH_CODE2SESSION_URL = "https://api.weixin.qq.com/sns/jscode2session?appid={}&secret={}&js_code={}&grant_type=authorization_code";
    /** 小程序获取用户手机号的url */
    public static final String WECHAT_MINI_GET_USER_PHONE_NUMBER_URL = "https://api.weixin.qq.com/wxa/business/getuserphonenumber?access_token={}";

    /** 小程序生成小程序码的url */
    public static final String WECHAT_MINI_QRCODE_UNLIMITED_URL = "https://api.weixin.qq.com/wxa/getwxacodeunlimit?access_token={}";
    /** 小程序发送订阅消息的url */
    public static final String WECHAT_MINI_SEND_SUBSCRIBE_URL = "https://api.weixin.qq.com/cgi-bin/message/subscribe/send?access_token={}";
    /** 小程序获取订阅列表（自己的） */
    public static final String WECHAT_MINI_GET_ALL_PRIVATE_TEMPLATE_URL = "https://api.weixin.qq.com/wxaapi/newtmpl/gettemplate?access_token={}";
    /** 小程序删除模板（自己的） */
    public static final String WECHAT_MINI_DEL_PRIVATE_TEMPLATE_URL = "https://api.weixin.qq.com/wxaapi/newtmpl/deltemplate?access_token={}";
    /** 小程序获取订阅模板（小程序的） */
    public static final String WECHAT_MINI_GET_TEMPLATE_URL = "https://api.weixin.qq.com/wxaapi/newtmpl/getpubtemplatekeywords?access_token={}&tid={}";
    /** 公众号添加模板（自己的） */
    public static final String WECHAT_MINI_API_ADD_TEMPLATE_URL = "https://api.weixin.qq.com/wxaapi/newtmpl/addtemplate?access_token={}";
    /** 公众号发送模板消息的url */
    public static final String WECHAT_PUBLIC_SEND_TEMPLATE_URL = "https://api.weixin.qq.com/cgi-bin/message/template/send?access_token={}";
    /** 公众号获取用户列表的url */
    public static final String WECHAT_PUBLIC_USER_GET_URL = "https://api.weixin.qq.com/cgi-bin/user/get?access_token={}&next_openid={}";
    /** 公众号获取用户信息的url */
    public static final String WECHAT_PUBLIC_USER_INFO_URL = "https://api.weixin.qq.com/cgi-bin/user/info?access_token={}&openid={}&lang=zh_CN";
    /** 公众号发送客服消息的url */
    public static final String WECHAT_PUBLIC_SEND_CUSTOM_URL = "https://api.weixin.qq.com/cgi-bin/message/custom/send?access_token={}";



    /** 小程序accessToken redis key */
    public static final String REDIS_WECAHT_MINI_ACCESS_TOKEN_KEY = "wechat_mini_accessToken";

    /** 公众号accessToken redis key */
    public static final String REDIS_WECAHT_PUBLIC_ACCESS_TOKEN_KEY = "wechat_public_accessToken";
    /** 公众号JsApiTicket redis key */
    public static final String REDIS_PUBLIC_JS_API_TICKET = "wechat_js_api_ticket";
    public static final Long REDIS_PUBLIC_JS_API_TICKET_EXPRESS = 7100L;

    // 订阅消息模板ID
    /**
     * 红包通知
     */
    public static final Integer WECHAT_TEMPLATE_REDPACKET = 1;
    /**
     * 邀请结果
     */
    public static final Integer WECHAT_TEMPLATE_INVITE = 2;

    /**
     * 公众号的模板ID
     */
    public static final Integer WECHAT_TEMPLATE_PUBLIC = 3;

    // 公众号推送小程序卡片的缩略图媒体ID
    public static final String WECHAT_PUBLIC_PROGRAM_MEDIA_ID = "OzYShizo1HZLI519611111Vb_nH7ubxspS-TDB";
    // 公众号推送小程序卡片的烟花缩略图媒体ID
    public static final String WECHAT_PUBLIC_PROGRAM_FIREWORK_MEDIA_ID = "OzYShizo1HZLI5196b11111D_M9Ynl_K57L6KYg_ddCcN4";


    //消息模板队列key
    public static final String WE_CHAT_MESSAGE_KEY_PUBLIC = "we_chat_public_message_list";
    public static final String WE_CHAT_CUSTOM_KEY_PUBLIC = "we_chat_public_custom_list";
    public static final String WE_CHAT_MESSAGE_KEY_PROGRAM = "we_chat_program_message_list";



}
