package org.yixz.common.config;

import org.springframework.context.annotation.Configuration;
import org.springframework.web.servlet.config.annotation.WebMvcConfigurer;

/**
 * 全局配置
 * <AUTHOR>
 * @date 2021年07月22日 17:43
 */
@Configuration
public class GlobalMvcConfig implements WebMvcConfigurer {
    /*@Resource
    LoginInterceptor loginInterceptor;

    @Override
    public void addInterceptors(InterceptorRegistry registry) {
        //注册登录拦截器
        registry.addInterceptor(loginInterceptor).addPathPatterns("/**");
    }*/
}
