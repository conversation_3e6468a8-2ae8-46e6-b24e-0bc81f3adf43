package org.yixz.common.util;

import lombok.extern.slf4j.Slf4j;
import org.yixz.common.enums.VideoPlatformEnum;

import java.net.URL;

/**
 * 平台URL检测工具类
 *
 * <AUTHOR>
 * @date 2025-07-26
 */
@Slf4j
public class PlatformUrlDetector {
    
    /**
     * 检测URL对应的视频平台
     *
     * @param url 视频URL
     * @return 检测到的平台
     */
    public static VideoPlatformEnum detectPlatform(String url) {
        if (url == null || url.trim().isEmpty()) {
            log.warn("URL为空，使用默认下载器");
            return VideoPlatformEnum.YT_DLP;
        }
        
        try {
            // 标准化URL
            String normalizedUrl = normalizeUrl(url);
            
            // 使用枚举中的检测逻辑
            VideoPlatformEnum platform = VideoPlatformEnum.detectByUrl(normalizedUrl);
            
            log.debug("URL: {} 检测到平台: {}", url, platform.getName());
            return platform;
            
        } catch (Exception e) {
            log.error("URL检测失败: {}, 使用默认下载器", url, e);
            return VideoPlatformEnum.YT_DLP;
        }
    }
    
    /**
     * 标准化URL
     *
     * @param url 原始URL
     * @return 标准化后的URL
     */
    private static String normalizeUrl(String url) {
        try {
            // 如果URL不包含协议，添加https://
            if (!url.startsWith("http://") && !url.startsWith("https://")) {
                url = "https://" + url;
            }
            
            // 解析URL以验证格式
            URL parsedUrl = new URL(url);
            return parsedUrl.toString().toLowerCase();
            
        } catch (Exception e) {
            log.warn("URL格式化失败: {}, 使用原始URL", url);
            return url.toLowerCase();
        }
    }
    
    /**
     * 验证URL格式
     *
     * @param url 待验证的URL
     * @return 是否为有效URL
     */
    public static boolean isValidUrl(String url) {
        if (url == null || url.trim().isEmpty()) {
            return false;
        }
        
        try {
            String normalizedUrl = normalizeUrl(url);
            new URL(normalizedUrl);
            return true;
        } catch (Exception e) {
            log.debug("无效URL: {}", url);
            return false;
        }
    }
    
    /**
     * 提取短链接的真实URL（简单实现）
     *
     * @param shortUrl 短链接
     * @return 真实URL，如果提取失败返回原URL
     */
    public static String expandShortUrl(String shortUrl) {
        // 这里可以实现短链接展开逻辑
        // 目前返回原URL
        return shortUrl;
    }
}
