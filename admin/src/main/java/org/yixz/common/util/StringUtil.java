package org.yixz.common.util;

public class StringUtil {
    /**
     * 处理特殊字符串并返回安全的文件名
     *
     * @param name 原始文件名
     * @param aid 备用ID
     * @return 处理后的文件名
     */
    public static String getFileName(String name, String aid) {
        try {
            if (name == null || name.trim().isEmpty()) {
                return aid;
            }

            // 限制长度
            if (name.length() > 64) {
                name = name.substring(0, 64);
            }

            // 替换所有非中文、数字、字母为点号
            String result = name.replaceAll("[^A-Za-z0-9\\u4e00-\\u9fa5]+", ".");

            // 合并多个点号为一个
            result = result.replaceAll("\\.+", ".");



            // 去除首尾点号
            result = result.replaceAll("^\\.|\\.$", "");

            // 去除孤立的点号（即两个点号之间无字符的情况）
            result = result.replaceAll("(?<=\\.)\\.(?=\\.)", "");

            // 再次去除首尾点号，防止中间清理后新产生的
            result = result.replaceAll("^\\.|\\.$", "");

            result = result.replace(".", "");

            // 如果结果为空，使用备用ID
            if (result.isEmpty()) {
                return aid;
            }

            // 文件名不能以数字开头
            if (result.matches("^[0-9].*")) {
                result = "ep" + result;
            }

            return result;
        } catch (Exception e) {
            return aid;
        }
    }
}
