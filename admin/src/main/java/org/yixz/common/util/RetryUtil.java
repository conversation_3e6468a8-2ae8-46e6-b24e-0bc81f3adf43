package org.yixz.common.util;

import lombok.extern.slf4j.Slf4j;

import java.util.function.Supplier;

/**
 * 重试工具类
 * 提供通用的重试机制
 *
 * <AUTHOR>
 * @date 2025-07-21
 */
@Slf4j
public class RetryUtil {

    /**
     * 执行带重试的操作
     *
     * @param operation 要执行的操作
     * @param maxAttempts 最大重试次数
     * @param delayMs 重试间隔（毫秒）
     * @param operationName 操作名称（用于日志）
     * @return 操作结果
     */
    public static <T> T executeWithRetry(Supplier<T> operation, int maxAttempts, int delayMs, String operationName) {
        Exception lastException = null;
        
        for (int attempt = 1; attempt <= maxAttempts; attempt++) {
            try {
                log.debug("执行操作: {}, 第{}次尝试", operationName, attempt);
                T result = operation.get();
                if (attempt > 1) {
                    log.info("操作成功: {}, 重试{}次后成功", operationName, attempt - 1);
                }
                return result;
            } catch (Exception e) {
                lastException = e;
                log.warn("操作失败: {}, 第{}次尝试失败: {}", operationName, attempt, e.getMessage());
                
                if (attempt < maxAttempts) {
                    try {
                        Thread.sleep(delayMs);
                    } catch (InterruptedException ie) {
                        Thread.currentThread().interrupt();
                        throw new RuntimeException("重试被中断", ie);
                    }
                }
            }
        }
        
        log.error("操作最终失败: {}, 已重试{}次", operationName, maxAttempts);
        throw new RuntimeException("操作失败，已重试" + maxAttempts + "次: " + operationName, lastException);
    }

    /**
     * 执行带重试的操作（无返回值）
     */
    public static void executeWithRetry(Runnable operation, int maxAttempts, int delayMs, String operationName) {
        executeWithRetry(() -> {
            operation.run();
            return null;
        }, maxAttempts, delayMs, operationName);
    }

    /**
     * 判断异常是否可重试
     */
    public static boolean isRetryableException(Exception e) {
        // 音频转换异常不应重试
        if (e instanceof org.yixz.common.exception.AudioConversionException) {
            return false;
        }

        // 检查异常链中是否包含音频转换异常
        Throwable cause = e.getCause();
        while (cause != null) {
            if (cause instanceof org.yixz.common.exception.AudioConversionException) {
                return false;
            }
            cause = cause.getCause();
        }

        // 网络相关异常通常可以重试
        String message = e.getMessage();
        if (message != null) {
            String lowerMessage = message.toLowerCase();
            return lowerMessage.contains("timeout") ||
                   lowerMessage.contains("connection") ||
                   lowerMessage.contains("network") ||
                   lowerMessage.contains("socket") ||
                   lowerMessage.contains("503") ||
                   lowerMessage.contains("502") ||
                   lowerMessage.contains("500");
        }
        return false;
    }

    /**
     * 执行带条件重试的操作
     * 只有在满足重试条件时才重试
     */
    public static <T> T executeWithConditionalRetry(Supplier<T> operation, int maxAttempts, int delayMs, 
                                                   String operationName, java.util.function.Predicate<Exception> retryCondition) {
        Exception lastException = null;
        int attempt = 1;
        for (; attempt <= maxAttempts; attempt++) {
            try {
                log.debug("执行操作: {}, 第{}次尝试", operationName, attempt);
                T result = operation.get();
                if (attempt > 1) {
                    log.info("操作成功: {}, 重试{}次后成功", operationName, attempt - 1);
                }
                return result;
            } catch (Exception e) {
                lastException = e;
                log.warn("操作失败: {}, 第{}次尝试失败: {}", operationName, attempt, e.getMessage());
                
                // 检查是否满足重试条件
                if (attempt < maxAttempts && retryCondition.test(e)) {
                    try {
                        Thread.sleep(delayMs);
                    } catch (InterruptedException ie) {
                        Thread.currentThread().interrupt();
                        throw new RuntimeException("重试被中断", ie);
                    }
                } else {
                    break;
                }
            }
        }
        
        log.error("操作最终失败: {}, 已重试{}次", operationName, attempt);
        throw new RuntimeException("操作失败: " + operationName, lastException);
    }
}
