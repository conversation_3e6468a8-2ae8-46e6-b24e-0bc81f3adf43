package org.yixz.common.util;

import cn.hutool.core.util.ObjectUtil;
import jakarta.servlet.http.HttpServletRequest;
import org.apache.commons.lang3.ObjectUtils;
import org.springframework.stereotype.Component;
import org.springframework.util.StringUtils;
import org.springframework.web.context.request.RequestContextHolder;
import org.springframework.web.context.request.ServletRequestAttributes;
import org.yixz.common.constants.ErrorConstants;
import org.yixz.common.constants.RedisConstants;
import org.yixz.common.enums.ServiceExceptionEnum;
import org.yixz.common.exception.BizException;
import org.yixz.entity.vo.TokenUserMesVo;
import org.yixz.entity.vo.TokenVO;
import org.yixz.entity.vo.UserVO;

import java.time.Duration;
import java.util.Objects;
import java.util.UUID;

/**
 * Token工具类
 **/
@Component
public class TokenUtil {

    //Token解析
    public TokenUserMesVo analysisToken()
    {
        HttpServletRequest request = ((ServletRequestAttributes) Objects.requireNonNull(RequestContextHolder.getRequestAttributes())).getRequest();
        //获取token
        String token = request.getHeader("Token");
        if (!StringUtils.hasText(token)){
            throw new BizException(ServiceExceptionEnum.SYS_UNAUTHORIZED.code,ServiceExceptionEnum.SYS_UNAUTHORIZED.message);
        }
        token = token.replaceAll("\"","");
        //获取加密密钥
        String ipAddress = IpUtil.getIpAddress(request);
        String userAgent = BrowserUtil.getUserAgent(request);
        //对token进行解密
        String decrypt = AesUtil.decrypt(token, ipAddress + userAgent);

        if (decrypt == null){
            throw new BizException(ServiceExceptionEnum.SYS_UNAUTHORIZED.message);
        }

        UserVO userVO = RedisUtil.getCacheObject(decrypt);
        if (ObjectUtils.isEmpty(userVO)) {
            throw new BizException(ServiceExceptionEnum.SYS_UNAUTHORIZED.message);
        }
        //获取用户名和id
        return ConverterUtil.convert(userVO, TokenUserMesVo.class);

    }

    public String getUserId(){
        TokenUserMesVo tokenUserMesVo = analysisToken();

        if (ObjectUtil.isEmpty(tokenUserMesVo)){
            throw new BizException(ErrorConstants.USER_IS_NULL);
        }

        String userId = tokenUserMesVo.getUserId();

        //String userId = "b068c657-5b5a-40f1-8c52-8ca1d66efe96";

        if (!StringUtils.hasText(userId)){
            throw new BizException(ErrorConstants.USER_IS_NULL);
        }

        return userId;

    }

    /**
     * 获取token
     */
    public static String getToken(String key) {
        HttpServletRequest request = ((ServletRequestAttributes) Objects.requireNonNull(RequestContextHolder.getRequestAttributes())).getRequest();
        String token = request.getHeader("key");
        if (token == null) {
            token = request.getParameter(key);
        }
        if (token == null || token.length() == 0) {
            return null;
        }
        return token;
    }

    /**
     * 创建令牌
     *
     * @param tokenVO
     * @return 令牌
     */
    public String createToken(TokenVO tokenVO) {
        HttpServletRequest request = ((ServletRequestAttributes) Objects.requireNonNull(RequestContextHolder.getRequestAttributes())).getRequest();
        String token = RedisConstants.USER_TOKEN_REDIS_KEY_PREFIX + UUID.randomUUID().toString().replace("-", "");
        String ipAddress = IpUtil.getIpAddress(request);
        String userAgent = BrowserUtil.getUserAgent(request);
        String reToken = AesUtil.encrypt(token, ipAddress + userAgent);
        tokenVO.setToken(reToken);
        //测试对接先调整过期为720小时
        RedisUtil.setCacheObject(token, tokenVO, Duration.ofHours(720));
        return reToken;
    }


}
