package org.yixz.common.util;

import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.BeanUtils;

import java.util.List;
import java.util.stream.Collectors;

@Slf4j
public class ConverterUtil {

    public static <S, T> T convert(S source, Class<T> tClass) {
        T target = null;
        try {
            target = tClass.newInstance();
            if (source == null) {
                return null;
            }
            BeanUtils.copyProperties(source, target);
        } catch (InstantiationException e) {
            log.error("对象转换出错");
            e.printStackTrace();
        } catch (IllegalAccessException e) {
            log.error("对象转换出错");
            e.printStackTrace();
        }
        return target;
    }


    public static <S, T> List<T> convert(List<S> sourceList, Class<T> tClass) {
        return sourceList.stream().map(e -> convert(e, tClass)
        ).collect(Collectors.toList());
    }

}
