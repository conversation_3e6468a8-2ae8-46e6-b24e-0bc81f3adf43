package org.yixz.common.util;

import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import lombok.extern.slf4j.Slf4j;
import okhttp3.OkHttpClient;
import okhttp3.Request;
import okhttp3.Response;
import okhttp3.ResponseBody;
import org.apache.commons.lang3.StringUtils;
import org.jsoup.Jsoup;
import org.jsoup.nodes.Document;
import org.yixz.common.enums.VideoPlatformEnum;
import org.yixz.common.exception.BizException;
import org.yixz.entity.vo.VideoDownloadResponseVo;

import java.io.BufferedInputStream;
import java.io.BufferedOutputStream;
import java.io.File;
import java.io.FileOutputStream;
import java.io.IOException;
import java.io.UnsupportedEncodingException;
import java.net.SocketTimeoutException;
import java.net.URLEncoder;
import java.nio.charset.StandardCharsets;
import java.security.MessageDigest;
import java.security.NoSuchAlgorithmException;
import java.time.LocalDate;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.TreeMap;
import java.util.concurrent.TimeUnit;
import java.util.regex.Matcher;
import java.util.regex.Pattern;
import java.util.stream.Collectors;

import static org.yixz.common.constants.RedisConstants.VIDEO_LOGIN_COOKIE;

@Slf4j
public class BiliUtil {

    private final static Integer BILI_BITSTREAM = 64;
    private final static String USER_AGENT = "Mozilla/5.0 (Linux; Android 14; Xiaomi 23127PN0CC Build/UKQ1.230917.001; wv) AppleWebKit/537.36 (KHTML, like Gecko) Version/4.0 Chrome/123.0.6312.60 Mobile Safari/537.36";
    private final static String DROID = "Mozilla/5.0 BiliDroid/8.42.0 (<EMAIL>)";

    private static final int[] mixinKeyEncTab = new int[] {
            46, 47, 18, 2, 53, 8, 23, 32, 15, 50, 10, 31, 58, 3, 45, 35, 27, 43, 5, 49,
            33, 9, 42, 19, 29, 28, 14, 39, 12, 38, 41, 13, 37, 48, 7, 16, 24, 55, 40,
            61, 26, 17, 0, 1, 60, 51, 30, 4, 22, 25, 54, 21, 56, 59, 6, 63, 57, 62, 11,
            36, 20, 34, 44, 52
    };

    private static final char[] hexDigits = "0123456789abcdef".toCharArray();

    // 缓存的wbi keys
    private static String cachedImgKey = null;
    private static String cachedSubKey = null;
    private static LocalDate cacheDate = null;

    /**
     * 解析视频信息并下载视频源文件
     *
     * @param url   视频URL
     * @param cookie 用户token
     * @return 视频信息列表
     */
    public static VideoDownloadResponseVo findVideoStreaming(String url, String cookie) {
        // 获取视频基本信息
        VideoDownloadResponseVo videoDataInfo = getVideoDataInfo(url);
        if (videoDataInfo.getVideoFiles().isEmpty()) {
            log.warn("url：{} 未获取到视频信息", url);
            return videoDataInfo;
        }

        log.info("url: {} 获取到{}个视频数据", url, videoDataInfo.getVideoFiles().size());

        for (VideoDownloadResponseVo.VideoFileInfo videoFileInfo : videoDataInfo.getVideoFiles()) {
            try {
                // 获取必要参数
                String aid = videoDataInfo.getAid();
                String cid = videoFileInfo.getCid();
                String title = videoDataInfo.getTitle();
                String quality = videoFileInfo.getQuality();

                if (aid == null || cid == null || title == null) {
                    log.warn("视频数据不完整: aid={}, cid={}, title={}", aid, cid, title);
                    continue;
                }

                // 生成文件名
                String filename = StringUtil.getFileName(title, cid);

                // 构建API请求地址
                String apiUrl = buildInterfaceAddress(aid, cid, cookie, quality);

                // 请求视频源信息
                String response = httpGetBili(apiUrl, "UTF-8", cookie);

                JSONObject videoData = JSONObject.parseObject(response);

                // 判断是否为DASH格式视频
                boolean isDashFormat = (BILI_BITSTREAM >= 80 && quality.equals("1")) ||
                        videoData.getJSONObject("data").containsKey("dash");

                if (isDashFormat) {
                    // 处理DASH格式视频
                    processDashVideo(videoData, videoFileInfo, filename);
                } else {
                    // 处理普通格式视频
                    processNormalVideo(videoData, videoFileInfo, filename);
                }
                // 如果是DASH格式，通常只有一个视频
                if (isDashFormat) {
                    return videoDataInfo;
                }
            } catch (Exception e) {
                log.error("处理视频【{}】时出错: ", videoFileInfo.getName(), e);
                throw new BizException(e.getMessage());
            }
        }
        return videoDataInfo;
    }

    /**
     * 处理DASH格式视频
     *
     * @param videoData 视频JSON数据
     * @param filename  文件名
     * @return 处理后的视频信息
     */
    private static void processDashVideo(JSONObject videoData, VideoDownloadResponseVo.VideoFileInfo videoFileInfo, String filename) {
        processing(videoData, videoFileInfo, filename);
    }

    /**
     * 处理普通格式视频
     *
     * @param videoData 视频JSON数据
     * @param filename  文件名
     * @return 处理后的视频信息
     */
    private static void processNormalVideo(JSONObject videoData, VideoDownloadResponseVo.VideoFileInfo videoFileInfo, String filename) {
        // 获取视频直链
        String videoUrl = videoData.getJSONObject("data").getJSONArray("durl").getJSONObject(0).getString("url");

        // 使用HTTP直接下载
        String videoFileName = filename + ".mp4";
        String targetDir = FileUtil.generateVideoDownloadDir(VideoPlatformEnum.BILIBILI.getCode(), false, filename);
        downBiliFromUrl(videoUrl, videoFileName, targetDir);

        // 返回视频信息
        videoFileInfo.setName(videoFileName);
        videoFileInfo.setUrl(videoUrl);
        videoFileInfo.setFormat("mp4");
        videoFileInfo.setPath(targetDir + File.separator + videoFileName);
    }

    /**
     * 处理DASH格式视频，下载和合并音视频
     *
     * @param videoData JSON视频数据
     * @param filename  文件名
     */
    private static void processing(JSONObject videoData, VideoDownloadResponseVo.VideoFileInfo videoFileInfo, String filename) {
        // 获取音视频流URL
        JSONObject dashData = videoData.getJSONObject("data").getJSONObject("dash");
        String videoUrl = dashData.getJSONArray("video").getJSONObject(0).getString("base_url");
        String audioUrl = dashData.getJSONArray("audio").getJSONObject(0).getString("base_url");

        // 下载视频
        String savePath = processHttpDownload(videoUrl, audioUrl, filename);

        videoFileInfo.setName(filename);
        videoFileInfo.setPath(savePath);
    }

    /**
     * 使用HTTP方式下载并处理DASH格式视频
     */
    private static String processHttpDownload(String videoUrl, String audioUrl, String filename) {
        // 创建临时目录
        String tempDir = FileUtil.generateVideoDownloadDir(VideoPlatformEnum.BILIBILI.getCode(), true, filename);
        String outputPath = FileUtil.generateVideoDownloadDir(VideoPlatformEnum.BILIBILI.getCode(), false, filename);

        // 下载音视频文件
        String videoFile = tempDir + File.separator + filename + "-video.m4s";
        String audioFile = tempDir + File.separator + filename + "-audio.m4s";

        downBiliFromUrl(videoUrl, filename + "-video.m4s", tempDir);
        downBiliFromUrl(audioUrl, filename + "-audio.m4s", tempDir);

        // 合并音视频文件
        String ffmpegCmd = String.format("ffmpeg -y -i %s -i %s -c:v copy -c:a copy -f mp4 %s",
                videoFile, audioFile, outputPath);

        CommandUtil.command(ffmpegCmd);

        // 清理临时文件
        System.gc();
        FileUtil.deleteFile(videoFile);
        FileUtil.deleteFile(audioFile);
        return outputPath;
    }

    /**
     * @param url 视频URL
     */
    public static VideoDownloadResponseVo getVideoDataInfo(String url) {
        VideoDownloadResponseVo res = new VideoDownloadResponseVo();
        String parseEntry = BiliUtil.parseEntry(url);
        String api = "";
        if (parseEntry.contains("BV")) {
            api = "https://api.bilibili.com/x/web-interface/view?bvid=" + parseEntry.substring(2);
        }
        if (parseEntry.contains("av")) {
            api = "https://api.bilibili.com/x/web-interface/view?aid=" + parseEntry.substring(2);
        }
        String searchResult = httpGetBili(api, "UTF-8", null);
        log.info("解析视频信息识别，查询结果：{}", searchResult);
        JSONObject videoData = JSONObject.parseObject(searchResult);
        if (videoData.getString("code").equals("0")) {
            // 优化多集问题 从page 里取
            String bvid = videoData.getJSONObject("data").getString("bvid");
            String aid = videoData.getJSONObject("data").getString("aid");
            String desc = videoData.getJSONObject("data").getString("desc");
            String pic = videoData.getJSONObject("data").getString("pic");
            JSONObject dimension = videoData.getJSONObject("data").getJSONObject("dimension");
            String owner = videoData.getJSONObject("data").getString("owner");
            String createTime = videoData.getJSONObject("data").getString("ctime");

            Integer width = dimension.getInteger("width");
            Integer height = dimension.getInteger("height");

            JSONArray jsonArray = videoData.getJSONObject("data").getJSONArray("pages");
            ArrayList<VideoDownloadResponseVo.VideoFileInfo> videoFileInfos = new ArrayList<>();
            for (int i = 0; i < jsonArray.size(); i++) {
                VideoDownloadResponseVo.VideoFileInfo videoFileInfo = new VideoDownloadResponseVo.VideoFileInfo();
                JSONObject jsonObject = jsonArray.getJSONObject(i);
                String cid = jsonObject.getString("cid");
                String title = jsonObject.getString("part");
                if (width >= 1920 || height >= 1080) {
                    videoFileInfo.setQuality("1");
                } else {
                    videoFileInfo.setQuality("0");
                }
                if (null == pic) {
                    pic = jsonObject.getString("first_frame");
                }
                res.setTitle(title);
                videoFileInfo.setCid(cid);
                videoFileInfos.add(videoFileInfo);
            }
            // 设置返回值
            res.setAid(aid);
            res.setBvid(bvid);
            res.setDescription(desc);
            res.setAuthor(owner);
            res.setCreateTime(createTime);
            res.setThumbnail(pic);
            res.setVideoFiles(videoFileInfos);
        } else {
            log.error("解析视频信息识别，查询结果：{}", searchResult);
            throw new BizException("解析视频信息失败");
        }
        return res;
    }

    public static String parseEntry(String url) {
        if (url.contains("/video/av") || url.contains("/video/BV")) {
            return BiliUtil.findUrlAidOrBid(url);
        } else {
            Document document = null;
            try {
                document = Jsoup.connect(url).userAgent(
                                "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36")
                        .get();
                String baseUri = document.baseUri();
                return BiliUtil.findUrlAidOrBid(baseUri);
            } catch (IOException e1) {
                log.error("解析视频信息失败", e1);
            }
        }
        return "";
    }

    public static String findUrlAidOrBid(String url) {
        String replace = "";
        Pattern pattern = Pattern.compile("/video/(BV\\w+|av\\d+)", Pattern.CASE_INSENSITIVE);
        Matcher matcher = pattern.matcher(url);
        if (matcher.find()) {
            String videoId = matcher.group(1);
            return videoId;
        }
        // 下边是旧代码 后续移除 理论无效
        if (url.contains("http")) {
            replace = url.replaceAll("http://", "").replaceAll("https://", "").replace("www.bilibili.com/video/", "");
            int indexOf = replace.indexOf("/");
            String id = replace.substring(0, indexOf);
            return id;
        } else {
            replace = url.replaceAll("/video/", "");
            return replace;
        }
    }

    public static String buildInterfaceAddress(String aid, String cid, String cookie, String quality) {
        int bilibitstream = BILI_BITSTREAM;
        if (quality.equals("0")) {
            log.info("视频没有2k以上进行画质降级");
            bilibitstream = 80; // 画质降级
        }
        String api = "https://api.bilibili.com/x/player/playurl?avid=" + aid + "&cid=" + cid;
        if (null != cookie && !cookie.isEmpty()) {
            if (bilibitstream != 64) {
                // vip
                if (bilibitstream >= 120) {
                    api = api + "&qn=0";
                } else {
                    api = api + "&qn=" + bilibitstream;
                }
            } else {
                api = api + "&qn=80";
            }
        } else {
            api = api + "&qn=64";
        }
        api = api + "&fnver=0"; // 固定 0
        api = switch (bilibitstream) {
            case 80 -> api + "&fourk=1&fnval=" + (16 | 128); // 4k 传128
            case 112 -> api + "&fourk=1&fnval=" + (16 | 128); // 4k 传128
            case 116 -> api + "&fourk=1&fnval=" + (16 | 128); // 4k 传128
            case 120 -> api + "&fourk=1&fnval=" + (16 | 128); // 4k 传128
            case 125 -> api + "&fourk=1&fnval=" + (16 | 64); // hdr 传64
            case 126 -> api + "&fourk=1&fnval=" + (16 | 512); // 杜比视界 传128
            case 127 -> api + "&fourk=1&fnval=" + (16 | 1024); // 8k 传128
            default -> api + "&fourk=0&fnval=1";
        };
        return api;
    }

    /**
     * 获取用户投稿视频
     *
     * @param mid    用户ID
     * @param maxcur 限制数量
     * @return 视频列表
     */
    public static JSONArray getVideos(String mid, Integer maxcur) {
        List<JSONObject> videos = new ArrayList<>();
        getVideosRecursive(mid, "1", maxcur, videos);
        JSONArray array = new JSONArray();
        array.addAll(videos);
        return array;
    }

    /**
     * 递归获取视频的具体实现
     */
    private static void getVideosRecursive(String mid, String pn, Integer maxcur, List<JSONObject> videos) {
        TreeMap<String, Object> params = new TreeMap<>();
        params.put("mid", mid);
        params.put("ps", "30");
        params.put("pn", pn);
        params.put("order", "pubdate");

        String wbiUrl = buildWbiUrl(params);
        if (wbiUrl == null) {
            return;
        }
        // 从Redis获取cookie
        String cookie = RedisUtil.getCacheMapValue(VIDEO_LOGIN_COOKIE, VideoPlatformEnum.BILIBILI.getCode());
        String apiUrl = "https://api.bilibili.com/x/space/wbi/arc/search?" + wbiUrl;
        try {
            String response = httpGetBili(apiUrl, cookie, "https://space.bilibili.com", "https://space.bilibili.com/" + mid);
            JSONObject json = JSONObject.parseObject(response);
            if (json.getInteger("code") == 0) {
                JSONObject data = json.getJSONObject("data");
                JSONObject list = data.getJSONObject("list");
                JSONArray vlist = list.getJSONArray("vlist");

                if (vlist.isEmpty()) {
                    return;
                }

                // 添加视频
                for (int i = 0; i < vlist.size(); i++) {
                    JSONObject video = vlist.getJSONObject(i);
                    videos.add(video);

                    // 如果设置了maxcur且已达到限制,则停止获取
                    if (maxcur != null && videos.size() >= maxcur) {
                        return;
                    }
                }

                // 检查是否需要获取下一页
                if (maxcur == null || videos.size() < maxcur) {
                    Thread.sleep(5000); // 睡一会
                    int page = Integer.parseInt(pn);
                    int count = data.getJSONObject("page").getInteger("count");
                    int ps = data.getJSONObject("page").getInteger("ps");
                    int totalPages = (count + ps - 1) / ps;

                    if (page < totalPages) {
                        getVideosRecursive(mid, String.valueOf(page + 1), maxcur, videos);
                    }
                }
            }
        } catch (Exception e) {
            log.error("获取视频列表失败: {}", e.getMessage());
        }
    }

    /**
     * 根据URL获取视频信息
     * @param url 视频URL
     * @param charset 编码
     * @param cookie cookie
     * @return 视频信息
     */
    public static String httpGetBili(String url, String charset, String cookie) {
        // 构建请求头
        Map<String, String> headers = new HashMap<>();
        headers.put("user-agent", USER_AGENT);
        if (StringUtils.isNotBlank(cookie)) {
            headers.put("cookie", cookie);
        }

        // 发送GET请求
        String response = OkHttpUtil.doGet(url, null, headers);
        if (StringUtils.isBlank(response)) {
            throw new BizException("获取视频信息失败，结果为空，url=" + url);
        }
        // 处理编码转换（如果需要且响应不为空）
        try {
            if (charset != null && !charset.equals("UTF-8")) {
                // 将UTF-8响应转换为指定编码
                byte[] bytes = response.getBytes(StandardCharsets.UTF_8);
                response = new String(bytes, charset);
            }
        }catch (Exception e){
            log.error("不支持的编码格式：{}", charset);
        }
        return response;
    }

    public static String httpGetBili(String url, String cookie, String origin, String referer) {
        OkHttpClient client = SpringContextUtil.getBean(OkHttpClient.class);
        Request.Builder requestBuilder = new Request.Builder()
                .url(url)
                .get()
                .header("User-Agent", USER_AGENT);
        if (origin != null && !origin.isEmpty()) {
            requestBuilder.header("Origin", origin);
        }
        if (referer != null && !referer.isEmpty()) {
            requestBuilder.header("Referer", referer);
        }
        if (cookie != null && !cookie.isEmpty()) {
            requestBuilder.header("Cookie", cookie);
        }
        Request request = requestBuilder.build();
        String responseStr = "";
        try (Response response = client.newCall(request).execute()) {
            if (!response.isSuccessful()) {
                log.error("B站视频下载返回失败，状态码: {}，信息：{}", response.code(), response.message());
            } else {
                ResponseBody body = response.body();
                if (body != null) {
                    byte[] bytes = body.bytes();
                    responseStr = new String(bytes, StandardCharsets.UTF_8);
                }
            }
        } catch (IOException e) {
            log.error("下载B站视频识别失败: ", e);
        }
        return responseStr;
    }

    public static void downBiliFromUrl(String urlStr, String fileName, String savePath) {
        downBiliFromUrl(urlStr, fileName, savePath, null);
    }

    public static void downBiliFromUrl(String urlStr, String fileName, String savePath, String cookie) {
        if (urlStr == null || urlStr.isEmpty() || fileName == null || fileName.isEmpty() ||
                savePath == null || savePath.isEmpty()) {
            throw new IllegalArgumentException("urlStr, fileName, savePath 不能为空");
        }

        int maxRetries = 3;
        int retryCount = 0;
        long retryDelay = 5000;

        while (retryCount < maxRetries) {
            OkHttpClient client = new OkHttpClient.Builder()
                    .connectTimeout(5, TimeUnit.SECONDS)
                    .readTimeout(30, TimeUnit.SECONDS)
                    .writeTimeout(30, TimeUnit.SECONDS)
                    .retryOnConnectionFailure(true)
                    .build();

            File saveDir = new File(savePath);
            File file = new File(saveDir, fileName);
            long downloaded = 0;
            long lastReadTime = System.currentTimeMillis();
            boolean needRetry = false;

            try {
                if (!saveDir.exists()) {
                    saveDir.mkdirs();
                }

                Request.Builder requestBuilder = new Request.Builder()
                        .url(urlStr)
                        .addHeader("User-Agent", DROID)
                        .addHeader("referer", "https://www.bilibili.com");

                if (cookie != null && !cookie.isEmpty()) {
                    requestBuilder.addHeader("Cookie", cookie);
                }

                Request request = requestBuilder.build();

                try (Response response = client.newCall(request).execute()) {
                    if (!response.isSuccessful()) {
                        log.error("下载失败：code:{}, msg:{}", response.code(), response.message());
                        return;
                    }

                    assert response.body() != null;
                    long fileLength = response.body().contentLength();
                    if (file.exists() && fileLength > 0 && file.length() == fileLength) {
                        log.info("文件已存在且大小相同,跳过下载: {}", fileName);
                        return;
                    }

                    try (BufferedInputStream bis = new BufferedInputStream(response.body().byteStream());
                         BufferedOutputStream bos = new BufferedOutputStream(new FileOutputStream(file))) {

                        byte[] buffer = new byte[32 * 1024];
                        int len;
                        long startTime = System.currentTimeMillis();
                        long lastProgressTime = startTime;

                        while ((len = bis.read(buffer)) != -1) {
                            bos.write(buffer, 0, len);
                            downloaded += len;
                            long currentTime = System.currentTimeMillis();

                            if (fileLength > 0 && currentTime - lastProgressTime >= 1000) {
                                int progress = (int) (downloaded * 100 / fileLength);
                                if (progress % 10 == 0) {
                                    double speed = (downloaded / 1024.0) / ((currentTime - startTime) / 1000.0);
                                    log.info("下载进度: {}%, 速度: {} KB/s, 文件: {}", progress, speed, fileName);
                                }
                                lastProgressTime = currentTime;
                            }

                            if (currentTime - lastReadTime > 30000) {
                                log.info("下载失败，超时");
                                return;
                            }
                        }
                        bos.flush();

                        if (fileLength > 0 && file.length() != fileLength) {
                            log.error("下载失败，文件不完整");
                            return;
                        }
                        log.info("文件下载完成: {}", fileName);
                        return;
                    }
                }
            } catch (SocketTimeoutException e) {
                log.warn("下载超时(第 {} 次重试): {}", retryCount + 1, fileName);
                needRetry = true;
            } catch (IOException e) {
                log.error("下载出错：", e);
                return;
            } finally {
                if (needRetry && file.exists()) {
                    file.delete();
                }
            }

            retryCount++;
            if (retryCount < maxRetries) {
                try {
                    Thread.sleep(retryDelay);
                } catch (InterruptedException ignored) {
                }
            }
        }
        log.error("{}下载失败，已重试多次", fileName);
    }

    /**
     * 获取当前有效的WBI签名密钥
     *
     * @return String[] 返回img_key和sub_key
     */
    public static String[] getWbiKeys() {
        LocalDate today = LocalDate.now();

        // 检查缓存是否存在且在当天有效
        if (cachedImgKey != null && cachedSubKey != null &&
                cacheDate != null && cacheDate.equals(today)) {
            return new String[] { cachedImgKey, cachedSubKey };
        }

        // 缓存不存在或已过期，重新获取
        try {
            String cookie = RedisUtil.getCacheMapValue(VIDEO_LOGIN_COOKIE, VideoPlatformEnum.BILIBILI.getCode());
            String navResponse = BiliUtil.httpGetBili("https://api.bilibili.com/x/web-interface/nav",
                    cookie,null,null);
            JSONObject json = JSONObject.parseObject(navResponse);
            if (json.getInteger("code") == 0) {
                JSONObject data = json.getJSONObject("data");
                JSONObject wbiImg = data.getJSONObject("wbi_img");
                String imgUrl = wbiImg.getString("img_url");
                String subUrl = wbiImg.getString("sub_url");
                // 提取文件名中的字符串作为key
                cachedImgKey = imgUrl.substring(imgUrl.lastIndexOf("/") + 1).split("\\.")[0];
                cachedSubKey = subUrl.substring(subUrl.lastIndexOf("/") + 1).split("\\.")[0];
                cacheDate = today;
                return new String[] { cachedImgKey, cachedSubKey };
            }
        } catch (Exception e) {
            log.error("getWbiKeys方法异常: ", e);
        }
        return null;
    }

    /**
     * 获取缓存的img_key
     */
    public static String getImgKey() {
        String[] keys = getWbiKeys();
        return keys != null ? keys[0] : null;
    }

    /**
     * 获取缓存的sub_key
     */
    public static String getSubKey() {
        String[] keys = getWbiKeys();
        return keys != null ? keys[1] : null;
    }

    public static String md5(String input) {
        try {
            MessageDigest md = MessageDigest.getInstance("MD5");
            byte[] messageDigest = md.digest(input.getBytes(StandardCharsets.UTF_8));
            char[] result = new char[messageDigest.length * 2];
            for (int i = 0; i < messageDigest.length; i++) {
                result[i * 2] = hexDigits[(messageDigest[i] >> 4) & 0xF];
                result[i * 2 + 1] = hexDigits[messageDigest[i] & 0xF];
            }
            return new String(result);
        } catch (NoSuchAlgorithmException e) {
            return null;
        }
    }

    public static String getMixinKey(String imgKey, String subKey) {
        String s = imgKey + subKey;
        StringBuilder key = new StringBuilder();
        for (int i = 0; i < 32; i++)
            key.append(s.charAt(mixinKeyEncTab[i]));
        return key.toString();
    }

    public static String encodeURIComponent(Object o) {
        try {
            return URLEncoder.encode(o.toString(), "UTF-8").replace("+", "%20");
        } catch (UnsupportedEncodingException e) {
            throw new RuntimeException(e);
        }
    }

    /**
     * 生成带签名的URL参数
     *
     * @param params 请求参数（已按key排序）
     * @return 带签名的URL参数字符串
     */
    public static String buildWbiUrl(TreeMap<String, Object> params) {
        // 获取当前的wbi keys
        String[] wbiKeys = getWbiKeys();
        if (wbiKeys == null) {
            return null;
        }

        // 添加当前时间戳参数
        params.put("wts", System.currentTimeMillis() / 1000);

        // 将参数转换为URL参数格式
        String param = params.entrySet().stream()
                .map(entry -> String.format("%s=%s", entry.getKey(), encodeURIComponent(entry.getValue())))
                .collect(Collectors.joining("&"));

        // 计算签名
        String mixinKey = getMixinKey(wbiKeys[0], wbiKeys[1]);
        String wbiSign = md5(param + mixinKey);

        // 组合最终参数
        return param + "&w_rid=" + wbiSign;
    }
}
