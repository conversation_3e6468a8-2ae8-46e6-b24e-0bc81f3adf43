package org.yixz.common.util;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.io.BufferedReader;
import java.io.IOException;
import java.io.InputStream;
import java.io.InputStreamReader;
import java.util.regex.Matcher;
import java.util.regex.Pattern;

public class CommandUtil {

    private static Logger logger = LoggerFactory.getLogger(CommandUtil.class);

    static Pattern pattern = Pattern.compile("\"(.*?)\"");

    /**
     * 执行命令并输出结果到控制台
     *
     * @param command 要执行的命令
     */
    public static void command(String command) {
        Process process = null;
        try {
            ProcessBuilder processBuilder;
            if (System.getProperty("os.name").toLowerCase().contains("win")) {
                processBuilder = new ProcessBuilder("cmd.exe", "/c", command);
            } else {
                processBuilder = new ProcessBuilder("/bin/sh", "-c", command);
            }

            processBuilder.redirectErrorStream(true);
            process = processBuilder.start();
            try (InputStream inputStream = process.getInputStream();
                 BufferedReader reader = new BufferedReader(new InputStreamReader(inputStream))) {
                String line;
                while ((line = reader.readLine()) != null) {
                    System.out.println(line);
                }
            }
            int exitCode = process.waitFor();
            logger.info("命令执行完毕，退出码：" + exitCode);
        } catch (IOException | InterruptedException e) {
            logger.error("命令执行异常：" + e.getMessage(), e);
        } finally {
            if (process != null) {
                try {
                    process.destroy();
                    if (process.isAlive()) {
                        process.destroyForcibly();
                    }
                } catch (Exception e) {
                    logger.error("销毁进程时发生异常：" + e.getMessage());
                }
            }
        }
    }

    public static String steamcmd(String account, String password, String wallpaper) {
        try {
            ProcessBuilder processBuilder = new ProcessBuilder("steamcmd", "+login " + account + " " + password + "",
                    "+workshop_download_item 431960 " + wallpaper + "", "+quit");
            Process process = processBuilder.start();
            InputStream inputStream = process.getInputStream();
            BufferedReader reader = new BufferedReader(new InputStreamReader(inputStream));

            String line;
            String path = "";
            while ((line = reader.readLine()) != null) {
                Matcher matcher = pattern.matcher(line);
                while (matcher.find()) {
                    path = matcher.group(1);
                }
            }
            int exitCode = process.waitFor();
            System.out.println("SteamCMD执行完毕，退出码：" + exitCode);
            return path;
        } catch (IOException | InterruptedException e) {
            e.printStackTrace();
        }
        return wallpaper;
    }

    /**
     * 执行命令并返回输出结果
     *
     * @param command 要执行的命令
     * @return 命令执行的输出结果
     */
    public static String commandos(String command) {
        StringBuilder output = new StringBuilder();
        Process process = null;
        BufferedReader reader = null;
        try {
            ProcessBuilder processBuilder;
            if (System.getProperty("os.name").toLowerCase().contains("win")) {
                processBuilder = new ProcessBuilder("cmd.exe", "/c", command);
            } else {
                processBuilder = new ProcessBuilder("/bin/sh", "-c", command);
            }
            processBuilder.redirectErrorStream(true);
            process = processBuilder.start();
            try (InputStream inputStream = process.getInputStream();
                 BufferedReader bufferedReader = new BufferedReader(new InputStreamReader(inputStream, "UTF-8"))) {
                reader = bufferedReader;
                String line;
                while ((line = reader.readLine()) != null) {
                    output.append(line).append("\n");
                }
            }
            int exitCode = process.waitFor();
            logger.info("命令执行完毕，退出码：" + exitCode);
        } catch (IOException | InterruptedException e) {
            logger.error("命令执行异常：" + e.getMessage(), e);
        } finally {
            if (process != null) {
                try {
                    process.destroy();
                    if (process.isAlive()) {
                        process.destroyForcibly();
                    }
                } catch (Exception e) {
                    logger.error("销毁进程时发生异常：" + e.getMessage());
                }
            }
        }
        return output.toString().trim();
    }

    public static String f2cmd(String cookie, String aid, String fuc, String uid, String cid, Integer maxc,
                               String out) {
        StringBuilder cmd = new StringBuilder("/opt/venv/bin/python3 /home/<USER>/script/douyin.py ");
        switch (fuc) {
            case "fetch_video":
                cmd.append("fetch_video ")
                        .append("--cookie \"").append(cookie).append("\" ")
                        .append("--aweme_id \"").append(aid).append("\"");
                break;

            case "fetch_user_like_videos":
            case "fetch_user_post_videos":
                cmd.append(fuc).append(" ")
                        .append("--cookie \"").append(cookie).append("\" ")
                        .append("--uid \"").append(uid).append("\" ")
                        .append("--maxc \"").append(maxc).append("\" ")
                        .append("--output \"").append(out).append("\"");
                break;

            case "fetch_user_collects":
                cmd.append("fetch_user_collects ")
                        .append("--cookie \"").append(cookie).append("\"");
                break;

            case "fetch_user_collects_videos":
                cmd.append("fetch_user_collects_videos ")
                        .append("--cookie \"").append(cookie).append("\" ")
                        .append("--cid \"").append(cid).append("\" ")
                        .append("--maxc \"").append(maxc).append("\" ")
                        .append("--output \"").append(out).append("\"");
                break;

            case "fetch_user_feed_videos":
                cmd.append("fetch_user_feed_videos ")
                        .append("--cookie \"").append(cookie).append("\" ")
                        .append("--uid \"").append(uid).append("\" ")
                        .append("--output \"").append(out).append("\"");
                break;

            case "fetch_post_data":
                cmd.append("fetch_post_data ")
                        .append("--cookie \"").append(cookie).append("\" ")
                        .append("--aweme_id \"").append(aid).append("\" ")
                        .append("--output \"").append(out).append("\"");
                break;
            default:
                throw new IllegalArgumentException("Unsupported function: " + fuc);
        }

        return CommandUtil.commandos(cmd.toString());
    }
}
