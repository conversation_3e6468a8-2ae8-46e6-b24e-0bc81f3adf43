package org.yixz.common.util;

import lombok.extern.slf4j.Slf4j;
import okhttp3.FormBody;
import okhttp3.MediaType;
import okhttp3.OkHttpClient;
import okhttp3.Request;
import okhttp3.RequestBody;
import okhttp3.Response;

import java.util.Map;

@Slf4j
public class OkHttpUtil {

    private static final MediaType JSON = MediaType.parse("application/json; charset=utf-8");
    private static final MediaType XML = MediaType.parse("application/xml; charset=utf-8");

    private static final OkHttpClient okHttpClient = SpringContextUtil.getBean(OkHttpClient.class);

    /**
     * get 请求
     *
     * @param url
     *            请求url地址
     * @return string
     */
    public static String doGet(String url) {
        return doGet(url, null, null);
    }

    /**
     * get 请求
     *
     * @param url
     *            请求url地址
     * @param headers
     *            请求头字段 {k1, v1 k2, v2, ...}
     * @return string
     */
    public static String doGet(String url, Map<String, String> headers) {
        return doGet(url, null, headers);
    }

    /**
     * get 请求
     *
     * @param url
     *            请求url地址
     * @param params
     *            请求参数 map
     * @param headers
     *            请求头字段 {k1, v1 k2, v2, ...}
     * @return string
     */
    public static String doGet(String url, Map<String, String> params, Map<String, String> headers) {
        //拼接请求参数
        StringBuilder sb = buildGetParams(url, params);
        //组装头部
        Request.Builder builder = new Request.Builder();
        buildHeader(builder, headers);

        Request request = builder.url(sb.toString()).build();
        return execute(request);
    }


    /**
     * post 请求
     *
     * @param url
     *            请求url地址
     * @param params
     *            请求参数 map
     * @return string
     */
    public static String doPost(String url, Map<String, String> params) {
        FormBody.Builder builder = new FormBody.Builder();
        //组装参数
        buildPostParams(builder, params);
        Request request = new Request.Builder().url(url).post(builder.build()).build();
        return execute(request);
    }

    /**
     * post 请求
     *
     * @param url
     *            请求url地址
     * @param params
     *            请求参数 map
     * @return string
     */
    public static String doPost(String url, Map<String, String> params, Map<String, String> headers) {
        FormBody.Builder builder = new FormBody.Builder();
        //组装参数
        buildPostParams(builder, params);

        Request.Builder requestBuilder = new Request.Builder();
        //组装头部
        buildHeader(requestBuilder, headers);
        Request request = requestBuilder.url(url).post(builder.build()).build();
        return execute(request);
    }

    /**
     * post 请求, 请求数据为 json 的字符串
     *
     * @param url
     *            请求url地址
     * @param json
     *            请求数据, json 字符串
     * @return string
     */
    public static String doPostJson(String url, String json) {
        return executePost(url, json, JSON);
    }

    /**
     * post 请求, 请求数据为 json 的字符串
     *
     * @param url
     *            请求url地址
     * @param json
     *            请求数据, json 字符串
     * @return string
     */
    public static String doPostJson(String url, String json, Map<String, String> headers) {
        return executePost(url, json, JSON, headers);
    }

    /**
     * post 请求, 请求数据为 xml 的字符串
     *
     * @param url
     *            请求url地址
     * @param xml
     *            请求数据, xml 字符串
     * @return string
     */
    public static String doPostXml(String url, String xml) {
        return executePost(url, xml, XML);
    }

    private static String executePost(String url, String data, MediaType mediaType) {
        RequestBody requestBody = RequestBody.Companion.create(data, mediaType);
        Request request = new Request.Builder().url(url).post(requestBody).build();
        return execute(request);
    }

    private static String executePost(String url, String data, MediaType mediaType, Map<String, String> headers) {
        RequestBody requestBody = RequestBody.Companion.create(data, mediaType);
        Request.Builder builder = new Request.Builder();
        //组装头部
        buildHeader(builder, headers);
        Request request = builder.url(url).post(requestBody).build();
        return execute(request);
    }

    private static String execute(Request request) {
        try (Response response = okHttpClient.newCall(request).execute()) {
            if (response.isSuccessful()) {
                return response.body()==null?null:response.body().string();
            }
            log.error("请求失败， 响应码：{}， 响应信息：{}", response.code(), response.body()==null?"":response.body().string());
        } catch (Exception e) {
            log.error("请求失败", e);
        }
        return null;
    }

    private static StringBuilder buildGetParams(String url, Map<String, String> params){
        StringBuilder sb = new StringBuilder(url);
        if (params != null && !params.keySet().isEmpty()) {
            boolean firstFlag = true;
            for (Map.Entry<String, String> entry : params.entrySet()) {
                if (firstFlag) {
                    sb.append("?").append(entry.getKey()).append("=").append(entry.getValue());
                    firstFlag = false;
                } else {
                    sb.append("&").append(entry.getKey()).append("=").append(entry.getValue());
                }
            }
        }
        return sb;
    }

    private static void buildPostParams(FormBody.Builder builder, Map<String, String> params){
        if (params != null && !params.keySet().isEmpty()) {
            params.forEach(builder::add);
        }
    }

    private static void buildHeader(Request.Builder builder, Map<String, String> headers){
        if(headers!=null && !headers.isEmpty()) {
            headers.forEach(builder::addHeader);
        }
    }
}
