package org.yixz.common.util;

import lombok.extern.slf4j.Slf4j;
import ws.schild.jave.Encoder;

import java.util.concurrent.TimeUnit;

/**
 * 媒体转换器工具类
 * 统一管理FFmpeg和JAVE2的可用性检测和相关工具方法
 * 
 * <AUTHOR> Assistant
 * @since 2025-01-22
 */
@Slf4j
public class MediaConverterUtil {
    
    private static final String FFMPEG_COMMAND = "ffmpeg";
    
    // 缓存检测结果，避免重复检测
    private static Boolean ffmpegAvailable = null;
    private static Boolean javeAvailable = null;
    
    /**
     * 检查FFmpeg是否可用
     * 
     * @return FFmpeg是否可用
     */
    public static boolean isFFmpegAvailable() {
        if (ffmpegAvailable == null) {
            ffmpegAvailable = checkFFmpegAvailability();
        }
        return ffmpegAvailable;
    }
    
    /**
     * 检查JAVE2是否可用
     * 
     * @return JAVE2是否可用
     */
    public static boolean isJaveAvailable() {
        if (javeAvailable == null) {
            javeAvailable = checkJaveAvailability();
        }
        return javeAvailable;
    }
    
    /**
     * 检查是否有任何可用的转换器
     * 
     * @return 是否有可用的转换器
     */
    public static boolean isAnyConverterAvailable() {
        return isFFmpegAvailable() || isJaveAvailable();
    }
    
    /**
     * 获取首选的转换器类型
     *
     * @return 首选转换器类型
     */
    public static String getPreferredConverter() {
        if (isFFmpegAvailable()) {
            return "FFmpeg";
        } else if (isJaveAvailable()) {
            return "JAVE2";
        } else {
            return "无";
        }
    }
    
    /**
     * 获取首选的字幕提取器类型
     * 
     * @return 首选字幕提取器类型
     */
    public static String getPreferredSubtitleExtractor() {
        if (isFFmpegAvailable()) {
            return "FFmpeg";
        } else if (isJaveAvailable()) {
            return "JAVE2";
        } else {
            return "无";
        }
    }
    
    /**
     * 重置缓存，强制重新检测
     */
    public static void resetCache() {
        ffmpegAvailable = null;
        javeAvailable = null;
        log.info("媒体转换器可用性缓存已重置");
    }
    
    /**
     * 获取系统信息
     * 
     * @return 系统信息对象
     */
    public static SystemInfo getSystemInfo() {
        SystemInfo systemInfo = new SystemInfo();
        systemInfo.setOsName(System.getProperty("os.name"));
        systemInfo.setOsArch(System.getProperty("os.arch"));
        systemInfo.setJavaVersion(System.getProperty("java.version"));
        systemInfo.setFfmpegAvailable(isFFmpegAvailable());
        systemInfo.setJaveAvailable(isJaveAvailable());
        return systemInfo;
    }
    
    /**
     * 检查是否为Apple Silicon Mac
     * 
     * @return 是否为Apple Silicon Mac
     */
    public static boolean isAppleSiliconMac() {
        String osArch = System.getProperty("os.arch");
        String osName = System.getProperty("os.name");
        return "aarch64".equals(osArch) && osName.toLowerCase().contains("mac");
    }
    
    /**
     * 实际检测FFmpeg可用性
     */
    private static boolean checkFFmpegAvailability() {
        try {
            ProcessBuilder pb = new ProcessBuilder(FFMPEG_COMMAND, "-version");
            Process process = pb.start();
            boolean finished = process.waitFor(10, TimeUnit.SECONDS);
            
            if (finished && process.exitValue() == 0) {
                log.debug("FFmpeg可用");
                return true;
            } else {
                log.debug("FFmpeg不可用或超时");
                return false;
            }
        } catch (Exception e) {
            log.debug("检查FFmpeg可用性失败: {}", e.getMessage());
            return false;
        }
    }
    
    /**
     * 实际检测JAVE2可用性
     */
    private static boolean checkJaveAvailability() {
        try {
            Encoder encoder = new Encoder();
            log.debug("JAVE2可用");
            return true;
        } catch (Exception e) {
            log.debug("JAVE2不可用: {}", e.getMessage());
            return false;
        }
    }
    
    /**
     * 系统信息类
     */
    public static class SystemInfo {
        private String osName;
        private String osArch;
        private String javaVersion;
        private boolean ffmpegAvailable;
        private boolean javeAvailable;
        
        // Getters and Setters
        public String getOsName() {
            return osName;
        }
        
        public void setOsName(String osName) {
            this.osName = osName;
        }
        
        public String getOsArch() {
            return osArch;
        }
        
        public void setOsArch(String osArch) {
            this.osArch = osArch;
        }
        
        public String getJavaVersion() {
            return javaVersion;
        }
        
        public void setJavaVersion(String javaVersion) {
            this.javaVersion = javaVersion;
        }
        
        public boolean isFfmpegAvailable() {
            return ffmpegAvailable;
        }
        
        public void setFfmpegAvailable(boolean ffmpegAvailable) {
            this.ffmpegAvailable = ffmpegAvailable;
        }
        
        public boolean isJaveAvailable() {
            return javeAvailable;
        }
        
        public void setJaveAvailable(boolean javeAvailable) {
            this.javeAvailable = javeAvailable;
        }
        
        public boolean isAnyConverterAvailable() {
            return ffmpegAvailable || javeAvailable;
        }
        
        @Override
        public String toString() {
            return String.format("SystemInfo{OS='%s %s', Java='%s', FFmpeg=%s, JAVE2=%s}",
                    osName, osArch, javaVersion, ffmpegAvailable, javeAvailable);
        }
    }
}
