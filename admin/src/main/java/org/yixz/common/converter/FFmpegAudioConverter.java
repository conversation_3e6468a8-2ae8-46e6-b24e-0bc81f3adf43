package org.yixz.common.converter;

import lombok.extern.slf4j.Slf4j;
import org.springframework.web.multipart.MultipartFile;
import org.yixz.common.exception.AudioConversionException;
import org.yixz.common.util.FileUtil;
import org.yixz.common.util.MediaConverterUtil;

import java.io.BufferedReader;
import java.io.File;
import java.io.InputStreamReader;
import java.util.ArrayList;
import java.util.List;
import java.util.concurrent.TimeUnit;

/**
 * FFmpeg命令行音频转换工具类
 * 作为JAVE2的备选方案，直接调用系统中的FFmpeg命令
 *
 * <AUTHOR>
 * @date 2025-07-22
 */
@Slf4j
public class FFmpegAudioConverter {

    private static final String FFMPEG_COMMAND = "ffmpeg";
    private static final int TIMEOUT_SECONDS = 300; // 5分钟超时

    /**
     * FFmpeg转换结果类
     */
    public static class FFmpegResult {
        private boolean success;
        private File convertedFile;
        private String message;
        private String ffmpegOutput;
        private long conversionTimeMs;

        // Getters and Setters
        public boolean isSuccess() { return success; }
        public void setSuccess(boolean success) { this.success = success; }

        public File getConvertedFile() { return convertedFile; }
        public void setConvertedFile(File convertedFile) { this.convertedFile = convertedFile; }

        public String getMessage() { return message; }
        public void setMessage(String message) { this.message = message; }

        public String getFfmpegOutput() { return ffmpegOutput; }
        public void setFfmpegOutput(String ffmpegOutput) { this.ffmpegOutput = ffmpegOutput; }

        public long getConversionTimeMs() { return conversionTimeMs; }
        public void setConversionTimeMs(long conversionTimeMs) { this.conversionTimeMs = conversionTimeMs; }

        @Override
        public String toString() {
            return String.format("FFmpegResult{success=%s, message='%s', conversionTime=%dms}",
                    success, message, conversionTimeMs);
        }
    }

    /**
     * 检查FFmpeg是否可用
     */
    public static boolean isFFmpegAvailable() {
        return MediaConverterUtil.isFFmpegAvailable();
    }

    /**
     * 使用FFmpeg转换音频为阿里云ASR兼容格式
     */
    public static FFmpegResult convertToAliyunFormat(MultipartFile sourceFile) {
        File tempSourceFile = null;
        File tempTargetFile = null;

        try {
            // 创建临时源文件
            tempSourceFile = FileUtil.createTempFile(sourceFile, "ffmpeg_source_");

            // 检测源文件信息
            AudioFormatDetector.AudioInfo sourceInfo = AudioFormatDetector.detectAudioInfo(tempSourceFile);

            // 如果已经符合要求，直接返回
            if (sourceInfo.isSupported()) {
                FFmpegResult result = new FFmpegResult();
                result.setSuccess(true);
                result.setConvertedFile(tempSourceFile);
                result.setMessage("音频格式已符合要求，无需转换");
                tempSourceFile = null; // 防止被删除
                return result;
            }

            // 创建目标文件
            tempTargetFile = File.createTempFile("ffmpeg_converted_", ".wav");

            // 获取推荐转换参数
            AudioFormatDetector.ConversionParams params = AudioFormatDetector.getRecommendedParams(sourceInfo);

            // 执行FFmpeg转换
            return executeFFmpegConversion(tempSourceFile, tempTargetFile, params);

        } catch (AudioConversionException e) {
            // 音频转换异常直接抛出
            throw e;
        } catch (Exception e) {
            log.error("FFmpeg音频转换失败", e);
            throw new AudioConversionException("FFmpeg音频转换失败: " + e.getMessage(), e);
        } finally {
            // 清理临时源文件
            if (tempSourceFile != null && tempSourceFile.exists()) {
                tempSourceFile.delete();
            }
        }
    }

    /**
     * 使用FFmpeg转换音频为百度ASR兼容格式
     */
    public static FFmpegResult convertToBaiduFormat(MultipartFile sourceFile) {
        File tempSourceFile = null;
        File tempTargetFile = null;

        try {
            // 创建临时源文件
            tempSourceFile = FileUtil.createTempFile(sourceFile, "ffmpeg_source_");

            // 检测源文件信息
            AudioFormatDetector.AudioInfo sourceInfo = AudioFormatDetector.detectAudioInfo(tempSourceFile);

            // 进行百度ASR特定的格式验证
            AudioFormatDetector.validateBaiduAudioFormat(sourceInfo);

            // 如果已经符合要求，直接返回
            if (sourceInfo.isSupported()) {
                FFmpegResult result = new FFmpegResult();
                result.setSuccess(true);
                result.setConvertedFile(tempSourceFile);
                result.setMessage("音频格式已符合百度ASR要求，无需转换");
                tempSourceFile = null; // 防止被删除
                return result;
            }

            // 创建目标文件
            tempTargetFile = File.createTempFile("ffmpeg_converted_", ".wav");

            // 获取百度ASR推荐转换参数
            AudioFormatDetector.ConversionParams params = getBaiduRecommendedParams(sourceInfo);

            // 执行FFmpeg转换
            return executeFFmpegConversion(tempSourceFile, tempTargetFile, params);

        } catch (AudioConversionException e) {
            // 音频转换异常直接抛出
            throw e;
        } catch (Exception e) {
            log.error("FFmpeg百度ASR音频转换失败", e);
            throw new AudioConversionException("FFmpeg百度ASR音频转换失败: " + e.getMessage(), e);
        } finally {
            // 清理临时源文件
            if (tempSourceFile != null && tempSourceFile.exists()) {
                tempSourceFile.delete();
            }
        }
    }

    /**
     * 获取百度ASR推荐的转换参数
     */
    private static AudioFormatDetector.ConversionParams getBaiduRecommendedParams(AudioFormatDetector.AudioInfo sourceInfo) {
        AudioFormatDetector.ConversionParams params = new AudioFormatDetector.ConversionParams();

        // 百度ASR推荐参数：WAV格式，16000Hz采样率，单声道，16位
        params.setTargetFormat("WAV");
        params.setTargetSampleRate(16000);
        params.setTargetChannels(1); // 单声道
        params.setTargetBitRate(256000); // 16位 * 16000Hz * 1声道 = 256kbps

        log.debug("百度ASR推荐转换参数: 格式={}, 采样率={}Hz, 声道={}, 比特率={}",
                params.getTargetFormat(), params.getTargetSampleRate(),
                params.getTargetChannels(), params.getTargetBitRate());

        return params;
    }

    /**
     * 执行FFmpeg转换命令
     */
    private static FFmpegResult executeFFmpegConversion(File sourceFile, File targetFile,
                                                       AudioFormatDetector.ConversionParams params) {
        FFmpegResult result = new FFmpegResult();
        long startTime = System.currentTimeMillis();

        try {
            // 构建FFmpeg命令
            List<String> command = buildFFmpegCommand(sourceFile, targetFile, params);

            log.info("执行FFmpeg命令: {}", String.join(" ", command));

            // 执行命令
            ProcessBuilder pb = new ProcessBuilder(command);
            pb.redirectErrorStream(true);
            Process process = pb.start();

            // 读取输出
            StringBuilder output = new StringBuilder();
            try (BufferedReader reader = new BufferedReader(new InputStreamReader(process.getInputStream()))) {
                String line;
                while ((line = reader.readLine()) != null) {
                    output.append(line).append("\n");
                }
            }

            // 等待完成
            boolean finished = process.waitFor(TIMEOUT_SECONDS, TimeUnit.SECONDS);
            long conversionTime = System.currentTimeMillis() - startTime;
            result.setConversionTimeMs(conversionTime);
            result.setFfmpegOutput(output.toString());

            if (!finished) {
                process.destroyForcibly();
                throw new AudioConversionException("FFmpeg转换超时");
            }

            if (process.exitValue() != 0) {
                log.error("FFmpeg转换失败，输出: {}", output.toString());
                throw new AudioConversionException("FFmpeg转换失败，退出码: " + process.exitValue());
            }

            // 验证转换结果
            if (!targetFile.exists() || targetFile.length() == 0) {
                throw new AudioConversionException("转换后的文件不存在或为空");
            }

            // 检测转换后的文件信息
            AudioFormatDetector.AudioInfo targetInfo = AudioFormatDetector.detectAudioInfo(targetFile);

            if (targetInfo.isSupported()) {
                result.setSuccess(true);
                result.setConvertedFile(targetFile);
                result.setMessage(String.format("FFmpeg转换成功，耗时: %dms", conversionTime));
                log.info("FFmpeg转换成功，耗时: {}ms", conversionTime);
            } else {
                throw new AudioConversionException("转换后的文件仍不符合要求: " + targetInfo.getReason());
            }

        } catch (AudioConversionException e) {
            // 音频转换异常直接抛出
            throw e;
        } catch (Exception e) {
            log.error("FFmpeg转换异常", e);
            throw new AudioConversionException("FFmpeg转换异常: " + e.getMessage(), e);
        }

        return result;
    }

    /**
     * 构建FFmpeg命令
     */
    private static List<String> buildFFmpegCommand(File sourceFile, File targetFile,
                                                  AudioFormatDetector.ConversionParams params) {
        List<String> command = new ArrayList<>();

        command.add(FFMPEG_COMMAND);
        command.add("-i");
        command.add(sourceFile.getAbsolutePath());

        // 覆盖输出文件
        command.add("-y");

        // 音频编码器
        command.add("-acodec");
        command.add("pcm_s16le"); // 16位PCM编码

        // 采样率
        command.add("-ar");
        command.add(String.valueOf(params.getTargetSampleRate()));

        // 声道数
        command.add("-ac");
        command.add(String.valueOf(params.getTargetChannels()));

        // 比特率
        command.add("-ab");
        command.add(params.getTargetBitRate() + "");

        // 输出格式
        command.add("-f");
        command.add(params.getTargetFormat().toLowerCase());

        // 输出文件
        command.add(targetFile.getAbsolutePath());

        return command;
    }

    /**
     * 获取音频文件信息（使用FFprobe）
     */
    public static String getAudioInfo(File audioFile) {
        try {
            List<String> command = new ArrayList<>();
            command.add("ffprobe");
            command.add("-v");
            command.add("quiet");
            command.add("-print_format");
            command.add("json");
            command.add("-show_format");
            command.add("-show_streams");
            command.add(audioFile.getAbsolutePath());

            ProcessBuilder pb = new ProcessBuilder(command);
            Process process = pb.start();

            StringBuilder output = new StringBuilder();
            try (BufferedReader reader = new BufferedReader(new InputStreamReader(process.getInputStream()))) {
                String line;
                while ((line = reader.readLine()) != null) {
                    output.append(line).append("\n");
                }
            }

            boolean finished = process.waitFor(30, TimeUnit.SECONDS);
            if (finished && process.exitValue() == 0) {
                return output.toString();
            } else {
                log.warn("FFprobe获取音频信息失败");
                return null;
            }

        } catch (Exception e) {
            log.error("FFprobe获取音频信息异常", e);
            return null;
        }
    }



    /**
     * 使用FFmpeg从视频文件中分离音频
     *
     * @param videoFile 视频文件
     * @param outputFormat 输出音频格式
     * @return 音频分离结果
     */
    public static MediaSeparationResult separateAudioFromVideo(File videoFile, String outputFormat) {
        File tempSourceFile = null;
        File tempAudioFile = null;

        try {
            long startTime = System.currentTimeMillis();

            // 创建临时源文件
            tempSourceFile = FileUtil.createTempFile(videoFile, "ffmpeg_video_source_");

            // 检测视频信息
            MediaSeparationResult.VideoInfo videoInfo = AudioConverter.detectVideoInfo(tempSourceFile);

            // 创建结果对象
            MediaSeparationResult result = new MediaSeparationResult();
            result.setSourceVideoInfo(videoInfo);
            result.setConverterUsed("FFmpeg");

            // 创建临时音频文件
            tempAudioFile = File.createTempFile("ffmpeg_separated_audio_", "." + outputFormat.toLowerCase());

            // 执行FFmpeg音频分离
            executeFFmpegAudioSeparation(tempSourceFile, tempAudioFile, outputFormat);

            // 检测分离出的音频信息
            AudioFormatDetector.AudioInfo audioInfo = AudioFormatDetector.detectAudioInfo(tempAudioFile);

            long processingTime = System.currentTimeMillis() - startTime;

            // 设置结果
            result.setSuccess(true);
            result.setAudioFile(tempAudioFile);
            result.setAudioInfo(audioInfo);
            result.setProcessingTimeMs(processingTime);
            result.setTempFile(tempAudioFile);
            result.setMessage(String.format("FFmpeg音频分离成功，耗时: %dms", processingTime));

            // 创建音频流
            result.setAudioStream(AudioConverter.convertedFileToInputStream(tempAudioFile));

            log.info("FFmpeg音频分离成功，耗时: {}ms，输出格式: {}", processingTime, outputFormat);

            tempAudioFile = null; // 防止被清理
            return result;

        } catch (Exception e) {
            log.error("FFmpeg音频分离失败", e);
            throw new AudioConversionException("FFmpeg音频分离失败: " + e.getMessage(), e);
        } finally {
            // 清理临时源文件
            FileUtil.cleanupTempFile(tempSourceFile);
            // 如果出现异常，清理音频文件
            FileUtil.cleanupTempFile(tempAudioFile);
        }
    }

    /**
     * 执行FFmpeg音频分离
     */
    private static void executeFFmpegAudioSeparation(File sourceFile, File targetFile, String outputFormat) throws Exception {
        List<String> command = buildFFmpegAudioSeparationCommand(sourceFile, targetFile, outputFormat);

        long startTime = System.currentTimeMillis();

        ProcessBuilder pb = new ProcessBuilder(command);
        pb.redirectErrorStream(true);
        Process process = pb.start();

        // 读取输出（用于调试）
        StringBuilder output = new StringBuilder();
        try (BufferedReader reader = new BufferedReader(new InputStreamReader(process.getInputStream()))) {
            String line;
            while ((line = reader.readLine()) != null) {
                output.append(line).append("\n");
            }
        }

        boolean finished = process.waitFor(60, TimeUnit.SECONDS);

        if (!finished) {
            process.destroyForcibly();
            throw new AudioConversionException("FFmpeg音频分离超时");
        }

        int exitCode = process.exitValue();
        long conversionTime = System.currentTimeMillis() - startTime;

        if (exitCode != 0) {
            log.error("FFmpeg音频分离失败，退出码: {}，输出: {}", exitCode, output.toString());
            throw new AudioConversionException("FFmpeg音频分离失败，退出码: " + exitCode);
        }

        // 检查输出文件是否存在且有内容
        if (!targetFile.exists() || targetFile.length() == 0) {
            throw new AudioConversionException("FFmpeg未能成功分离音频");
        }

        log.debug("FFmpeg音频分离完成，耗时: {}ms", conversionTime);
    }

    /**
     * 构建FFmpeg音频分离命令
     */
    private static List<String> buildFFmpegAudioSeparationCommand(File sourceFile, File targetFile, String outputFormat) {
        List<String> command = new ArrayList<>();

        command.add(FFMPEG_COMMAND);
        command.add("-i");
        command.add(sourceFile.getAbsolutePath());

        // 覆盖输出文件
        command.add("-y");

        // 只提取音频，不包含视频
        command.add("-vn");

        // 根据输出格式设置编码器
        switch (outputFormat.toLowerCase()) {
            case "mp3":
                command.add("-acodec");
                command.add("libmp3lame");
                command.add("-ab");
                command.add("128k");
                break;
            case "aac":
                command.add("-acodec");
                command.add("aac");
                command.add("-ab");
                command.add("128k");
                break;
            case "ogg":
                command.add("-acodec");
                command.add("libvorbis");
                command.add("-ab");
                command.add("128k");
                break;
            case "wav":
            default:
                command.add("-acodec");
                command.add("pcm_s16le");
                command.add("-ar");
                command.add("44100");
                command.add("-ac");
                command.add("2");
                break;
        }

        // 输出文件
        command.add(targetFile.getAbsolutePath());

        return command;
    }
}
