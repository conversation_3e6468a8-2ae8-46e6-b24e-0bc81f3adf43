package org.yixz.common.converter;

/**
 * 字幕信息封装类
 * 用于封装字幕轨道的详细信息
 * 
 * <AUTHOR> Assistant
 * @since 2025-01-22
 */
public class SubtitleInfo {
    
    /**
     * 字幕轨道索引
     */
    private int trackIndex;
    
    /**
     * 字幕语言
     */
    private String language;
    
    /**
     * 字幕格式（srt, vtt, ass, ssa等）
     */
    private String format;
    
    /**
     * 字幕编码
     */
    private String encoding;
    
    /**
     * 字幕标题/描述
     */
    private String title;
    
    /**
     * 是否为默认字幕
     */
    private boolean isDefault;
    
    /**
     * 是否为强制字幕
     */
    private boolean isForced;
    
    /**
     * 字幕条目数量
     */
    private int entryCount;
    
    /**
     * 字幕持续时间（秒）
     */
    private long duration;
    
    // 构造函数
    public SubtitleInfo() {}
    
    public SubtitleInfo(int trackIndex, String language, String format) {
        this.trackIndex = trackIndex;
        this.language = language;
        this.format = format;
    }
    
    // Getter和Setter方法
    public int getTrackIndex() {
        return trackIndex;
    }
    
    public void setTrackIndex(int trackIndex) {
        this.trackIndex = trackIndex;
    }
    
    public String getLanguage() {
        return language;
    }
    
    public void setLanguage(String language) {
        this.language = language;
    }
    
    public String getFormat() {
        return format;
    }
    
    public void setFormat(String format) {
        this.format = format;
    }
    
    public String getEncoding() {
        return encoding;
    }
    
    public void setEncoding(String encoding) {
        this.encoding = encoding;
    }
    
    public String getTitle() {
        return title;
    }
    
    public void setTitle(String title) {
        this.title = title;
    }
    
    public boolean isDefault() {
        return isDefault;
    }
    
    public void setDefault(boolean isDefault) {
        this.isDefault = isDefault;
    }
    
    public boolean isForced() {
        return isForced;
    }
    
    public void setForced(boolean isForced) {
        this.isForced = isForced;
    }
    
    public int getEntryCount() {
        return entryCount;
    }
    
    public void setEntryCount(int entryCount) {
        this.entryCount = entryCount;
    }
    
    public long getDuration() {
        return duration;
    }
    
    public void setDuration(long duration) {
        this.duration = duration;
    }
    
    @Override
    public String toString() {
        return String.format("SubtitleInfo{trackIndex=%d, language='%s', format='%s', title='%s', isDefault=%s, isForced=%s, entryCount=%d}",
                trackIndex, language, format, title, isDefault, isForced, entryCount);
    }
    
    @Override
    public boolean equals(Object obj) {
        if (this == obj) return true;
        if (obj == null || getClass() != obj.getClass()) return false;
        
        SubtitleInfo that = (SubtitleInfo) obj;
        return trackIndex == that.trackIndex &&
               isDefault == that.isDefault &&
               isForced == that.isForced &&
               entryCount == that.entryCount &&
               duration == that.duration &&
               (language != null ? language.equals(that.language) : that.language == null) &&
               (format != null ? format.equals(that.format) : that.format == null) &&
               (encoding != null ? encoding.equals(that.encoding) : that.encoding == null) &&
               (title != null ? title.equals(that.title) : that.title == null);
    }
    
    @Override
    public int hashCode() {
        int result = trackIndex;
        result = 31 * result + (language != null ? language.hashCode() : 0);
        result = 31 * result + (format != null ? format.hashCode() : 0);
        result = 31 * result + (encoding != null ? encoding.hashCode() : 0);
        result = 31 * result + (title != null ? title.hashCode() : 0);
        result = 31 * result + (isDefault ? 1 : 0);
        result = 31 * result + (isForced ? 1 : 0);
        result = 31 * result + entryCount;
        result = 31 * result + (int) (duration ^ (duration >>> 32));
        return result;
    }
}
