package org.yixz.common.converter;

import java.io.File;
import java.io.InputStream;
import java.util.List;
import java.util.ArrayList;

/**
 * 字幕提取结果封装类
 * 用于封装字幕提取操作的结果信息
 * 
 * <AUTHOR> Assistant
 * @since 2025-01-22
 */
public class SubtitleExtractionResult {
    
    /**
     * 操作是否成功
     */
    private boolean success;
    
    /**
     * 结果消息
     */
    private String message;
    
    /**
     * 提取的字幕文件列表
     */
    private List<SubtitleFile> subtitleFiles;
    
    /**
     * 源视频信息
     */
    private MediaSeparationResult.VideoInfo sourceVideoInfo;
    
    /**
     * 处理耗时（毫秒）
     */
    private long processingTimeMs;
    
    /**
     * 使用的提取器类型
     */
    private String extractorUsed;
    
    /**
     * 临时文件列表（需要清理）
     */
    private List<File> tempFiles;
    
    // 构造函数
    public SubtitleExtractionResult() {
        this.success = false;
        this.subtitleFiles = new ArrayList<>();
        this.tempFiles = new ArrayList<>();
    }
    
    // Getter和Setter方法
    public boolean isSuccess() {
        return success;
    }
    
    public void setSuccess(boolean success) {
        this.success = success;
    }
    
    public String getMessage() {
        return message;
    }
    
    public void setMessage(String message) {
        this.message = message;
    }
    
    public List<SubtitleFile> getSubtitleFiles() {
        return subtitleFiles;
    }
    
    public void setSubtitleFiles(List<SubtitleFile> subtitleFiles) {
        this.subtitleFiles = subtitleFiles;
    }
    
    public void addSubtitleFile(SubtitleFile subtitleFile) {
        if (this.subtitleFiles == null) {
            this.subtitleFiles = new ArrayList<>();
        }
        this.subtitleFiles.add(subtitleFile);
    }
    
    public MediaSeparationResult.VideoInfo getSourceVideoInfo() {
        return sourceVideoInfo;
    }
    
    public void setSourceVideoInfo(MediaSeparationResult.VideoInfo sourceVideoInfo) {
        this.sourceVideoInfo = sourceVideoInfo;
    }
    
    public long getProcessingTimeMs() {
        return processingTimeMs;
    }
    
    public void setProcessingTimeMs(long processingTimeMs) {
        this.processingTimeMs = processingTimeMs;
    }
    
    public String getExtractorUsed() {
        return extractorUsed;
    }
    
    public void setExtractorUsed(String extractorUsed) {
        this.extractorUsed = extractorUsed;
    }
    
    public List<File> getTempFiles() {
        return tempFiles;
    }
    
    public void setTempFiles(List<File> tempFiles) {
        this.tempFiles = tempFiles;
    }
    
    public void addTempFile(File tempFile) {
        if (this.tempFiles == null) {
            this.tempFiles = new ArrayList<>();
        }
        this.tempFiles.add(tempFile);
    }
    
    /**
     * 字幕文件内部类
     */
    public static class SubtitleFile {
        private SubtitleInfo subtitleInfo;
        private File file;
        private InputStream stream;
        private String content; // 字幕文本内容
        
        public SubtitleFile() {}
        
        public SubtitleFile(SubtitleInfo subtitleInfo, File file) {
            this.subtitleInfo = subtitleInfo;
            this.file = file;
        }
        
        // Getter和Setter方法
        public SubtitleInfo getSubtitleInfo() {
            return subtitleInfo;
        }
        
        public void setSubtitleInfo(SubtitleInfo subtitleInfo) {
            this.subtitleInfo = subtitleInfo;
        }
        
        public File getFile() {
            return file;
        }
        
        public void setFile(File file) {
            this.file = file;
        }
        
        public InputStream getStream() {
            return stream;
        }
        
        public void setStream(InputStream stream) {
            this.stream = stream;
        }
        
        public String getContent() {
            return content;
        }
        
        public void setContent(String content) {
            this.content = content;
        }
        
        @Override
        public String toString() {
            return String.format("SubtitleFile{info=%s, file='%s', hasContent=%s}",
                    subtitleInfo, file != null ? file.getName() : "null", content != null);
        }
    }
    
    /**
     * 获取指定轨道的字幕文件
     */
    public SubtitleFile getSubtitleFile(int trackIndex) {
        if (subtitleFiles == null) {
            return null;
        }
        
        return subtitleFiles.stream()
                .filter(sf -> sf.getSubtitleInfo() != null && sf.getSubtitleInfo().getTrackIndex() == trackIndex)
                .findFirst()
                .orElse(null);
    }
    
    /**
     * 获取默认字幕文件
     */
    public SubtitleFile getDefaultSubtitleFile() {
        if (subtitleFiles == null || subtitleFiles.isEmpty()) {
            return null;
        }
        
        // 查找默认字幕
        SubtitleFile defaultSubtitle = subtitleFiles.stream()
                .filter(sf -> sf.getSubtitleInfo() != null && sf.getSubtitleInfo().isDefault())
                .findFirst()
                .orElse(null);
        
        // 如果没有默认字幕，返回第一个
        return defaultSubtitle != null ? defaultSubtitle : subtitleFiles.get(0);
    }
    
    /**
     * 获取字幕轨道数量
     */
    public int getSubtitleTrackCount() {
        return subtitleFiles != null ? subtitleFiles.size() : 0;
    }
    
    @Override
    public String toString() {
        return String.format("SubtitleExtractionResult{success=%s, message='%s', trackCount=%d, processingTimeMs=%d, extractorUsed='%s'}",
                success, message, getSubtitleTrackCount(), processingTimeMs, extractorUsed);
    }
}
