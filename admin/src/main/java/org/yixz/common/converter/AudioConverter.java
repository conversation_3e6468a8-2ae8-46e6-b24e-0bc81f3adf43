package org.yixz.common.converter;

import lombok.extern.slf4j.Slf4j;
import org.springframework.web.multipart.MultipartFile;
import org.yixz.common.exception.AudioConversionException;
import org.yixz.common.util.FileUtil;
import ws.schild.jave.Encoder;
import ws.schild.jave.EncoderException;
import ws.schild.jave.MultimediaObject;
import ws.schild.jave.encode.AudioAttributes;
import ws.schild.jave.encode.EncodingAttributes;
import ws.schild.jave.info.MultimediaInfo;

import java.io.File;
import java.io.FileInputStream;
import java.io.IOException;
import java.io.InputStream;
import java.nio.file.Files;

/**
 * 音频转换工具类
 * 基于JAVE2库实现音频格式转换，支持转换为阿里云ASR兼容格式
 *
 * <AUTHOR>
 * @date 2025-07-22
 */
@Slf4j
public class AudioConverter {

    /**
     * 转换结果类
     */
    public static class ConversionResult {
        private boolean success;
        private File convertedFile;
        private String message;
        private AudioFormatDetector.AudioInfo sourceInfo;
        private AudioFormatDetector.AudioInfo targetInfo;
        private long conversionTimeMs;

        // Getters and Setters
        public boolean isSuccess() { return success; }
        public void setSuccess(boolean success) { this.success = success; }
        
        public File getConvertedFile() { return convertedFile; }
        public void setConvertedFile(File convertedFile) { this.convertedFile = convertedFile; }
        
        public String getMessage() { return message; }
        public void setMessage(String message) { this.message = message; }
        
        public AudioFormatDetector.AudioInfo getSourceInfo() { return sourceInfo; }
        public void setSourceInfo(AudioFormatDetector.AudioInfo sourceInfo) { this.sourceInfo = sourceInfo; }
        
        public AudioFormatDetector.AudioInfo getTargetInfo() { return targetInfo; }
        public void setTargetInfo(AudioFormatDetector.AudioInfo targetInfo) { this.targetInfo = targetInfo; }
        
        public long getConversionTimeMs() { return conversionTimeMs; }
        public void setConversionTimeMs(long conversionTimeMs) { this.conversionTimeMs = conversionTimeMs; }

        @Override
        public String toString() {
            return String.format("ConversionResult{success=%s, message='%s', conversionTime=%dms}",
                    success, message, conversionTimeMs);
        }
    }

    /**
     * 转换MultipartFile为阿里云ASR兼容格式
     */
    public static ConversionResult convertToAliyunFormat(MultipartFile sourceFile) {
        File tempSourceFile = null;
        File tempTargetFile = null;
        
        try {
            // 创建临时源文件
            tempSourceFile = FileUtil.createTempFile(sourceFile, "source_");
            
            // 检测源文件信息
            AudioFormatDetector.AudioInfo sourceInfo = AudioFormatDetector.detectAudioInfo(tempSourceFile);

            ConversionResult result = new ConversionResult();
            result.setSourceInfo(sourceInfo);

            // 如果已经符合要求，直接返回
            if (sourceInfo.isSupported()) {
                result.setSuccess(true);
                result.setConvertedFile(tempSourceFile);
                result.setMessage("音频格式已符合要求，无需转换");
                result.setTargetInfo(sourceInfo);
                tempSourceFile = null; // 防止被删除
                return result;
            }

            // 如果检测失败，抛出转换异常
            if (!sourceInfo.isSupported() && sourceInfo.getReason() != null &&
                sourceInfo.getReason().contains("读取音频文件失败")) {
                throw new AudioConversionException("音频文件格式检测失败: " + sourceInfo.getReason());
            }
            
            // 获取推荐转换参数
            AudioFormatDetector.ConversionParams params = AudioFormatDetector.getRecommendedParams(sourceInfo);
            
            // 执行转换
            tempTargetFile = createTempTargetFile(params.getTargetFormat());
            return convertAudio(tempSourceFile, tempTargetFile, params, sourceInfo);
            
        } catch (AudioConversionException e) {
            // 音频转换异常直接抛出
            throw e;
        } catch (Exception e) {
            log.error("音频转换失败", e);
            throw new AudioConversionException("音频转换失败: " + e.getMessage(), e);
        } finally {
            // 清理临时源文件
            if (tempSourceFile != null && tempSourceFile.exists()) {
                tempSourceFile.delete();
            }
        }
    }

    /**
     * 转换MultipartFile为百度ASR兼容格式
     */
    public static ConversionResult convertToBaiduFormat(MultipartFile sourceFile) {
        File tempSourceFile = null;
        File tempTargetFile = null;

        try {
            // 创建临时源文件
            tempSourceFile = FileUtil.createTempFile(sourceFile, "source_");

            // 检测源文件信息
            AudioFormatDetector.AudioInfo sourceInfo = AudioFormatDetector.detectAudioInfo(tempSourceFile);

            ConversionResult result = new ConversionResult();
            result.setSourceInfo(sourceInfo);

            // 进行百度ASR特定的格式验证
            AudioFormatDetector.validateBaiduAudioFormat(sourceInfo);

            // 如果已经符合要求，直接返回
            if (sourceInfo.isSupported()) {
                result.setSuccess(true);
                result.setConvertedFile(tempSourceFile);
                result.setMessage("音频格式已符合百度ASR要求，无需转换");
                result.setTargetInfo(sourceInfo);
                tempSourceFile = null; // 防止被删除
                return result;
            }

            // 如果检测失败，抛出转换异常
            if (!sourceInfo.isSupported() && sourceInfo.getReason() != null &&
                sourceInfo.getReason().contains("读取音频文件失败")) {
                throw new AudioConversionException("音频文件格式检测失败: " + sourceInfo.getReason());
            }

            // 获取百度ASR推荐转换参数
            AudioFormatDetector.ConversionParams params = getBaiduRecommendedParams(sourceInfo);

            // 执行转换
            tempTargetFile = createTempTargetFile(params.getTargetFormat());
            return convertAudio(tempSourceFile, tempTargetFile, params, sourceInfo);

        } catch (AudioConversionException e) {
            // 音频转换异常直接抛出
            throw e;
        } catch (Exception e) {
            log.error("百度ASR音频转换失败", e);
            throw new AudioConversionException("百度ASR音频转换失败: " + e.getMessage(), e);
        } finally {
            // 清理临时源文件
            if (tempSourceFile != null && tempSourceFile.exists()) {
                tempSourceFile.delete();
            }
        }
    }

    /**
     * 获取百度ASR推荐的转换参数
     */
    private static AudioFormatDetector.ConversionParams getBaiduRecommendedParams(AudioFormatDetector.AudioInfo sourceInfo) {
        AudioFormatDetector.ConversionParams params = new AudioFormatDetector.ConversionParams();

        // 百度ASR推荐参数：WAV格式，16000Hz采样率，单声道，16位
        params.setTargetFormat("WAV");
        params.setTargetSampleRate(16000);
        params.setTargetChannels(1); // 单声道
        params.setTargetBitRate(256000); // 16位 * 16000Hz * 1声道 = 256kbps

        log.debug("百度ASR推荐转换参数: 格式={}, 采样率={}Hz, 声道={}, 比特率={}",
                params.getTargetFormat(), params.getTargetSampleRate(),
                params.getTargetChannels(), params.getTargetBitRate());

        return params;
    }

    /**
     * 转换音频文件
     */
    public static ConversionResult convertAudio(File sourceFile, File targetFile, 
                                              AudioFormatDetector.ConversionParams params,
                                              AudioFormatDetector.AudioInfo sourceInfo) {
        ConversionResult result = new ConversionResult();
        result.setSourceInfo(sourceInfo);
        
        long startTime = System.currentTimeMillis();
        
        try {
            // 创建编码器
            Encoder encoder = new Encoder();
            
            // 设置音频属性
            AudioAttributes audioAttributes = new AudioAttributes();
            audioAttributes.setCodec("pcm_s16le"); // 16位PCM编码
            audioAttributes.setBitRate(params.getTargetBitRate());
            audioAttributes.setSamplingRate(params.getTargetSampleRate());
            audioAttributes.setChannels(params.getTargetChannels());
            
            // 设置编码属性
            EncodingAttributes encodingAttributes = new EncodingAttributes();
            encodingAttributes.setOutputFormat(params.getTargetFormat().toLowerCase());
            encodingAttributes.setAudioAttributes(audioAttributes);
            
            // 执行转换
            MultimediaObject source = new MultimediaObject(sourceFile);
            encoder.encode(source, targetFile, encodingAttributes);
            
            long conversionTime = System.currentTimeMillis() - startTime;
            result.setConversionTimeMs(conversionTime);
            
            // 验证转换结果
            AudioFormatDetector.AudioInfo targetInfo = AudioFormatDetector.detectAudioInfo(targetFile);
            result.setTargetInfo(targetInfo);
            
            if (targetInfo.isSupported()) {
                result.setSuccess(true);
                result.setConvertedFile(targetFile);
                result.setMessage(String.format("转换成功: %s -> %s, 耗时: %dms",
                        sourceInfo.getFormat(), targetInfo.getFormat(), conversionTime));
                log.debug("音频转换成功: {} -> {}, 耗时: {}ms",
                        sourceInfo.getFormat(), targetInfo.getFormat(), conversionTime);
            } else {
                log.error("转换后文件仍不符合要求: {}", targetInfo.getReason());
                throw new AudioConversionException("转换后的文件仍不符合要求: " + targetInfo.getReason());
            }

        } catch (EncoderException e) {
            log.error("音频编码失败: {}", e.getMessage());
            throw new AudioConversionException("音频编码失败: " + e.getMessage(), e);
        } catch (Exception e) {
            log.error("音频转换异常: {}", e.getMessage());
            throw new AudioConversionException("音频转换异常: " + e.getMessage(), e);
        }
        
        return result;
    }

    /**
     * 批量转换音频文件
     */
    public static ConversionResult[] convertMultipleFiles(MultipartFile[] sourceFiles) {
        ConversionResult[] results = new ConversionResult[sourceFiles.length];
        
        for (int i = 0; i < sourceFiles.length; i++) {
            results[i] = convertToAliyunFormat(sourceFiles[i]);
        }
        
        return results;
    }

    /**
     * 创建临时目标文件
     */
    private static File createTempTargetFile(String format) throws IOException {
        String extension = "." + format.toLowerCase();
        return File.createTempFile("converted_", extension);
    }

    /**
     * 清理临时文件
     */
    public static void cleanupTempFile(File file) {
        if (file != null && file.exists()) {
            try {
                Files.delete(file.toPath());
                log.debug("临时文件已清理: {}", file.getAbsolutePath());
            } catch (IOException e) {
                log.warn("清理临时文件失败: {}", file.getAbsolutePath(), e);
            }
        }
    }

    /**
     * 将转换后的文件转换为InputStream
     */
    public static InputStream convertedFileToInputStream(File convertedFile) throws IOException {
        return new FileInputStream(convertedFile);
    }

    /**
     * 估算转换时间（基于文件大小，单位：毫秒）
     */
    public static long estimateConversionTime(long fileSizeBytes) {
        // 粗略估算：每MB大约需要1-3秒转换时间
        long fileSizeMB = fileSizeBytes / (1024 * 1024);
        return Math.max(1000, fileSizeMB * 2000); // 最少1秒，每MB约2秒
    }

    /**
     * 从视频文件中分离音频（File版本）
     *
     * @param videoFile 视频文件
     * @return 音频分离结果
     * @throws AudioConversionException 音频转换异常
     */
    public static MediaSeparationResult separateAudioFromVideo(File videoFile) throws AudioConversionException {
        return separateAudioFromVideo(videoFile, "wav", 0);
    }

    /**
     * 从视频文件中分离音频（File版本，指定格式）
     *
     * @param videoFile 视频文件
     * @param outputFormat 输出音频格式（wav, mp3, aac等）
     * @return 音频分离结果
     * @throws AudioConversionException 音频转换异常
     */
    public static MediaSeparationResult separateAudioFromVideo(File videoFile, String outputFormat) throws AudioConversionException {
        return separateAudioFromVideo(videoFile, outputFormat, 0);
    }

    /**
     * 从视频文件中提取指定音频轨道（File版本）
     *
     * @param videoFile 视频文件
     * @param outputFormat 输出音频格式
     * @param audioTrackIndex 音频轨道索引（0为第一个轨道）
     * @return 音频分离结果
     * @throws AudioConversionException 音频转换异常
     */
    public static MediaSeparationResult separateAudioFromVideo(File videoFile, String outputFormat, int audioTrackIndex) throws AudioConversionException {
        if (videoFile == null || !videoFile.exists()) {
            throw new AudioConversionException("视频文件不存在或为空");
        }

        File tempAudioFile = null;

        try {
            long startTime = System.currentTimeMillis();

            // 检测视频信息
            MediaSeparationResult.VideoInfo videoInfo = detectVideoInfo(videoFile);

            // 创建结果对象
            MediaSeparationResult result = new MediaSeparationResult();
            result.setSourceVideoInfo(videoInfo);
            result.setConverterUsed("JAVE2");

            // 验证音频轨道索引
            if (audioTrackIndex >= videoInfo.getAudioTracks()) {
                throw new AudioConversionException(String.format("音频轨道索引 %d 超出范围，视频只有 %d 个音频轨道",
                        audioTrackIndex, videoInfo.getAudioTracks()));
            }

            // 创建临时音频文件
            tempAudioFile = createTempTargetFile(outputFormat);

            // 执行音频分离
            extractAudioTrack(videoFile, tempAudioFile, outputFormat, audioTrackIndex);

            // 检测分离出的音频信息
            AudioFormatDetector.AudioInfo audioInfo = AudioFormatDetector.detectAudioInfo(tempAudioFile);

            long processingTime = System.currentTimeMillis() - startTime;

            // 设置结果
            result.setSuccess(true);
            result.setAudioFile(tempAudioFile);
            result.setAudioInfo(audioInfo);
            result.setProcessingTimeMs(processingTime);
            result.setTempFile(tempAudioFile);
            result.setMessage(String.format("音频分离成功，耗时: %dms", processingTime));

            // 创建音频流
            result.setAudioStream(convertedFileToInputStream(tempAudioFile));

            log.info("音频分离成功，耗时: {}ms，输出格式: {}", processingTime, outputFormat);

            tempAudioFile = null; // 防止被清理
            return result;

        } catch (AudioConversionException e) {
            throw e;
        } catch (Exception e) {
            log.error("音频分离失败", e);
            throw new AudioConversionException("音频分离失败: " + e.getMessage(), e);
        } finally {
            // 如果出现异常，清理音频文件
            if (tempAudioFile != null && tempAudioFile.exists()) {
                tempAudioFile.delete();
            }
        }
    }

    /**
     * 检测视频文件信息
     *
     * @param videoFile 视频文件
     * @return 视频信息
     * @throws Exception 检测异常
     */
    public static MediaSeparationResult.VideoInfo detectVideoInfo(File videoFile) throws Exception {
        MediaSeparationResult.VideoInfo videoInfo = new MediaSeparationResult.VideoInfo();

        try {
            MultimediaObject source = new MultimediaObject(videoFile);
            MultimediaInfo info = source.getInfo();

            // 设置基本信息
            videoInfo.setDuration(info.getDuration() / 1000); // 转换为秒

            // 获取视频信息
            if (info.getVideo() != null) {
                videoInfo.setWidth(info.getVideo().getSize().getWidth());
                videoInfo.setHeight(info.getVideo().getSize().getHeight());
                videoInfo.setVideoCodec(info.getVideo().getDecoder());
            }

            // 获取音频信息
            if (info.getAudio() != null) {
                videoInfo.setAudioCodec(info.getAudio().getDecoder());
                videoInfo.setAudioTracks(1); // JAVE2默认只能检测到一个音频轨道
            } else {
                videoInfo.setAudioTracks(0);
            }

            // 检测文件格式
            String fileName = videoFile.getName().toLowerCase();
            if (fileName.endsWith(".mp4")) {
                videoInfo.setFormat("MP4");
            } else if (fileName.endsWith(".avi")) {
                videoInfo.setFormat("AVI");
            } else if (fileName.endsWith(".mov")) {
                videoInfo.setFormat("MOV");
            } else if (fileName.endsWith(".mkv")) {
                videoInfo.setFormat("MKV");
            } else {
                videoInfo.setFormat("UNKNOWN");
            }

            // 简单检测是否可能包含字幕（基于文件格式）
            videoInfo.setHasSubtitles(fileName.endsWith(".mkv") || fileName.endsWith(".mp4"));

            log.debug("检测到视频信息: {}", videoInfo);

        } catch (Exception e) {
            log.error("检测视频信息失败", e);
            throw new Exception("检测视频信息失败: " + e.getMessage(), e);
        }

        return videoInfo;
    }

    /**
     * 提取音频轨道
     *
     * @param sourceFile 源视频文件
     * @param targetFile 目标音频文件
     * @param outputFormat 输出格式
     * @param trackIndex 音频轨道索引
     * @throws Exception 提取异常
     */
    private static void extractAudioTrack(File sourceFile, File targetFile, String outputFormat, int trackIndex) throws Exception {
        try {
            // 创建编码器
            Encoder encoder = new Encoder();

            // 设置音频属性
            AudioAttributes audioAttributes = new AudioAttributes();

            // 根据输出格式设置编码器
            switch (outputFormat.toLowerCase()) {
                case "mp3":
                    audioAttributes.setCodec("libmp3lame");
                    audioAttributes.setBitRate(128000);
                    break;
                case "aac":
                    audioAttributes.setCodec("aac");
                    audioAttributes.setBitRate(128000);
                    break;
                case "ogg":
                    audioAttributes.setCodec("libvorbis");
                    audioAttributes.setBitRate(128000);
                    break;
                case "wav":
                default:
                    audioAttributes.setCodec("pcm_s16le");
                    audioAttributes.setBitRate(1411200); // 16位44.1kHz立体声
                    break;
            }

            audioAttributes.setChannels(2); // 立体声
            audioAttributes.setSamplingRate(44100); // 44.1kHz

            // 设置编码属性
            EncodingAttributes encodingAttributes = new EncodingAttributes();
            encodingAttributes.setOutputFormat(outputFormat.toLowerCase());
            encodingAttributes.setAudioAttributes(audioAttributes);

            // 只提取音频，不包含视频
            encodingAttributes.setVideoAttributes(null);

            // 执行转换
            MultimediaObject source = new MultimediaObject(sourceFile);
            encoder.encode(source, targetFile, encodingAttributes);

            log.debug("音频轨道提取完成: {} -> {}", sourceFile.getName(), targetFile.getName());

        } catch (Exception e) {
            log.error("提取音频轨道失败", e);
            throw new Exception("提取音频轨道失败: " + e.getMessage(), e);
        }
    }
}
