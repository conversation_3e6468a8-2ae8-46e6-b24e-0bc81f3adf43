package org.yixz.common.converter;

import java.io.File;
import java.io.InputStream;

/**
 * 媒体分离结果封装类
 * 用于封装音频分离操作的结果信息
 * 
 * <AUTHOR> Assistant
 * @since 2025-01-22
 */
public class MediaSeparationResult {
    
    /**
     * 操作是否成功
     */
    private boolean success;
    
    /**
     * 结果消息
     */
    private String message;
    
    /**
     * 分离出的音频文件
     */
    private File audioFile;
    
    /**
     * 音频文件输入流
     */
    private InputStream audioStream;
    
    /**
     * 源视频信息
     */
    private VideoInfo sourceVideoInfo;
    
    /**
     * 分离出的音频信息
     */
    private AudioFormatDetector.AudioInfo audioInfo;
    
    /**
     * 处理耗时（毫秒）
     */
    private long processingTimeMs;
    
    /**
     * 使用的转换器类型
     */
    private String converterUsed;
    
    /**
     * 临时文件（需要清理）
     */
    private File tempFile;
    
    // 构造函数
    public MediaSeparationResult() {
        this.success = false;
    }
    
    // Getter和Setter方法
    public boolean isSuccess() {
        return success;
    }
    
    public void setSuccess(boolean success) {
        this.success = success;
    }
    
    public String getMessage() {
        return message;
    }
    
    public void setMessage(String message) {
        this.message = message;
    }
    
    public File getAudioFile() {
        return audioFile;
    }
    
    public void setAudioFile(File audioFile) {
        this.audioFile = audioFile;
    }
    
    public InputStream getAudioStream() {
        return audioStream;
    }
    
    public void setAudioStream(InputStream audioStream) {
        this.audioStream = audioStream;
    }
    
    public VideoInfo getSourceVideoInfo() {
        return sourceVideoInfo;
    }
    
    public void setSourceVideoInfo(VideoInfo sourceVideoInfo) {
        this.sourceVideoInfo = sourceVideoInfo;
    }
    
    public AudioFormatDetector.AudioInfo getAudioInfo() {
        return audioInfo;
    }
    
    public void setAudioInfo(AudioFormatDetector.AudioInfo audioInfo) {
        this.audioInfo = audioInfo;
    }
    
    public long getProcessingTimeMs() {
        return processingTimeMs;
    }
    
    public void setProcessingTimeMs(long processingTimeMs) {
        this.processingTimeMs = processingTimeMs;
    }
    
    public String getConverterUsed() {
        return converterUsed;
    }
    
    public void setConverterUsed(String converterUsed) {
        this.converterUsed = converterUsed;
    }
    
    public File getTempFile() {
        return tempFile;
    }
    
    public void setTempFile(File tempFile) {
        this.tempFile = tempFile;
    }
    
    /**
     * 视频信息内部类
     */
    public static class VideoInfo {
        private String format;
        private long duration; // 持续时间（秒）
        private int width;
        private int height;
        private String videoCodec;
        private String audioCodec;
        private int audioTracks; // 音频轨道数量
        private boolean hasSubtitles; // 是否包含字幕
        
        // 构造函数
        public VideoInfo() {}
        
        // Getter和Setter方法
        public String getFormat() {
            return format;
        }
        
        public void setFormat(String format) {
            this.format = format;
        }
        
        public long getDuration() {
            return duration;
        }
        
        public void setDuration(long duration) {
            this.duration = duration;
        }
        
        public int getWidth() {
            return width;
        }
        
        public void setWidth(int width) {
            this.width = width;
        }
        
        public int getHeight() {
            return height;
        }
        
        public void setHeight(int height) {
            this.height = height;
        }
        
        public String getVideoCodec() {
            return videoCodec;
        }
        
        public void setVideoCodec(String videoCodec) {
            this.videoCodec = videoCodec;
        }
        
        public String getAudioCodec() {
            return audioCodec;
        }
        
        public void setAudioCodec(String audioCodec) {
            this.audioCodec = audioCodec;
        }
        
        public int getAudioTracks() {
            return audioTracks;
        }
        
        public void setAudioTracks(int audioTracks) {
            this.audioTracks = audioTracks;
        }
        
        public boolean isHasSubtitles() {
            return hasSubtitles;
        }
        
        public void setHasSubtitles(boolean hasSubtitles) {
            this.hasSubtitles = hasSubtitles;
        }
        
        @Override
        public String toString() {
            return String.format("VideoInfo{format='%s', duration=%d, resolution=%dx%d, videoCodec='%s', audioCodec='%s', audioTracks=%d, hasSubtitles=%s}",
                    format, duration, width, height, videoCodec, audioCodec, audioTracks, hasSubtitles);
        }
    }
    
    @Override
    public String toString() {
        return String.format("MediaSeparationResult{success=%s, message='%s', processingTimeMs=%d, converterUsed='%s'}",
                success, message, processingTimeMs, converterUsed);
    }
}
