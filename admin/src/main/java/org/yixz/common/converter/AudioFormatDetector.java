package org.yixz.common.converter;

import lombok.extern.slf4j.Slf4j;
import org.jaudiotagger.audio.AudioFile;
import org.jaudiotagger.audio.AudioFileIO;
import org.jaudiotagger.audio.AudioHeader;
import org.springframework.web.multipart.MultipartFile;
import org.yixz.common.enums.AiProviderEnum;

import java.io.File;
import java.io.FileOutputStream;
import java.io.IOException;
import java.io.InputStream;
import java.util.Arrays;
import java.util.HashSet;
import java.util.Set;

/**
 * 音频格式检测工具类
 * 用于检测音频文件的格式、采样率、声道数等信息
 *
 * <AUTHOR>
 * @date 2025-07-22
 */
@Slf4j
public class AudioFormatDetector {

    /**
     * 阿里云ASR支持的音频格式
     */
    public static final Set<String> ALIYUN_SUPPORTED_FORMATS = new HashSet<>(Arrays.asList(
            "PCM", "WAV", "OGG", "AMR", "MP3", "AAC", "OPUS", "SPEEX"
    ));

    /**
     * 阿里云ASR支持的采样率
     */
    public static final Set<Integer> ALIYUN_SUPPORTED_SAMPLE_RATES = new HashSet<>(Arrays.asList(
            8000, 16000
    ));

    /**
     * 百度ASR支持的音频格式
     */
    public static final Set<String> BAIDU_SUPPORTED_FORMATS = new HashSet<>(Arrays.asList(
            "PCM", "WAV", "AMR", "M4A"
    ));

    /**
     * 百度ASR支持的采样率
     */
    public static final Set<Integer> BAIDU_SUPPORTED_SAMPLE_RATES = new HashSet<>(Arrays.asList(
            8000, 16000
    ));

    /**
     * 音频信息类
     */
    public static class AudioInfo {
        private String format;
        private int sampleRate;
        private String channels;
        private long bitRate;
        private long duration; // 毫秒
        private boolean isSupported;
        private String reason;

        // Getters and Setters
        public String getFormat() { return format; }
        public void setFormat(String format) { this.format = format; }
        
        public int getSampleRate() { return sampleRate; }
        public void setSampleRate(int sampleRate) { this.sampleRate = sampleRate; }
        
        public String getChannels() { return channels; }
        public void setChannels(String channels) { this.channels = channels; }
        
        public long getBitRate() { return bitRate; }
        public void setBitRate(long bitRate) { this.bitRate = bitRate; }
        
        public long getDuration() { return duration; }
        public void setDuration(long duration) { this.duration = duration; }
        
        public boolean isSupported() { return isSupported; }
        public void setSupported(boolean supported) { isSupported = supported; }
        
        public String getReason() { return reason; }
        public void setReason(String reason) { this.reason = reason; }

        @Override
        public String toString() {
            return String.format("AudioInfo{format='%s', sampleRate=%d, channels=%s, bitRate=%d, duration=%d, isSupported=%s, reason='%s'}",
                    format, sampleRate, channels, bitRate, duration, isSupported, reason);
        }
    }

    /**
     * 检测MultipartFile音频信息
     */
    public static AudioInfo detectAudioInfo(MultipartFile file) {
        File tempFile = null;
        try {
            // 创建临时文件
            tempFile = createTempFile(file);
            return detectAudioInfo(tempFile);
        } catch (Exception e) {
            log.error("检测音频信息失败", e);
            AudioInfo info = new AudioInfo();
            info.setSupported(false);
            info.setReason("检测音频信息失败: " + e.getMessage());
            return info;
        } finally {
            // 清理临时文件
            if (tempFile != null && tempFile.exists()) {
                tempFile.delete();
            }
        }
    }

    /**
     * 检测文件音频信息
     */
    public static AudioInfo detectAudioInfo(File file) {
        AudioInfo info = new AudioInfo();

        // 检查是否为PCM文件
        if (isPcmFile(file)) {
            return createPcmAudioInfo(file);
        }

        try {
            AudioFile audioFile = AudioFileIO.read(file);
            AudioHeader header = audioFile.getAudioHeader();

            // 获取基本信息
            info.setFormat(header.getFormat().toUpperCase());
            info.setSampleRate(header.getSampleRateAsNumber());
            info.setChannels(String.valueOf(header.getChannels()));
            info.setBitRate(header.getBitRateAsNumber());
            info.setDuration(header.getTrackLength() * 1000L); // 转换为毫秒

            // 注意：这里不进行厂商特定的验证，只检测基本信息
            // 厂商特定的验证应该在各自的转换服务中调用相应的验证方法
            info.setSupported(true); // 默认设置为支持，具体验证由调用方决定
            info.setReason("音频信息检测成功");

            log.debug("音频信息检测完成: {}", info);
            return info;

        } catch (Exception e) {
            log.error("读取音频文件失败: {}", file.getAbsolutePath(), e);
            info.setSupported(false);
            info.setReason("读取音频文件失败: " + e.getMessage());
            return info;
        }
    }

    /**
     * 验证音频格式是否符合阿里云ASR要求
     */
    public static void validateAliyunAudioFormat(AudioInfo info) {
        StringBuilder reasons = new StringBuilder();
        boolean isSupported = true;

        // 检查格式 - 需要从详细格式描述中提取基本格式
        String basicFormat = extractBasicFormat(info.getFormat(), AiProviderEnum.ALIYUN);
        if (!ALIYUN_SUPPORTED_FORMATS.contains(basicFormat)) {
            isSupported = false;
            reasons.append("不支持的音频格式: ").append(info.getFormat()).append(" (基本格式: ").append(basicFormat).append("); ");
        }

        // 检查采样率
        if (!ALIYUN_SUPPORTED_SAMPLE_RATES.contains(info.getSampleRate())) {
            isSupported = false;
            reasons.append("不支持的采样率: ").append(info.getSampleRate()).append("Hz; ");
        }

        // 检查声道数（必须是单声道）
        if (!isMonoChannel(info.getChannels())) {
            isSupported = false;
            reasons.append("不支持的声道数: ").append(info.getChannels()).append("(需要单声道); ");
        }

        info.setSupported(isSupported);
        if (!isSupported) {
            info.setReason(reasons.toString());
        } else {
            info.setReason("格式符合要求");
        }
    }

    /**
     * 验证音频格式是否符合百度ASR要求
     */
    public static void validateBaiduAudioFormat(AudioInfo info) {
        StringBuilder reasons = new StringBuilder();
        boolean isSupported = true;

        // 检查格式 - 需要从详细格式描述中提取基本格式
        String basicFormat = extractBasicFormat(info.getFormat(), AiProviderEnum.BAIDU);
        if (!BAIDU_SUPPORTED_FORMATS.contains(basicFormat)) {
            isSupported = false;
            reasons.append("不支持的音频格式: ").append(info.getFormat()).append(" (基本格式: ").append(basicFormat).append("); ");
        }

        // 检查采样率
        if (!BAIDU_SUPPORTED_SAMPLE_RATES.contains(info.getSampleRate())) {
            isSupported = false;
            reasons.append("不支持的采样率: ").append(info.getSampleRate()).append("Hz; ");
        }

        // 检查声道数（必须是单声道）
        if (!isMonoChannel(info.getChannels())) {
            isSupported = false;
            reasons.append("不支持的声道数: ").append(info.getChannels()).append("(需要单声道); ");
        }

        info.setSupported(isSupported);
        if (!isSupported) {
            info.setReason(reasons.toString());
        } else {
            info.setReason("格式符合百度ASR要求");
        }
    }

    /**
     * 通用的格式提取方法，支持不同服务商的格式映射规则
     *
     * @param detailedFormat 详细格式描述
     * @param provider 服务提供商
     * @return 基本格式
     */
    public static String extractBasicFormat(String detailedFormat, AiProviderEnum provider) {
        if (detailedFormat == null || detailedFormat.trim().isEmpty()) {
            return "UNKNOWN";
        }

        String upperFormat = detailedFormat.toUpperCase().trim();

        // WAV格式的各种表示（所有服务商都支持）
        if (upperFormat.contains("WAV") || upperFormat.contains("RIFF")) {
            return "WAV";
        }

        // PCM格式（所有服务商都支持）
        if (upperFormat.contains("PCM") || upperFormat.contains("LINEAR")) {
            return "PCM";
        }

        // AMR格式（所有服务商都支持）
        if (upperFormat.contains("AMR")) {
            return "AMR";
        }

        // 根据服务商处理不同的格式映射
        return switch (provider) {
            case ALIYUN -> extractFormatForAliyun(upperFormat);
            case BAIDU -> extractFormatForBaidu(upperFormat);
            default -> extractFormatGeneric(upperFormat);
        };
    }

    /**
     * 阿里云特定的格式提取
     */
    private static String extractFormatForAliyun(String upperFormat) {
        // MP3格式的各种表示
        if (upperFormat.contains("MP3") || upperFormat.contains("MPEG AUDIO LAYER 3") ||
            upperFormat.contains("MPEG-1 LAYER 3") || upperFormat.contains("MPEG-2 LAYER 3")) {
            return "MP3";
        }

        // AAC格式的各种表示
        if (upperFormat.contains("AAC") || upperFormat.contains("M4A") ||
            upperFormat.contains("MP4") || upperFormat.contains("MPEG-4")) {
            return "AAC";
        }

        // OGG格式的各种表示
        if (upperFormat.contains("OGG") || upperFormat.contains("VORBIS") ||
            upperFormat.contains("OPUS") || upperFormat.contains("SPEEX")) {
            return "OGG";
        }

        // FLAC格式
        if (upperFormat.contains("FLAC")) {
            return "FLAC";
        }

        return extractFormatGeneric(upperFormat);
    }

    /**
     * 百度ASR特定的格式提取
     */
    private static String extractFormatForBaidu(String upperFormat) {
        // M4A/AAC格式的各种表示（百度ASR中映射为M4A）
        if (upperFormat.contains("M4A") || upperFormat.contains("AAC") ||
            upperFormat.contains("MP4") || upperFormat.contains("MPEG-4")) {
            return "M4A";
        }

        return extractFormatGeneric(upperFormat);
    }

    /**
     * 通用格式提取（兜底逻辑）
     */
    private static String extractFormatGeneric(String upperFormat) {
        // 如果没有匹配到，尝试提取第一个单词作为格式
        String[] parts = upperFormat.split("[\\s\\-_]+");
        if (parts.length > 0 && !parts[0].isEmpty()) {
            return parts[0];
        }

        return "UNKNOWN";
    }

    /**
     * 检查是否为单声道
     * 支持多种单声道的表示方式
     */
    private static boolean isMonoChannel(String channels) {
        if (channels == null || channels.trim().isEmpty()) {
            return false;
        }

        String upperChannels = channels.toUpperCase().trim();

        // 数字1表示单声道
        if ("1".equals(upperChannels)) {
            return true;
        }

        // 英文单声道表示
        if ("MONO".equals(upperChannels) || "MONAURAL".equals(upperChannels)) {
            return true;
        }

        // 其他可能的表示方式
        if ("SINGLE".equals(upperChannels) || "1 CHANNEL".equals(upperChannels) ||
            "1CH".equals(upperChannels) || "1-CHANNEL".equals(upperChannels)) {
            return true;
        }

        return false;
    }

    /**
     * 根据文件扩展名判断音频格式
     */
    public static String getFormatByExtension(String filename) {
        if (filename == null || !filename.contains(".")) {
            return "UNKNOWN";
        }
        
        String extension = filename.substring(filename.lastIndexOf(".") + 1).toUpperCase();

        return switch (extension) {
            case "WAV" -> "WAV";
            case "MP3" -> "MP3";
            case "AAC", "M4A" -> "AAC";
            case "OGG" -> "OGG";
            case "AMR" -> "AMR";
            case "OPUS" -> "OPUS";
            case "SPEEX" -> "SPEEX";
            default -> extension;
        };
    }

    /**
     * 检查是否需要转换
     */
    public static boolean needsConversion(AudioInfo info) {
        return !info.isSupported();
    }

    /**
     * 获取推荐的转换参数
     */
    public static ConversionParams getRecommendedParams(AudioInfo sourceInfo) {
        ConversionParams params = new ConversionParams();
        
        // 默认转换为16kHz单声道PCM WAV格式
        params.setTargetFormat("WAV");
        params.setTargetSampleRate(16000);
        params.setTargetChannels(1);
        params.setTargetBitRate(256000); // 256kbps
        
        // 如果源文件采样率是8kHz，保持8kHz
        if (sourceInfo.getSampleRate() == 8000) {
            params.setTargetSampleRate(8000);
        }
        
        return params;
    }

    /**
     * 转换参数类
     */
    public static class ConversionParams {
        private String targetFormat = "WAV";
        private int targetSampleRate = 16000;
        private int targetChannels = 1;
        private int targetBitRate = 256000;

        // Getters and Setters
        public String getTargetFormat() { return targetFormat; }
        public void setTargetFormat(String targetFormat) { this.targetFormat = targetFormat; }
        
        public int getTargetSampleRate() { return targetSampleRate; }
        public void setTargetSampleRate(int targetSampleRate) { this.targetSampleRate = targetSampleRate; }
        
        public int getTargetChannels() { return targetChannels; }
        public void setTargetChannels(int targetChannels) { this.targetChannels = targetChannels; }
        
        public int getTargetBitRate() { return targetBitRate; }
        public void setTargetBitRate(int targetBitRate) { this.targetBitRate = targetBitRate; }

        @Override
        public String toString() {
            return String.format("ConversionParams{format='%s', sampleRate=%d, channels=%d, bitRate=%d}",
                    targetFormat, targetSampleRate, targetChannels, targetBitRate);
        }
    }

    /**
     * 创建临时文件
     */
    private static File createTempFile(MultipartFile multipartFile) throws IOException {
        String originalFilename = multipartFile.getOriginalFilename();
        String extension = "";
        if (originalFilename != null && originalFilename.contains(".")) {
            extension = originalFilename.substring(originalFilename.lastIndexOf("."));
        }
        
        File tempFile = File.createTempFile("audio_detect_", extension);
        
        try (InputStream inputStream = multipartFile.getInputStream();
             FileOutputStream outputStream = new FileOutputStream(tempFile)) {
            
            byte[] buffer = new byte[8192];
            int bytesRead;
            while ((bytesRead = inputStream.read(buffer)) != -1) {
                outputStream.write(buffer, 0, bytesRead);
            }
        }

        return tempFile;
    }

    /**
     * 检查文件是否为PCM格式
     */
    private static boolean isPcmFile(File file) {
        if (file == null || !file.exists()) {
            return false;
        }

        String fileName = file.getName().toLowerCase();
        return fileName.endsWith(".pcm");
    }

    /**
     * 为PCM文件创建默认的音频信息
     * 由于PCM是原始音频数据，没有元数据头，我们使用常见的语音识别参数作为默认值
     */
    private static AudioInfo createPcmAudioInfo(File file) {
        AudioInfo info = new AudioInfo();

        try {
            // PCM文件的默认参数（适用于语音识别场景）
            info.setFormat("PCM");
            info.setSampleRate(16000); // 16kHz采样率
            info.setChannels("1"); // 单声道
            info.setBitRate(256000); // 16位 * 16000Hz * 1声道 = 256kbps

            // 尝试根据文件大小估算时长（假设16位单声道16kHz）
            long fileSize = file.length();
            long bytesPerSecond = 16000 * 2; // 16kHz * 2字节(16位)
            long durationMs = (fileSize * 1000L) / bytesPerSecond;
            info.setDuration(durationMs);

            info.setSupported(true);
            info.setReason("PCM文件使用默认参数：16kHz单声道16位");

            log.info("检测到PCM文件: {}, 使用默认参数 - 采样率: {}Hz, 声道: {}, 预估时长: {}ms",
                    file.getName(), info.getSampleRate(), info.getChannels(), info.getDuration());

        } catch (Exception e) {
            log.error("处理PCM文件失败: {}", file.getAbsolutePath(), e);
            info.setSupported(false);
            info.setReason("PCM文件处理失败: " + e.getMessage());
        }

        return info;
    }
}
