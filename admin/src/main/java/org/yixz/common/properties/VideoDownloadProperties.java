package org.yixz.common.properties;

import lombok.Data;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.stereotype.Component;

/**
 * 视频下载配置
 *
 * <AUTHOR>
 * @date 2025-07-26
 */
@Data
@Component
@ConfigurationProperties(prefix = "video.download")
public class VideoDownloadProperties {
    
    /**
     * 默认下载器
     */
    private String defaultDownloader = "yt-dlp";
    
    /**
     * 是否启用平台特定下载器
     */
    private boolean enablePlatformSpecific = true;
    
    /**
     * 是否启用降级机制（平台特定失败时使用yt-dlp）
     */
    private boolean enableFallback = true;
    
    /**
     * 下载超时时间（毫秒）
     */
    private long downloadTimeoutMs = 300000; // 5分钟
    
    /**
     * 连接超时时间（毫秒）
     */
    private long connectTimeoutMs = 30000; // 30秒
    
    /**
     * 最大重试次数
     */
    private int maxRetryAttempts = 3;
    
    /**
     * 重试延迟时间（毫秒）
     */
    private long retryDelayMs = 2000;
    
    /**
     * 临时文件目录
     */
    private String tempDir = "/tmp/video_download";
    
    /**
     * 是否保留临时文件（调试用）
     */
    private boolean keepTempFiles = false;

    /**
     * 视频保存目录
     */
    private String saveDir = "/data/video";
    
    /**
     * yt-dlp配置
     */
    private YtDlpConfig ytDlp = new YtDlpConfig();
    
    /**
     * 抖音配置
     */
    private DouyinConfig douyin = new DouyinConfig();
    
    /**
     * 快手配置
     */
    private KuaishouConfig kuaishou = new KuaishouConfig();
    
    /**
     * B站配置
     */
    private BilibiliConfig bilibili = new BilibiliConfig();
    
    /**
     * yt-dlp配置类
     */
    @Data
    public static class YtDlpConfig {
        private String executablePath = "yt-dlp";
        private String defaultFormat = "best[height<=720]";
        private boolean extractAudio = false;
        private String audioFormat = "mp3";
        private boolean writeSubtitles = false;
        private boolean writeAutoSubtitles = false;
        private String outputTemplate = "%(title)s.%(ext)s";
    }
    
    /**
     * 抖音配置类
     */
    @Data
    public static class DouyinConfig {
        private boolean enabled = true;
        private String userAgent = "Mozilla/5.0 (iPhone; CPU iPhone OS 14_0 like Mac OS X)";
        private int maxRetries = 3;
    }
    
    /**
     * 快手配置类
     */
    @Data
    public static class KuaishouConfig {
        private boolean enabled = true;
        private String userAgent = "Mozilla/5.0 (iPhone; CPU iPhone OS 14_0 like Mac OS X)";
        private int maxRetries = 3;
    }
    
    /**
     * B站配置类
     */
    @Data
    public static class BilibiliConfig {
        private boolean enabled = true;
        private String userAgent = "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36";
        private int maxRetries = 3;
        private boolean requireLogin = false;
        private String sessdata = "";
        private String biliJct = "";
    }
}
