package org.yixz.common.properties;

import lombok.Data;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.stereotype.Component;

/**
 * AI服务配置
 *
 * <AUTHOR>
 * @date 2025-07-20
 */
@Data
@Component
@ConfigurationProperties(prefix = "ai")
public class AiServiceProperties {
    
    /**
     * 默认服务提供商
     */
    private String provider = "aliyun";
    
    /**
     * 阿里云配置
     */
    private AliyunConfig aliyun = new AliyunConfig();
    
    /**
     * 百度云配置
     */
    private BaiduConfig baidu = new BaiduConfig();
    
    @Data
    public static class AliyunConfig {
        private String accessKeyId;
        private String accessKeySecret;
        private AsrConfig asr = new AsrConfig();
        private OcrConfig ocr = new OcrConfig();

        @Data
        public static class AsrConfig {
            private String endpoint = "https://nls-meta.cn-shanghai.aliyuncs.com";
            private String appKey;
            // 音频参数配置
            private int defaultSampleRate = 16000;
            private String defaultFormat = "PCM";
            private boolean enableIntermediateResult = true;
            private boolean enableVoiceDetection = true;
            private boolean enablePunctuation = true;
            private boolean enableITN = false;
            // 超时配置
            private int connectTimeoutMs = 10000;
            private int readTimeoutMs = 30000;
            private int maxWaitTimeMs = 5000;
            // 重试配置
            private int maxRetryAttempts = 3;
            private int retryDelayMs = 1000;
        }

        @Data
        public static class OcrConfig {
            private String endpoint = "https://ocr-api.cn-hangzhou.aliyuncs.com";
            // 超时配置
            private int connectTimeoutMs = 10000;
            private int readTimeoutMs = 30000;
            // 重试配置
            private int maxRetryAttempts = 3;
            private int retryDelayMs = 1000;
        }
    }
    
    @Data
    public static class BaiduConfig {
        private String apiKey;
        private String secretKey;
        private String appId;
        private AsrConfig asr = new AsrConfig();
        private OcrConfig ocr = new OcrConfig();

        @Data
        public static class AsrConfig {
            private String endpoint = "https://vop.baidu.com/server_api";
            private String realtimeAsr = "https://vop.baidu.com/server_api";
        }

        @Data
        public static class OcrConfig {
            private String endpoint = "https://aip.baidubce.com/rest/2.0/ocr";
        }
    }
}
