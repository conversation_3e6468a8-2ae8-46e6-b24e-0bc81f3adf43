package org.yixz.common.manager;

import com.alibaba.nls.client.protocol.NlsClient;
import jakarta.annotation.PreDestroy;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import org.yixz.common.properties.AiServiceProperties;

import java.util.concurrent.locks.ReentrantReadWriteLock;

/**
 * 阿里云NlsClient管理器
 * 负责NlsClient的创建、缓存和生命周期管理
 *
 * <AUTHOR>
 * @date 2025-07-21
 */
@Slf4j
@Component
public class AliyunNlsClientManager {

    @Autowired
    private AiServiceProperties aiServiceProperties;

    @Autowired
    private AliyunTokenManager tokenManager;

    private volatile NlsClient client;
    private volatile String currentToken;
    private final ReentrantReadWriteLock lock = new ReentrantReadWriteLock();

    /**
     * 获取有效的NlsClient
     * 如果client不存在或token已更新，会重新创建client
     */
    public NlsClient getClient() {
        String validToken = tokenManager.getValidToken();
        
        // 读锁检查client是否有效
        lock.readLock().lock();
        try {
            if (isClientValid(validToken)) {
                return client;
            }
        } finally {
            lock.readLock().unlock();
        }

        // 写锁创建或重建client
        lock.writeLock().lock();
        try {
            // 双重检查
            if (!isClientValid(validToken)) {
                createClient(validToken);
            }
            return client;
        } finally {
            lock.writeLock().unlock();
        }
    }

    /**
     * 检查client是否有效
     */
    private boolean isClientValid(String token) {
        return client != null && token.equals(currentToken);
    }

    /**
     * 创建新的NlsClient
     */
    private void createClient(String token) {
        try {
            // 关闭旧的client
            if (client != null) {
                try {
                    client.shutdown();
                    log.info("旧的NlsClient已关闭");
                } catch (Exception e) {
                    log.warn("关闭旧NlsClient时发生异常", e);
                }
            }

            // 创建新的client
            AiServiceProperties.AliyunConfig config = aiServiceProperties.getAliyun();
            long startTime = System.currentTimeMillis();
            
            this.client = new NlsClient(config.getAsr().getEndpoint(), token);
            this.currentToken = token;
            
            long costTime = System.currentTimeMillis() - startTime;
            log.info("NlsClient创建成功，耗时: {}ms, endpoint: {}", costTime, config.getAsr().getEndpoint());
            
        } catch (Exception e) {
            log.error("创建NlsClient失败", e);
            throw new RuntimeException("创建阿里云NlsClient失败", e);
        }
    }

    /**
     * 强制重建client
     */
    public void forceRebuild() {
        lock.writeLock().lock();
        try {
            String validToken = tokenManager.getValidToken();
            createClient(validToken);
        } finally {
            lock.writeLock().unlock();
        }
    }

    /**
     * 应用关闭时清理资源
     */
    @PreDestroy
    public void destroy() {
        lock.writeLock().lock();
        try {
            if (client != null) {
                client.shutdown();
                client = null;
                currentToken = null;
                log.info("NlsClient资源已清理");
            }
        } catch (Exception e) {
            log.error("清理NlsClient资源时发生异常", e);
        } finally {
            lock.writeLock().unlock();
        }
    }

    /**
     * 检查client是否可用
     */
    public boolean isAvailable() {
        lock.readLock().lock();
        try {
            return client != null && currentToken != null;
        } finally {
            lock.readLock().unlock();
        }
    }
}
