package org.yixz.common.manager;

import com.alibaba.nls.client.AccessToken;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import org.yixz.common.properties.AiServiceProperties;

import java.io.IOException;
import java.util.concurrent.locks.ReentrantReadWriteLock;

/**
 * 阿里云AccessToken管理器
 * 负责token的获取、缓存和自动刷新
 *
 * <AUTHOR>
 * @date 2025-07-21
 */
@Slf4j
@Component
public class AliyunTokenManager {

    @Autowired
    private AiServiceProperties aiServiceProperties;

    private volatile String token;
    private volatile long expireTime;
    private final ReentrantReadWriteLock lock = new ReentrantReadWriteLock();

    /**
     * 获取有效的AccessToken
     * 如果token不存在或即将过期，会自动刷新
     */
    public String getValidToken() {
        // 读锁检查token是否有效
        lock.readLock().lock();
        try {
            if (isTokenValid()) {
                return token;
            }
        } finally {
            lock.readLock().unlock();
        }

        // 写锁刷新token
        lock.writeLock().lock();
        try {
            // 双重检查，避免重复刷新
            if (!isTokenValid()) {
                refreshToken();
            }
            return token;
        } finally {
            lock.writeLock().unlock();
        }
    }

    /**
     * 检查token是否有效
     * 提前5分钟刷新token以避免过期
     */
    private boolean isTokenValid() {
        return token != null && System.currentTimeMillis() < (expireTime - 300000);
    }

    /**
     * 刷新AccessToken
     */
    private void refreshToken() {
        try {
            AiServiceProperties.AliyunConfig config = aiServiceProperties.getAliyun();
            AccessToken accessToken = new AccessToken(config.getAccessKeyId(), config.getAccessKeySecret());
            
            long startTime = System.currentTimeMillis();
            accessToken.apply();
            long costTime = System.currentTimeMillis() - startTime;
            
            this.token = accessToken.getToken();
            this.expireTime = accessToken.getExpireTime();
            
            log.info("阿里云AccessToken刷新成功，耗时: {}ms, 过期时间: {}", costTime, expireTime);
            
        } catch (IOException e) {
            log.error("阿里云AccessToken刷新失败", e);
            throw new RuntimeException("获取阿里云AccessToken失败", e);
        }
    }

    /**
     * 强制刷新token
     */
    public void forceRefresh() {
        lock.writeLock().lock();
        try {
            refreshToken();
        } finally {
            lock.writeLock().unlock();
        }
    }

    /**
     * 清除缓存的token
     */
    public void clearToken() {
        lock.writeLock().lock();
        try {
            this.token = null;
            this.expireTime = 0;
            log.info("阿里云AccessToken缓存已清除");
        } finally {
            lock.writeLock().unlock();
        }
    }

    /**
     * 获取token剩余有效时间（秒）
     */
    public long getTokenRemainingTime() {
        lock.readLock().lock();
        try {
            if (token == null) {
                return 0;
            }
            return Math.max(0, (expireTime - System.currentTimeMillis()) / 1000);
        } finally {
            lock.readLock().unlock();
        }
    }
}
