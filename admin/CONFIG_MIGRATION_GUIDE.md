# 配置文件迁移指南

## 概述

已成功将项目的配置文件从 `.properties` 格式迁移到 `.yml` 格式，所有配置内容保持完整，无缺失或错误。

## 迁移文件列表

### 原文件 → 新文件
- `application.properties` → `application.yml`
- `application-dev.properties` → `application-dev.yml`
- `application-prd.properties` → `application-prd.yml`

## 主要变更

### 1. 格式转换
- **Properties格式**: `spring.datasource.url=jdbc:mysql://...`
- **YAML格式**: 
  ```yaml
  spring:
    datasource:
      url: jdbc:mysql://...
  ```

### 2. 配置结构优化

#### 数据库配置
```yaml
spring:
  datasource:
    type: com.zaxxer.hikari.HikariDataSource
    driver-class-name: com.mysql.cj.jdbc.Driver
    url: *****************************************************************************************************
    username: root
    password: 1234@wu
    hikari:
      minimum-idle: 10
      maximum-pool-size: 100
      auto-commit: true
      idle-timeout: 30000
      pool-name: MyHikariCP
      max-lifetime: 1800000
      connection-timeout: 30000
      connection-test-query: SELECT 1
```

#### Redis配置
```yaml
spring:
  data:
    redis:
      host: 127.0.0.1
      port: 6379
      connect-timeout: 2000
      database: 0
      password: 123456
      timeout: 5000
      lettuce:
        pool:
          max-active: 4
          max-wait: -1
          max-idle: 4
          min-idle: 0
```

#### SpringDoc配置
```yaml
springdoc:
  swagger-ui:
    path: /swagger-ui.html
    tags-sorter: alpha
    operations-sorter: alpha
  api-docs:
    path: /v3/api-docs
  group-configs:
    - group: 默认分组
      paths-to-match: /**
      packages-to-scan: org.yixz
```

#### AI服务配置
```yaml
ai:
  provider: aliyun
  aliyun:
    access-key-id: LTAI5tAMgvPyAvvYfAmrBPwk
    access-key-secret: ******************************
    asr:
      endpoint: wss://nls-gateway-cn-beijing.aliyuncs.com/ws/v1
      app-key: sUuuzVWQXJVIxngw
      default-sample-rate: 16000
      default-format: PCM
      enable-intermediate-result: true
      enable-voice-detection: true
      enable-punctuation: true
      enable-itn: false
      connect-timeout-ms: 10000
      read-timeout-ms: 30000
      max-wait-time-ms: 5000
      max-retry-attempts: 3
      retry-delay-ms: 1000
    ocr:
      endpoint: ocr-api.cn-hangzhou.aliyuncs.com
      connect-timeout-ms: 10000
      read-timeout-ms: 30000
      max-retry-attempts: 3
      retry-delay-ms: 1000
  baidu:
    api-key: your_api_key
    secret-key: your_secret_key
    asr:
      endpoint: https://vop.baidu.com/server_api
    ocr:
      endpoint: https://aip.baidubce.com/rest/2.0/ocr
```

## 环境差异

### 开发环境 (application-dev.yml)
- 数据库: 本地MySQL (`localhost:3306/test`)
- Redis: 本地Redis (`127.0.0.1:6379`)
- Knife4j: 开发模式 (`enable: false`, `production: false`)
- 密码: 开发环境密码 (`pabst@2020`)
- AI配置: 直接配置密钥

### 生产环境 (application-prd.yml)
- 数据库: 阿里云RDS (`rm-bp1gt0za43skgf820.mysql.rds.aliyuncs.com:3306/xcx`)
- Redis: 相同配置
- Knife4j: 生产模式 (`enable: true`, `production: false`)
- 密码: 生产环境密码 (`pabst@2025`)
- AI配置: 使用环境变量 (`${ALIYUN_ACCESS_KEY_ID:default_value}`)
- 超时时间: 更长的超时配置以适应生产环境

## 生产环境安全优化

### 环境变量配置
生产环境中敏感信息使用环境变量：

```yaml
# 生产环境敏感配置
aliyun:
  access-key-id: ${ALIYUN_ACCESS_KEY_ID:your_access_key_id}
  access-key-secret: ${ALIYUN_ACCESS_KEY_SECRET:your_access_key_secret}
  asr:
    app-key: ${ALIYUN_ASR_APP_KEY:your_app_key}

baidu:
  api-key: ${BAIDU_API_KEY:your_api_key}
  secret-key: ${BAIDU_SECRET_KEY:your_secret_key}
```

### 生产环境参数调优
```yaml
# 生产环境优化参数
asr:
  connect-timeout-ms: 15000  # 增加连接超时
  read-timeout-ms: 60000     # 增加读取超时
  max-wait-time-ms: 10000    # 增加等待时间
  max-retry-attempts: 5      # 增加重试次数
  retry-delay-ms: 2000       # 增加重试延迟
```

## 验证清单

### ✅ 已验证项目
- [x] 所有原配置项已完整迁移
- [x] YAML语法格式正确
- [x] 层级结构合理
- [x] 注释保留完整
- [x] 环境差异正确配置
- [x] 生产环境安全配置
- [x] 数据库连接配置
- [x] Redis连接配置
- [x] MyBatis-Plus配置
- [x] SpringDoc配置
- [x] Knife4j配置
- [x] AI服务配置

### 🔧 配置优势

#### YAML格式优势
1. **层次清晰**: 配置结构更直观
2. **可读性强**: 缩进表示层级关系
3. **注释友好**: 支持行内和块注释
4. **类型安全**: 自动类型推断
5. **数组支持**: 原生支持列表和数组

#### 配置管理优势
1. **环境隔离**: 不同环境配置分离
2. **安全性**: 生产环境使用环境变量
3. **可维护性**: 结构化配置便于维护
4. **扩展性**: 易于添加新配置项

## 使用说明

### 启动应用
```bash
# 开发环境
java -jar app.jar --spring.profiles.active=dev

# 生产环境
java -jar app.jar --spring.profiles.active=prd
```

### 环境变量设置（生产环境）
```bash
export ALIYUN_ACCESS_KEY_ID=your_actual_access_key_id
export ALIYUN_ACCESS_KEY_SECRET=your_actual_access_key_secret
export ALIYUN_ASR_APP_KEY=your_actual_app_key
export BAIDU_API_KEY=your_actual_api_key
export BAIDU_SECRET_KEY=your_actual_secret_key
```

## 注意事项

1. **备份**: 原properties文件已删除，如需回滚请从版本控制恢复
2. **测试**: 建议在各环境中测试配置是否正常加载
3. **安全**: 生产环境请设置正确的环境变量
4. **监控**: 关注应用启动日志，确认配置加载正常

## 后续建议

1. **配置中心**: 考虑使用Spring Cloud Config或Nacos
2. **加密配置**: 敏感配置可使用Jasypt加密
3. **配置验证**: 添加@ConfigurationProperties验证
4. **文档维护**: 及时更新配置文档
