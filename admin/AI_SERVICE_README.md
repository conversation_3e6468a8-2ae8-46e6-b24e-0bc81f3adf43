# AI服务使用说明

## 功能概述

本项目实现了语音转文字(ASR)和图片转文字(OCR)功能，支持多厂商切换，目前支持阿里云和百度云。

## 架构设计

### 设计模式
- **策略模式**: 支持多厂商的灵活切换
- **工厂模式**: 根据配置动态创建对应厂商的服务实例
- **配置化管理**: 通过配置文件控制使用哪个厂商

### 项目结构
```
org.yixz.service.ai/
├── AsrService.java                    # ASR服务接口
├── OcrService.java                    # OCR服务接口
├── factory/
│   ├── AsrServiceFactory.java         # ASR服务工厂
│   └── OcrServiceFactory.java         # OCR服务工厂
├── impl/
│   ├── aliyun/
│   │   ├── AliyunAsrServiceImpl.java  # 阿里云ASR实现
│   │   └── AliyunOcrServiceImpl.java  # 阿里云OCR实现
│   └── baidu/
│       ├── BaiduAsrServiceImpl.java   # 百度云ASR实现
│       └── BaiduOcrServiceImpl.java   # 百度云OCR实现
├── config/
│   └── AiServiceConfig.java           # AI服务配置
└── enums/
    └── AiProviderEnum.java            # 厂商枚举
```

## 配置说明

在 `application-dev.properties` 中添加以下配置：

```properties
# AI服务配置
ai.provider=aliyun

# 阿里云配置
ai.aliyun.access-key-id=your_access_key_id
ai.aliyun.access-key-secret=your_access_key_secret
ai.aliyun.asr.endpoint=https://nls-meta.cn-shanghai.aliyuncs.com
ai.aliyun.ocr.endpoint=https://ocr-api.cn-hangzhou.aliyuncs.com

# 百度云配置
ai.baidu.api-key=your_api_key
ai.baidu.secret-key=your_secret_key
ai.baidu.asr.endpoint=https://vop.baidu.com/server_api
ai.baidu.ocr.endpoint=https://aip.baidubce.com/rest/2.0/ocr
```

## API接口

### 1. 语音转文字 (ASR)

**接口地址**: `POST /ai/asr`

**请求参数**:
- `audioFile`: 音频文件 (必填)
- `format`: 音频格式 (默认: wav)
- `sampleRate`: 采样率 (默认: 16000)
- `language`: 语言类型 (默认: zh)
- `enablePunctuation`: 是否开启标点符号 (默认: true)
- `enableDigitNormalization`: 是否开启数字转换 (默认: true)
- `provider`: 服务提供商 (可选，不填使用默认配置)

**响应示例**:
```json
{
  "code": "SUCCESS",
  "msg": "操作成功",
  "data": {
    "text": "识别出的文字内容",
    "confidence": 0.95,
    "duration": 10.5,
    "processTime": 1500,
    "provider": "aliyun",
    "segments": [
      {
        "text": "片段文字",
        "startTime": 0.0,
        "endTime": 5.0,
        "confidence": 0.95
      }
    ]
  }
}
```

### 2. 图片转文字 (OCR)

**接口地址**: `POST /ai/ocr`

**请求参数**:
- `imageFile`: 图片文件 (必填)
- `ocrType`: 识别类型 (默认: general)
- `detectOrientation`: 是否检测图片方向 (默认: true)
- `returnPosition`: 是否返回文字位置信息 (默认: false)
- `language`: 语言类型 (默认: zh)
- `provider`: 服务提供商 (可选，不填使用默认配置)

**响应示例**:
```json
{
  "code": "SUCCESS",
  "msg": "操作成功",
  "data": {
    "text": "识别出的文字内容",
    "confidence": 0.95,
    "processTime": 800,
    "provider": "aliyun",
    "orientation": 0,
    "words": [
      {
        "text": "单词",
        "confidence": 0.95,
        "position": {
          "left": 100,
          "top": 50,
          "width": 80,
          "height": 30
        }
      }
    ]
  }
}
```

### 3. 获取服务状态

**接口地址**: `GET /ai/status`

**响应示例**:
```json
{
  "code": "SUCCESS",
  "msg": "操作成功",
  "data": {
    "asrProvider": "aliyun",
    "asrAvailable": true,
    "ocrProvider": "aliyun",
    "ocrAvailable": true,
    "supportedAudioFormats": ["wav", "mp3", "flac", "aac", "m4a", "ogg"],
    "supportedImageFormats": ["jpg", "jpeg", "png", "bmp", "gif", "tiff", "webp"]
  }
}
```

## 支持的文件格式

### 音频格式
- WAV, MP3, FLAC, AAC, M4A, OGG
- 文件大小限制: 50MB

### 图片格式
- JPG, JPEG, PNG, BMP, GIF, TIFF, WEBP
- 文件大小限制: 10MB

## 扩展新厂商

1. 在 `AiProviderEnum` 中添加新的厂商枚举
2. 在 `AiServiceConfig` 中添加新厂商的配置类
3. 实现 `AsrService` 和 `OcrService` 接口
4. 在对应的工厂类中添加新厂商的创建逻辑

## 注意事项

1. 请确保配置了正确的厂商API密钥
2. 生产环境建议使用官方SDK而不是简化的HTTP调用
3. 建议添加重试机制和熔断器
4. 注意API调用频率限制
5. 敏感数据请做好加密处理
