# PCM文件支持实现说明

## 问题描述

在使用JAudioTagger库解析PCM文件时，会出现以下错误：
```
org.jaudiotagger.audio.exceptions.CannotReadException: No Reader associated to this extension: pcm
```

## 根本原因

JAudioTagger库设计用于读取带有元数据头的音频容器格式（如WAV、MP3、FLAC等），而PCM是原始音频数据格式，不包含采样率、声道数、位深度等元数据信息，因此JAudioTagger无法直接解析。

## 解决方案

在`AudioFormatDetector.detectAudioInfo()`方法中添加PCM文件特殊处理逻辑：

### 1. 修改的文件
- `admin/src/main/java/org/yixz/common/converter/AudioFormatDetector.java`

### 2. 实现细节

#### 2.1 PCM文件检测
```java
private static boolean isPcmFile(File file) {
    if (file == null || !file.exists()) {
        return false;
    }
    String fileName = file.getName().toLowerCase();
    return fileName.endsWith(".pcm");
}
```

#### 2.2 PCM默认参数设置
```java
private static AudioInfo createPcmAudioInfo(File file) {
    AudioInfo info = new AudioInfo();
    
    // PCM文件的默认参数（适用于语音识别场景）
    info.setFormat("PCM");
    info.setSampleRate(16000); // 16kHz采样率
    info.setChannels("1"); // 单声道
    info.setBitRate(256000); // 16位 * 16000Hz * 1声道 = 256kbps
    
    // 根据文件大小估算时长（假设16位单声道16kHz）
    long fileSize = file.length();
    long bytesPerSecond = 16000 * 2; // 16kHz * 2字节(16位)
    long durationMs = (fileSize * 1000L) / bytesPerSecond;
    info.setDuration(durationMs);
    
    info.setSupported(true);
    info.setReason("PCM文件使用默认参数：16kHz单声道16位");
    
    return info;
}
```

#### 2.3 主检测方法修改
在`detectAudioInfo()`方法开始处添加PCM检测：
```java
public static AudioInfo detectAudioInfo(File file) {
    AudioInfo info = new AudioInfo();
    
    // 检查是否为PCM文件
    if (isPcmFile(file)) {
        return createPcmAudioInfo(file);
    }
    
    // 原有的JAudioTagger处理逻辑...
}
```

## 默认参数说明

### 选择16kHz单声道16位的原因：
1. **16kHz采样率**：语音识别的标准采样率，既保证音质又控制文件大小
2. **单声道**：语音识别通常不需要立体声，单声道可减少文件大小
3. **16位深度**：提供足够的动态范围，是音频处理的标准位深度

### 时长计算公式：
```
时长(秒) = 文件大小(字节) / (采样率 × 位深度/8 × 声道数)
时长(毫秒) = 时长(秒) × 1000
```

## 兼容性

### 向后兼容性
- 对现有的非PCM音频文件处理逻辑无任何影响
- 保持原有的JAudioTagger处理流程不变
- 异常处理机制保持一致

### 扩展性
- 如果需要支持其他参数的PCM文件，可以通过文件名约定来识别
- 例如：`audio_8k_mono.pcm`（8kHz单声道）、`audio_44k_stereo.pcm`（44.1kHz立体声）

## 测试

创建了专门的测试类`AudioFormatDetectorPcmTest`，包含以下测试用例：
1. PCM文件正确检测和参数设置
2. 非PCM文件不受影响
3. 空文件和不存在文件的处理
4. 时长计算准确性验证

## 使用示例

```java
File pcmFile = new File("audio.pcm");
AudioFormatDetector.AudioInfo info = AudioFormatDetector.detectAudioInfo(pcmFile);

if (info.isSupported()) {
    System.out.println("格式: " + info.getFormat()); // PCM
    System.out.println("采样率: " + info.getSampleRate()); // 16000
    System.out.println("声道: " + info.getChannels()); // 1
    System.out.println("时长: " + info.getDuration() + "ms");
}
```

## 日志输出

当处理PCM文件时，会输出以下信息日志：
```
检测到PCM文件: audio.pcm, 使用默认参数 - 采样率: 16000Hz, 声道: 1, 预估时长: 5000ms
```

这有助于调试和监控PCM文件的处理情况。
